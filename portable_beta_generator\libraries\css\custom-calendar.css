/**
 * Custom Calendar Styles
 * 
 * Styles for the custom calendar implementation.
 * 
 * Version: 1.0.0
 */

/* Calendar Container */
.custom-calendar {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    min-height: 600px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    position: relative;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* Calendar Header */
.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.calendar-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #343a40;
    margin: 0;
}

/* Calendar Grid - Month View */
.calendar-grid.month-view {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    grid-auto-rows: minmax(100px, 1fr);
    flex: 1;
    border-top: 1px solid #e9ecef;
    border-left: 1px solid #e9ecef;
}

.calendar-day-header {
    padding: 10px;
    text-align: center;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
    border-right: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
}

.calendar-day {
    padding: 5px;
    border-right: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
    overflow: hidden;
    position: relative;
}

.calendar-day.prev-month,
.calendar-day.next-month {
    background-color: #f8f9fa;
    color: #adb5bd;
}

.calendar-day.today {
    background-color: #fff8e6;
}

.calendar-day.weekend {
    background-color: #f8f9fa;
}

.day-number {
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.calendar-day.today .day-number {
    display: inline-block;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background-color: #007bff;
    color: #fff;
    border-radius: 50%;
}

.day-events {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

/* Calendar Grid - Week View */
.calendar-grid.week-view {
    display: grid;
    grid-template-columns: 60px repeat(7, 1fr);
    flex: 1;
    border-top: 1px solid #e9ecef;
    border-left: 1px solid #e9ecef;
    overflow-y: auto;
}

.time-column {
    display: flex;
    flex-direction: column;
    border-right: 1px solid #e9ecef;
}

.time-slot {
    height: 60px;
    padding: 5px;
    text-align: right;
    font-size: 0.8rem;
    color: #6c757d;
    border-bottom: 1px solid #e9ecef;
}

.day-column {
    display: flex;
    flex-direction: column;
    border-right: 1px solid #e9ecef;
}

.day-column.full-width {
    grid-column: 2 / span 7;
}

.day-header {
    padding: 10px;
    text-align: center;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.day-name {
    font-weight: 600;
    color: #495057;
}

.hour-cell {
    height: 60px;
    border-bottom: 1px solid #e9ecef;
    position: relative;
}

.day-column.today {
    background-color: #fff8e6;
}

.day-column.weekend {
    background-color: #f8f9fa;
}

/* Calendar Events Container */
.calendar-events {
    position: relative;
    margin-top: 10px;
}

.all-day-container {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    min-height: 30px;
    margin-bottom: 10px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background-color: #f8f9fa;
    padding: 5px;
}

/* Calendar Event List */
.calendar-event-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow-y: auto;
}

.list-day-header {
    padding: 10px 15px;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    position: sticky;
    top: 0;
    z-index: 1;
}

.list-event {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    transition: background-color 0.2s;
}

.list-event:hover {
    background-color: #f8f9fa;
}

.event-color-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 10px;
    flex-shrink: 0;
}

.event-content {
    flex: 1;
}

.no-events {
    padding: 20px;
    text-align: center;
    color: #6c757d;
}

/* Calendar Events */
.calendar-event {
    padding: 2px 4px;
    font-size: 0.8rem;
    color: #fff;
    background-color: #007bff;
    border-radius: 3px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    margin-bottom: 1px;
    border-left: 3px solid rgba(0, 0, 0, 0.2);
}

.calendar-event.time-event {
    position: absolute;
    left: 0;
    width: 100%;
    z-index: 1;
    padding: 4px 6px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.calendar-event.all-day-event {
    height: 24px;
    display: flex;
    align-items: center;
}

.calendar-event .event-title {
    font-weight: 600;
    margin-bottom: 2px;
}

.calendar-event .event-time {
    font-size: 0.75rem;
    opacity: 0.9;
}

.more-events {
    font-size: 0.75rem;
    color: #6c757d;
    text-align: center;
    cursor: pointer;
}

/* Event Details */
.event-location,
.event-calendar {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 2px;
}

/* Loading Indicator */
.calendar-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

.calendar-loading .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Dragging */
.calendar-event.dragging {
    opacity: 0.7;
    z-index: 1000;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .calendar-title {
        font-size: 1.2rem;
    }
    
    .calendar-grid.month-view {
        grid-auto-rows: minmax(60px, 1fr);
    }
    
    .day-number {
        font-size: 0.8rem;
    }
    
    .calendar-event {
        font-size: 0.7rem;
    }
    
    .time-slot {
        font-size: 0.7rem;
        height: 50px;
    }
    
    .hour-cell {
        height: 50px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .custom-calendar {
        background-color: #212529;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }
    
    .calendar-header {
        background-color: #343a40;
        border-bottom-color: #495057;
    }
    
    .calendar-title {
        color: #f8f9fa;
    }
    
    .calendar-day-header {
        background-color: #343a40;
        color: #e9ecef;
        border-color: #495057;
    }
    
    .calendar-day {
        border-color: #495057;
        background-color: #212529;
    }
    
    .calendar-day.prev-month,
    .calendar-day.next-month {
        background-color: #2c3034;
        color: #6c757d;
    }
    
    .calendar-day.today {
        background-color: #2b3035;
    }
    
    .calendar-day.weekend {
        background-color: #2c3034;
    }
    
    .day-number {
        color: #e9ecef;
    }
    
    .time-slot {
        color: #adb5bd;
        border-color: #495057;
    }
    
    .day-header {
        background-color: #343a40;
        border-color: #495057;
    }
    
    .day-name {
        color: #e9ecef;
    }
    
    .hour-cell {
        border-color: #495057;
    }
    
    .all-day-container {
        background-color: #343a40;
        border-color: #495057;
    }
    
    .list-day-header {
        background-color: #343a40;
        color: #e9ecef;
        border-color: #495057;
    }
    
    .list-event {
        border-color: #495057;
    }
    
    .list-event:hover {
        background-color: #343a40;
    }
    
    .no-events {
        color: #adb5bd;
    }
    
    .more-events {
        color: #adb5bd;
    }
    
    .event-location,
    .event-calendar {
        color: #adb5bd;
    }
    
    .calendar-loading {
        background-color: rgba(33, 37, 41, 0.8);
    }
    
    .calendar-loading .spinner {
        border-color: #343a40;
        border-top-color: #007bff;
    }
}

/* Additional Styles for Enhanced Visual Appeal */

/* Calendar Day Hover Effect */
.calendar-day:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Event Hover Effect */
.calendar-event:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

/* Today Indicator Animation */
@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
}

.calendar-day.today .day-number {
    animation: pulse 2s infinite;
}

/* Current Time Indicator */
.current-time-indicator {
    position: absolute;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #dc3545;
    z-index: 2;
}

.current-time-indicator::before {
    content: '';
    position: absolute;
    left: -5px;
    top: -4px;
    width: 10px;
    height: 10px;
    background-color: #dc3545;
    border-radius: 50%;
}

/* Event Categories */
.calendar-event.important {
    background-color: #dc3545;
}

.calendar-event.holiday {
    background-color: #28a745;
}

.calendar-event.meeting {
    background-color: #6f42c1;
}

.calendar-event.personal {
    background-color: #fd7e14;
}

/* Month Navigation Animation */
@keyframes slideRight {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideLeft {
    from { transform: translateX(20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.slide-right {
    animation: slideRight 0.3s ease forwards;
}

.slide-left {
    animation: slideLeft 0.3s ease forwards;
}

/* Event Selection */
.calendar-event.selected {
    box-shadow: 0 0 0 2px #007bff;
}

/* Mobile Optimizations */
@media (max-width: 576px) {
    .calendar-grid.month-view {
        grid-auto-rows: minmax(50px, 1fr);
    }
    
    .calendar-day {
        padding: 2px;
    }
    
    .day-number {
        font-size: 0.7rem;
        margin-bottom: 2px;
    }
    
    .calendar-event {
        padding: 1px 2px;
        font-size: 0.65rem;
    }
    
    .calendar-day.today .day-number {
        width: 20px;
        height: 20px;
        line-height: 20px;
    }
    
    .time-column {
        width: 40px;
    }
    
    .time-slot {
        padding: 2px;
        height: 40px;
    }
    
    .hour-cell {
        height: 40px;
    }
}

/* Print Styles */
@media print {
    .custom-calendar {
        box-shadow: none;
        height: auto;
    }
    
    .calendar-grid.month-view {
        grid-auto-rows: 120px;
    }
    
    .calendar-event {
        break-inside: avoid;
        color: #000 !important;
        border: 1px solid #000;
        background-color: transparent !important;
    }
    
    .calendar-event::after {
        content: attr(title);
        color: #000;
    }
    
    .calendar-loading {
        display: none !important;
    }
}