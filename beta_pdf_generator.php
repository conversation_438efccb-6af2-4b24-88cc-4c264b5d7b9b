<?php
/**
 * Beta Tester PDF Generator - Portable Self-Contained System
 *
 * This single file contains everything needed to generate personalized PDFs
 * for beta tester outreach. Completely portable - works anywhere!
 *
 * Usage: Place in ANY directory and access via browser
 * Security: Password protected with optional IP restrictions
 *
 * Author: <PERSON> - Rowan Elite Rides
 * Version: 2.0.0 - Portable Edition
 */

// ============================================================================
// SECURITY CONFIGURATION - CUSTOMIZE THESE SETTINGS
// ============================================================================

// Change this password!
define('ADMIN_PASSWORD', '12181977');

// Optional: Restrict to specific IP addresses (leave empty array to allow all)
// Example: ['*************', '************']
define('ALLOWED_IPS', []);

// Optional: Require secret URL parameter for access
// Example: ?access=mysecretkey2025
define('SECRET_ACCESS_KEY', ''); // Leave empty to disable

// Optional: Hide from search engines and bots
define('STEALTH_MODE', true);

// ============================================================================
// COMPANY INFORMATION
// ============================================================================
define('COMPANY_NAME', 'Rowan Elite Rides');
define('CONTACT_NAME', 'Brian Correll');
define('CONTACT_EMAIL', '<EMAIL>');
define('CONTACT_PHONE', '************');

// ============================================================================
// SECURITY CHECKS - DO NOT MODIFY
// ============================================================================

// Check IP restrictions
if (!empty(ALLOWED_IPS) && !in_array($_SERVER['REMOTE_ADDR'], ALLOWED_IPS)) {
    http_response_code(404);
    exit('<!DOCTYPE html><html><head><title>404 Not Found</title></head><body><h1>Not Found</h1><p>The requested URL was not found on this server.</p></body></html>');
}

// Check secret access key
if (!empty(SECRET_ACCESS_KEY) && (!isset($_GET['access']) || $_GET['access'] !== SECRET_ACCESS_KEY)) {
    http_response_code(404);
    exit('<!DOCTYPE html><html><head><title>404 Not Found</title></head><body><h1>Not Found</h1><p>The requested URL was not found on this server.</p></body></html>');
}

// Stealth mode - hide from bots and search engines
if (STEALTH_MODE) {
    header('X-Robots-Tag: noindex, nofollow, noarchive, nosnippet');
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
}

// Start session for authentication
session_start();

// ============================================================================
// PORTABLE TCPDF DETECTION
// ============================================================================

function getTCPDFPath() {
    $possiblePaths = [
        __DIR__ . '/libraries/tcpdf/tcpdf.php',           // Same directory
        __DIR__ . '/tcpdf/tcpdf.php',                     // tcpdf folder in same directory
        __DIR__ . '/../libraries/tcpdf/tcpdf.php',        // Parent directory
        dirname($_SERVER['DOCUMENT_ROOT']) . '/libraries/tcpdf/tcpdf.php', // Server libraries
        $_SERVER['DOCUMENT_ROOT'] . '/libraries/tcpdf/tcpdf.php'  // Web root libraries
    ];

    foreach ($possiblePaths as $path) {
        if (file_exists($path)) {
            return $path;
        }
    }
    return false;
}

function isTCPDFAvailable() {
    return getTCPDFPath() !== false;
}

// Simple authentication
if (!isset($_SESSION['pdf_auth']) || $_SESSION['pdf_auth'] !== true) {
    if ($_POST['password'] ?? '' === ADMIN_PASSWORD) {
        $_SESSION['pdf_auth'] = true;
    } else {
        showLoginForm();
        exit;
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

// Handle actions
$action = $_GET['action'] ?? 'home';

switch ($action) {
    case 'nda':
        handleNDA();
        break;
    case 'outreach':
        handleOutreach();
        break;
    case 'generate':
        generatePDF();
        break;
    default:
        showHome();
        break;
}

function showLoginForm($error = '') {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Beta PDF Generator - Login</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body class="bg-light">
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white text-center">
                            <h4><i class="fas fa-file-pdf"></i> Beta PDF Generator</h4>
                        </div>
                        <div class="card-body">
                            <?php if ($error): ?>
                                <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
                            <?php endif; ?>
                            <form method="POST">
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password:</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">Login</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
}

function showHome() {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Beta PDF Generator</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body class="bg-light">
        <div class="container mt-4">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <h3><i class="fas fa-file-pdf"></i> Beta Tester PDF Generator</h3>
                            <a href="?logout=1" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                        <div class="card-body">
                            <p class="lead">Generate personalized PDFs for beta tester outreach.</p>

                            <?php checkTCPDFInstallation(); ?>
                            
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100 border-success">
                                        <div class="card-body">
                                            <h5 class="card-title">
                                                <i class="fas fa-file-contract text-success"></i>
                                                Beta Tester NDA
                                            </h5>
                                            <p class="card-text">Generate a professional Non-Disclosure Agreement for beta testers with lifetime free account benefits.</p>
                                        </div>
                                        <div class="card-footer bg-transparent">
                                            <a href="?action=nda" class="btn btn-success btn-block w-100">
                                                <i class="fas fa-play"></i> Generate NDA
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100 border-primary">
                                        <div class="card-body">
                                            <h5 class="card-title">
                                                <i class="fas fa-envelope text-primary"></i>
                                                Personalized Outreach Letter
                                            </h5>
                                            <p class="card-text">Create a customized outreach letter for potential beta testers based on their specific experience.</p>
                                        </div>
                                        <div class="card-footer bg-transparent">
                                            <a href="?action=outreach" class="btn btn-primary btn-block w-100">
                                                <i class="fas fa-play"></i> Generate Letter
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert alert-info mt-4">
                                <h6><i class="fas fa-info-circle"></i> Instructions:</h6>
                                <ol class="mb-0">
                                    <li>Choose the type of document you want to generate</li>
                                    <li>Fill out the questionnaire with details about the beta tester</li>
                                    <li>Download the personalized PDF document</li>
                                    <li>Send the PDF to your potential beta tester</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
}

function handleNDA() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        generatePDF();
        return;
    }
    
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Generate NDA - Beta PDF Generator</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body class="bg-light">
        <div class="container mt-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-file-contract"></i> Generate Beta Tester NDA</h3>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="template" value="nda">
                        
                        <div class="mb-3">
                            <label for="tester_name" class="form-label">Beta Tester Full Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="tester_name" name="tester_name" required>
                            <div class="form-text">Enter the full legal name of the beta tester for the NDA signature line.</div>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-file-pdf"></i> Generate NDA PDF
                            </button>
                            <a href="?" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Home
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
}

function handleOutreach() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        generatePDF();
        return;
    }

    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Generate Outreach Letter - Beta PDF Generator</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body class="bg-light">
        <div class="container mt-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3><i class="fas fa-envelope"></i> Generate Personalized Outreach Letter</h3>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="template" value="outreach">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="current_date" class="form-label">Current Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="current_date" name="current_date" value="<?= date('Y-m-d') ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tester_name" class="form-label">Tester Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="tester_name" name="tester_name" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="event_name" class="form-label">Their Event/Show Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="event_name" name="event_name" placeholder="e.g., Love Like Lauren" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tester_type" class="form-label">Type of Tester <span class="text-danger">*</span></label>
                                    <select class="form-control" id="tester_type" name="tester_type" required>
                                        <option value="">-- Select Type --</option>
                                        <option value="show managers">Show Managers</option>
                                        <option value="event organizers">Event Organizers</option>
                                        <option value="car club presidents">Car Club Presidents</option>
                                        <option value="racing coordinators">Racing Coordinators</option>
                                        <option value="automotive business owners">Automotive Business Owners</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="tester_experience" class="form-label">Their Experience (Optional)</label>
                            <textarea class="form-control" id="tester_experience" name="tester_experience" rows="2"
                                      placeholder="e.g., managing automotive events (leave blank for generic message)"></textarea>
                            <small class="form-text text-muted">If you don't know their specific experience, leave blank for a generic professional message.</small>
                        </div>

                        <div class="mb-3">
                            <label for="specific_compliment" class="form-label">Personal Touch (Optional)</label>
                            <textarea class="form-control" id="specific_compliment" name="specific_compliment" rows="2"
                                      placeholder="e.g., your excellent work organizing automotive events (leave blank for generic)"></textarea>
                            <small class="form-text text-muted">Add a personal compliment if you know their work, or leave blank for a professional generic message.</small>
                        </div>

                        <div class="mb-3">
                            <label for="closing_message" class="form-label">Closing Message</label>
                            <select class="form-control" id="closing_message" name="closing_message" onchange="updateClosingMessage()">
                                <option value="I'd love to hear your thoughts on this opportunity.">Generic Professional (Default)</option>
                                <option value="custom">Custom Message</option>
                            </select>
                            <textarea class="form-control mt-2" id="custom_closing" name="custom_closing" rows="2"
                                      style="display: none;" placeholder="Enter your custom closing message"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="ps_message" class="form-label">P.S. Message (Optional)</label>
                            <textarea class="form-control" id="ps_message" name="ps_message" rows="2"
                                      placeholder="Add a personal touch if you know them well (optional)"></textarea>
                        </div>

                        <script>
                        function updateClosingMessage() {
                            const select = document.getElementById('closing_message');
                            const customTextarea = document.getElementById('custom_closing');
                            if (select.value === 'custom') {
                                customTextarea.style.display = 'block';
                                customTextarea.required = true;
                            } else {
                                customTextarea.style.display = 'none';
                                customTextarea.required = false;
                            }
                        }
                        </script>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> Quick Tip:</h6>
                            <p class="mb-0">Only <strong>Name</strong>, <strong>Event/Show Name</strong>, and <strong>Type</strong> are required. Leave other fields blank for a professional generic letter!</p>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-file-pdf"></i> Generate Letter PDF
                            </button>
                            <button type="button" class="btn btn-success" onclick="quickFill()">
                                <i class="fas fa-bolt"></i> Quick Generic Fill
                            </button>
                            <a href="?" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Home
                            </a>
                        </div>

                        <script>
                        function quickFill() {
                            // Clear optional fields for generic letter
                            document.getElementById('tester_experience').value = '';
                            document.getElementById('specific_compliment').value = '';
                            document.getElementById('ps_message').value = '';
                            document.getElementById('closing_message').value = "I'd love to hear your thoughts on this opportunity.";
                            updateClosingMessage();
                            alert('Optional fields cleared! Just fill in Name, Event/Show Name, and Type, then generate.');
                        }
                        </script>
                    </form>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
}

function generatePDF() {
    $template = $_POST['template'] ?? '';

    // Check if TCPDF exists using portable detection
    $useTCPDF = isTCPDFAvailable();

    if ($template === 'nda') {
        $content = generateNDAContent($_POST);
        $filename = 'Beta_Tester_NDA_' . sanitizeFilename($_POST['tester_name']) . '_' . date('Y-m-d') . '.pdf';
    } elseif ($template === 'outreach') {
        $content = generateOutreachContent($_POST);
        $filename = 'Outreach_Letter_' . sanitizeFilename($_POST['tester_name']) . '_' . date('Y-m-d') . '.pdf';
    } else {
        die('Invalid template');
    }

    if ($useTCPDF) {
        generateTCPDFDocument($content, $filename);
    } else {
        generateHTMLDocument($content, $filename);
    }
}

function generateTCPDFDocument($content, $filename) {
    $tcpdfPath = getTCPDFPath();
    if (!$tcpdfPath) {
        die('TCPDF library not found');
    }
    require_once $tcpdfPath;

    $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);

    // Set document information
    $pdf->SetCreator(COMPANY_NAME);
    $pdf->SetAuthor(CONTACT_NAME);
    $pdf->SetTitle($filename);

    // Remove default header/footer
    $pdf->setPrintHeader(false);
    $pdf->setPrintFooter(false);

    // Set margins
    $pdf->SetMargins(15, 15, 15);

    // Add a page
    $pdf->AddPage();

    // Set font
    $pdf->SetFont('helvetica', '', 11);

    // Write content
    $pdf->writeHTML($content, true, false, true, false, '');

    // Output PDF
    $pdf->Output($filename, 'D');
    exit;
}

function generateHTMLDocument($content, $filename) {
    // Fallback: Generate HTML document if TCPDF not available
    header('Content-Type: text/html; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . str_replace('.pdf', '.html', $filename) . '"');

    echo '<!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>' . htmlspecialchars($filename) . '</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
            h1, h2, h3 { color: #1338BE; }
            .signature-line { border-bottom: 1px solid #000; width: 200px; display: inline-block; }
        </style>
    </head>
    <body>' . $content . '</body>
    </html>';
    exit;
}

function sanitizeFilename($filename) {
    return preg_replace('/[^a-zA-Z0-9_-]/', '_', $filename);
}

function generateNDAContent($data) {
    $testerName = htmlspecialchars($data['tester_name']);
    $currentDate = date('F j, Y');

    return '
    <h1 style="text-align: center; color: #1338BE; margin-bottom: 10px;">BETA TESTING NON-DISCLOSURE AGREEMENT</h1>
    <h2 style="text-align: center; margin-bottom: 30px;">' . COMPANY_NAME . ' Events & Shows Management System</h2>

    <p>This agreement is between <strong>' . COMPANY_NAME . '</strong> ("Company") and <strong>' . $testerName . '</strong> ("Beta Collaborator") regarding beta testing and development collaboration for the ' . COMPANY_NAME . ' Events & Shows Management System.</p>

    <h3 style="color: #1338BE;">CONFIDENTIAL INFORMATION includes:</h3>
    <ul>
    <li>All features, functionality, and technical details of the platform</li>
    <li>Screenshots, videos, documentation, or any visual materials provided</li>
    <li>Performance data, user interface designs, and business processes</li>
    <li>Development roadmap, feature plans, and strategic information</li>
    <li>Any bugs, issues, feedback discussions, or improvement suggestions</li>
    <li>Login credentials, testing procedures, and system access methods</li>
    </ul>

    <h3 style="color: #1338BE;">BETA COLLABORATOR AGREES TO:</h3>
    <ul>
    <li>Keep all information strictly confidential until public platform launch</li>
    <li>Not share login credentials or platform access with others</li>
    <li>Not screenshot, record, or document the system without written permission</li>
    <li>Not discuss the platform publicly on social media, forums, or with others</li>
    <li>Provide honest, detailed feedback and recommendations for improvements</li>
    <li>Report bugs and issues promptly and thoroughly</li>
    <li>Participate actively in the development collaboration process</li>
    <li>Test the platform across multiple devices when possible</li>
    </ul>

    <h3 style="color: #1338BE;">TESTING REQUIREMENTS:</h3>
    <p>Beta Collaborator agrees to conduct testing using <strong>FAKE/TEST DATA ONLY:</strong></p>
    <ul>
    <li>Create fake test shows and events (not real shows)</li>
    <li>Create fake judge and staff accounts as needed for testing</li>
    <li>Submit fake vehicle registrations to test registration workflows</li>
    <li>Use fake payment information for payment testing</li>
    <li>Conduct fake judging sessions to test scoring systems</li>
    <li>Test all platform features using simulated data only</li>
    </ul>

    <h3 style="color: #1338BE;">PROHIBITED ACTIVITIES DURING BETA:</h3>
    <ul>
    <li>Using the platform for real shows, events, or business purposes</li>
    <li>Creating actual shows or events that will be attended by real participants</li>
    <li>Processing real payments or collecting real registration fees</li>
    <li>Using the platform for any live, public-facing events</li>
    <li>Mixing real data with test data</li>
    </ul>

    <h3 style="color: #1338BE;">BETA COLLABORATOR BENEFITS:</h3>
    <p>In consideration for testing services and development feedback, Company agrees to provide:</p>
    <ul>
    <li>Lifetime free account for listing shows and events on the platform (after public launch)</li>
    <li>Exemption from all future subscription fees or listing charges</li>
    <li>Priority customer support during and after platform launch</li>
    <li>Early access to new features and updates</li>
    <li>Recognition as a founding community member</li>
    </ul>

    <h3 style="color: #1338BE;">COMPANY AGREES TO:</h3>
    <ul>
    <li>Provide reasonable testing access and technical support</li>
    <li>Consider collaborator feedback seriously in development decisions</li>
    <li>Protect collaborator\'s personal information and testing data</li>
    <li>Honor the lifetime free account benefit as described above</li>
    <li>Provide advance notice of any significant platform changes</li>
    <li>Provide clear guidelines for creating test data and scenarios</li>
    </ul>

    <h3 style="color: #1338BE;">TERM AND TERMINATION:</h3>
    <ul>
    <li>This agreement remains in effect until the platform\'s public launch</li>
    <li>Either party may terminate with 7 days written notice</li>
    <li>Confidentiality obligations survive termination indefinitely</li>
    <li>Lifetime account benefits survive termination and remain valid</li>
    <li>All test data will be purged before public launch</li>
    </ul>

    <h3 style="color: #1338BE;">GENERAL PROVISIONS:</h3>
    <ul>
    <li>This agreement is governed by the laws of North Carolina</li>
    <li>Any disputes will be resolved through binding arbitration</li>
    <li>This represents the complete agreement between parties</li>
    <li>Modifications must be in writing and signed by both parties</li>
    </ul>

    <br><br>
    <table width="100%" style="border-collapse: collapse;">
    <tr>
    <td width="50%" style="vertical-align: top; padding-right: 20px;">
    <strong>Beta Collaborator Signature:</strong><br><br>
    <div style="border-bottom: 1px solid #000; width: 200px; height: 20px; margin-bottom: 10px;"></div>
    Date: <div style="border-bottom: 1px solid #000; width: 100px; height: 20px; display: inline-block; margin-left: 10px;"></div><br><br>
    Print Name: ' . $testerName . '
    </td>
    <td width="50%" style="vertical-align: top; padding-left: 20px;">
    <strong>Company Representative Signature:</strong><br><br>
    <div style="border-bottom: 1px solid #000; width: 200px; height: 20px; margin-bottom: 10px;"></div>
    Date: <div style="border-bottom: 1px solid #000; width: 100px; height: 20px; display: inline-block; margin-left: 10px;"></div><br><br>
    Print Name: ' . CONTACT_NAME . '<br>
    Title: Founder/Developer, ' . COMPANY_NAME . '<br>
    Email: ' . CONTACT_EMAIL . '<br>
    Phone: ' . CONTACT_PHONE . '
    </td>
    </tr>
    </table>
    ';
}

function generateOutreachContent($data) {
    $currentDate = date('F j, Y', strtotime($data['current_date']));
    $testerName = htmlspecialchars($data['tester_name']);
    $eventName = htmlspecialchars($data['event_name']);
    $testerType = htmlspecialchars($data['tester_type']);
    $testerExperience = !empty($data['tester_experience']) ? htmlspecialchars($data['tester_experience']) : '';
    $specificCompliment = !empty($data['specific_compliment']) ? htmlspecialchars($data['specific_compliment']) : '';
    // Handle closing message
    if ($data['closing_message'] === 'custom' && !empty($data['custom_closing'])) {
        $closingMessage = htmlspecialchars($data['custom_closing']);
    } else {
        $closingMessage = htmlspecialchars($data['closing_message']);
    }
    $psMessage = !empty($data['ps_message']) ? htmlspecialchars($data['ps_message']) : '';

    // Create personalized or generic opening based on available info
    if (!empty($testerExperience) && !empty($specificCompliment)) {
        $personalizedOpening = "I'm reaching out because of your excellent work " . $testerExperience . ". I'm truly impressed with " . $specificCompliment . ".";
    } elseif (!empty($testerExperience)) {
        $personalizedOpening = "I'm reaching out because of your experience " . $testerExperience . ".";
    } elseif (!empty($specificCompliment)) {
        $personalizedOpening = "I'm reaching out because I'm impressed with " . $specificCompliment . ".";
    } else {
        $personalizedOpening = "I'm reaching out because of your experience in the automotive event industry.";
    }

    // Create expertise reference
    $expertiseReference = !empty($eventName) ? "your experience with " . $eventName : "your automotive event expertise";

    // Create future events reference
    $futureEventsRef = !empty($eventName) ? $eventName . " and your future events" : "your events";

    return '
    <div style="text-align: right; margin-bottom: 30px;">
    <strong>' . CONTACT_NAME . '</strong><br>
    Founder/Developer<br>
    ' . COMPANY_NAME . '<br>
    Email: ' . CONTACT_EMAIL . '<br>
    Phone: ' . CONTACT_PHONE . '<br>
    <br>
    ' . $currentDate . '
    </div>

    <div style="margin-bottom: 30px;">
    ' . $testerName . '<br>
    </div>

    <p><strong>Subject: Exclusive Beta Collaboration Opportunity - ' . $eventName . ' Management Platform</strong></p>

    <p>Dear ' . $testerName . ',</p>

    <p>I hope this letter finds you well! ' . $personalizedOpening . '</p>

    <p>I\'m developing a comprehensive automotive event management platform, and I\'m looking for a select group of experienced ' . $testerType . ' to collaborate with me during the beta testing phase. Based on ' . $expertiseReference . ', I believe your insights would be invaluable.</p>

    <h3 style="color: #1338BE;">Here\'s what I\'m offering to beta collaborators:</h3>
    <ul>
    <li><strong>🎯 Lifetime Free Account</strong> - Unlimited show/event listings forever (after launch)</li>
    <li><strong>📱 Early Access</strong> - Be among the first to use cutting-edge automotive event technology</li>
    <li><strong>🤝 Direct Influence</strong> - Your feedback and recommendations will shape the platform\'s development</li>
    <li><strong>🏆 Founding Member Status</strong> - Recognition as someone who helped build the platform</li>
    <li><strong>💰 Future Protection</strong> - While the platform will eventually transition to paid subscriptions, you\'ll keep free access for life</li>
    </ul>

    <h3 style="color: #1338BE;">What the testing involves:</h3>
    <ul>
    <li>Creating fake test shows and events to explore all features</li>
    <li>Setting up fake judge and staff accounts to test different user roles</li>
    <li>Submitting fake vehicle registrations to test the registration workflow</li>
    <li>Testing the judging and scoring systems with simulated data</li>
    <li>Providing detailed feedback and improvement suggestions</li>
    <li>Helping identify any bugs or usability issues</li>
    </ul>

    <p><strong>Important Note:</strong> During beta testing, the platform is for testing purposes only using fake data. No real shows or events should be created until the public launch.</p>

    <p>This isn\'t just about finding bugs - I\'m looking for collaborators who understand the real challenges of automotive event management.</p>

    <p>Your experience in the automotive event industry would help ensure this platform serves events of all types and sizes.</p>

    <p>Would you be interested in learning more about this opportunity? If so, I can send you a simple confidentiality agreement and more detailed information about the platform.</p>

    <p>This is a chance to directly influence technology that could benefit the entire automotive community while securing permanent free access for ' . $futureEventsRef . ' once the platform launches publicly.</p>

    <p>' . $closingMessage . '</p>

    <p>Best regards,</p>

    <p><strong>' . CONTACT_NAME . '</strong><br>
    Founder/Developer<br>
    ' . COMPANY_NAME . '</p>

    ' . ($psMessage ? '<p><em>P.S. ' . $psMessage . '</em></p>' : '') . '
    ';
}

// Check if TCPDF is available and show installation instructions if needed
function checkTCPDFInstallation() {
    if (!isTCPDFAvailable()) {
        ?>
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle"></i> TCPDF Library Not Found</h5>
            <p>For best PDF quality, install the TCPDF library in one of these locations:</p>
            <ul>
                <li><code>libraries/tcpdf/</code> (same directory as this file)</li>
                <li><code>tcpdf/</code> (same directory as this file)</li>
                <li><code>../libraries/tcpdf/</code> (parent directory)</li>
            </ul>
            <p><strong>Download:</strong> <a href="https://tcpdf.org/" target="_blank">https://tcpdf.org/</a></p>
            <p><strong>Note:</strong> The system will work without TCPDF but will generate HTML files instead of PDFs.</p>
        </div>
        <?php
        return false;
    } else {
        ?>
        <div class="alert alert-success">
            <h5><i class="fas fa-check-circle"></i> TCPDF Library Found</h5>
            <p>Professional PDF generation is available! Your PDFs will be high-quality and properly formatted.</p>
            <p><strong>Location:</strong> <code><?= str_replace(__DIR__, '.', getTCPDFPath()) ?></code></p>
        </div>
        <?php
        return true;
    }
}

?>

<!-- Add this at the end of the file for easy reference -->
<!--
BETA PDF GENERATOR - QUICK SETUP GUIDE

1. INSTALLATION:
   - Upload this file (beta_pdf_generator.php) to your web root
   - Change the ADMIN_PASSWORD at the top of the file
   - Optionally install TCPDF library for better PDF quality

2. USAGE:
   - Access: https://yoursite.com/beta_pdf_generator.php
   - Login with your admin password
   - Choose NDA or Outreach Letter
   - Fill out the form
   - Download the generated PDF

3. CUSTOMIZATION:
   - Edit the constants at the top for your company info
   - Modify the generateNDAContent() and generateOutreachContent() functions
   - Adjust styling in the HTML templates

4. REMOVAL:
   - When beta testing is complete, simply delete this file
   - No database changes or other files to clean up

5. SECURITY:
   - Change the default password immediately
   - Consider adding IP restrictions if needed
   - This is meant for temporary use during beta testing only

EXAMPLE USAGE FOR HAILEY GILLAND:
- Template: Outreach Letter
- Tester Name: Hailey Gilland
- Event Name: Love Like Lauren
- Experience: managing the Love Like Lauren show
- Compliment: how you've built such a meaningful and well-organized show that honors Lauren's memory
- Result: Professional personalized letter ready to send

-->
