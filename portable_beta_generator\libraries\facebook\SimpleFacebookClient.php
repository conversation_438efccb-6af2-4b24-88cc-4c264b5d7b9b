<?php
/**
 * Simple Facebook Client
 * 
 * A lightweight implementation of Facebook API client that doesn't rely on the Facebook SDK.
 * This is used as a fallback when the Facebook SDK is not available.
 */
class SimpleFacebookClient {
    /**
     * @var string Facebook App ID
     */
    private $appId;
    
    /**
     * @var string Facebook App Secret
     */
    private $appSecret;
    
    /**
     * @var string Facebook Graph API version
     */
    private $graphVersion;
    
    /**
     * @var string Redirect URI for OAuth flow
     */
    private $redirectUri;
    
    /**
     * Constructor
     * 
     * @param string $appId Facebook App ID
     * @param string $appSecret Facebook App Secret
     * @param string $redirectUri Redirect URI for OAuth flow
     * @param string $graphVersion Facebook Graph API version
     */
    public function __construct($appId, $appSecret, $redirectUri, $graphVersion = 'v18.0') {
        $this->appId = $appId;
        $this->appSecret = $appSecret;
        $this->redirectUri = $redirectUri;
        $this->graphVersion = $graphVersion;
    }
    
    /**
     * Get login URL
     * 
     * @param string $state CSRF state token
     * @return string Login URL
     */
    public function getLoginUrl($state) {
        $params = [
            'client_id' => $this->appId,
            'redirect_uri' => $this->redirectUri,
            'state' => $state,
            'scope' => 'email',
            'response_type' => 'code',
        ];
        
        return 'https://www.facebook.com/' . $this->graphVersion . '/dialog/oauth?' . http_build_query($params);
    }
    
    /**
     * Get access token from code
     * 
     * @param string $code Authorization code
     * @return string|null Access token or null on failure
     */
    public function getAccessTokenFromCode($code) {
        $params = [
            'client_id' => $this->appId,
            'client_secret' => $this->appSecret,
            'redirect_uri' => $this->redirectUri,
            'code' => $code,
        ];
        
        $tokenUrl = 'https://graph.facebook.com/' . $this->graphVersion . '/oauth/access_token';
        
        $response = $this->makeRequest($tokenUrl, $params);
        
        if ($response && isset($response['access_token'])) {
            return $response['access_token'];
        }
        
        return null;
    }
    
    /**
     * Get user data from access token
     * 
     * @param string $accessToken Access token
     * @return array|null User data or null on failure
     */
    public function getUserData($accessToken) {
        $params = [
            'fields' => 'id,name,email,picture.width(200).height(200)',
            'access_token' => $accessToken,
        ];
        
        $graphUrl = 'https://graph.facebook.com/' . $this->graphVersion . '/me';
        
        $userData = $this->makeRequest($graphUrl, $params);
        
        if ($userData && isset($userData['id'])) {
            // Format picture data to match SDK format
            if (isset($userData['picture']) && isset($userData['picture']['data'])) {
                $userData['picture'] = $userData['picture']['data']['url'];
            }
            
            return $userData;
        }
        
        return null;
    }
    
    /**
     * Make HTTP request to Facebook Graph API
     * 
     * @param string $url API endpoint URL
     * @param array $params Request parameters
     * @param string $method HTTP method (GET or POST)
     * @return array|null Response data or null on failure
     */
    private function makeRequest($url, $params, $method = 'GET') {
        $ch = curl_init();
        
        if ($method === 'GET') {
            $url .= '?' . http_build_query($params);
            curl_setopt($ch, CURLOPT_URL, $url);
        } else {
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        }
        
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($error) {
            error_log('SimpleFacebookClient: cURL error: ' . $error);
            return null;
        }
        
        $data = json_decode($response, true);
        
        if (isset($data['error'])) {
            error_log('SimpleFacebookClient: API error: ' . json_encode($data['error']));
            return null;
        }
        
        return $data;
    }
}