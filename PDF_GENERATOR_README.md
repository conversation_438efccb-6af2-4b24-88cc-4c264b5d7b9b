# PDF Generator System for Beta Tester Outreach

A comprehensive PDF generation system that creates personalized documents for beta tester outreach by asking questions and filling templates.

## 🚀 **Quick Start**

### 1. Installation
```bash
# Run the installation script
php install/install_pdf_generator.php
```
Or visit: `https://yoursite.com/install/install_pdf_generator.php`

### 2. Install TCPDF Library
Download TCPDF from https://tcpdf.org/ and extract to `/libraries/tcpdf/`

### 3. Add Routes
Add these routes to your `config/routes.php`:
```php
// PDF Generator Routes
'/pdf-generator' => 'PDFGeneratorController@index',
'/pdf-generator/questionnaire/{id}' => 'PDFGeneratorController@questionnaire',
'/pdf-generator/generate' => 'PDFGeneratorController@generate',
'/pdf-generator/download/{filename}' => 'PDFGeneratorController@download',
'/pdf-generator/templates' => 'PDFGeneratorController@templates',
'/pdf-generator/edit-template/{id?}' => 'PDFGeneratorController@editTemplate',
```

## 📋 **How It Works**

### Step 1: Choose a Template
- Access `/pdf-generator` from your admin panel
- Select from pre-built templates:
  - **Beta Tester NDA** - Professional NDA document
  - **Personalized Outreach Letter** - Customizable outreach letter

### Step 2: Answer Questions
The system asks relevant questions based on the template:

**For NDA Template:**
- Beta Tester Full Name

**For Outreach Letter:**
- Current Date
- Tester Full Name
- Tester Address (Optional)
- Their Event/Show Name
- Their Experience Description
- Specific Event/Show Reference
- Specific Compliment About Their Work
- Type of Tester (show managers, organizers, etc.)
- Their Specific Area of Expertise
- Additional Context About Their Challenges
- Specific Value They Bring
- Types of Events They Could Help With
- Their Specific Events/Shows
- Personalized Closing Message
- Personalized P.S. Message

### Step 3: Generate PDF
- System replaces placeholders in template with your answers
- Creates professional PDF document
- Provides download link

## 🎯 **Example Usage Scenarios**

### Scenario 1: Hailey Gilland (Love Like Lauren Show)
**Template:** Personalized Outreach Letter
**Questions & Answers:**
- **Tester Name:** Hailey Gilland
- **Event Name:** Love Like Lauren
- **Tester Experience:** managing the Love Like Lauren show
- **Specific Reference:** the Love Like Lauren show
- **Specific Compliment:** how you've built such a meaningful and well-organized show that honors Lauren's memory while bringing the automotive community together
- **Tester Type:** show managers
- **Specific Expertise:** Love Like Lauren and the unique challenges that come with organizing a memorial show
- **Specific Value Prop:** managing a show that balances honoring Lauren's legacy while creating an amazing experience for participants
- **Tester Events:** Love Like Lauren and any future events you organize
- **Closing Message:** I'd love to hear your thoughts, and please give my regards to everyone involved with Love Like Lauren - what you all do to honor her memory is truly special.

### Scenario 2: Car Club President
**Template:** Personalized Outreach Letter
**Questions & Answers:**
- **Tester Name:** John Smith
- **Event Name:** Monthly Club Meets
- **Tester Experience:** organizing monthly car club events
- **Specific Reference:** your club's monthly meets
- **Specific Compliment:** the consistent turnout and member engagement you achieve
- **Tester Type:** car club presidents
- **Specific Expertise:** club event coordination and member management
- **Specific Value Prop:** understanding the unique needs of car club events
- **Tester Events:** your club events
- **Closing Message:** Your experience with club management would be invaluable.

## 🛠 **Template System**

### Template Placeholders
Use `{{placeholder_name}}` in your templates. Examples:
- `{{tester_name}}` - Replaced with tester's name
- `{{event_name}}` - Replaced with their event name
- `{{current_date}}` - Replaced with selected date

### Question Types
- **text** - Single line text input
- **textarea** - Multi-line text input
- **email** - Email address input
- **phone** - Phone number input
- **date** - Date picker
- **select** - Dropdown selection
- **radio** - Radio button selection
- **checkbox** - Multiple checkbox selection

### Creating Custom Templates
1. Go to `/pdf-generator/templates`
2. Click "Create Template"
3. Define template content with placeholders
4. Add questions that will fill the placeholders
5. Save and test

## 📁 **File Structure**

```
/controllers/PDFGeneratorController.php    # Main controller
/models/PDFGeneratorModel.php              # Database operations
/views/admin/pdf_generator/
  ├── index.php                           # Template selection
  ├── questionnaire.php                   # Question form
  └── templates.php                       # Template management
/sql/pdf_generator_tables.sql             # Database schema
/uploads/generated_pdfs/                   # Generated PDF storage
/install/install_pdf_generator.php        # Installation script
```

## 🔧 **Database Tables**

### `pdf_templates`
- `id` - Template ID
- `name` - Template name
- `description` - Template description
- `template_content` - HTML content with placeholders
- `created_at` / `updated_at` - Timestamps

### `pdf_template_questions`
- `id` - Question ID
- `template_id` - Links to template
- `question_key` - Placeholder name
- `question_text` - Question displayed to user
- `question_type` - Input type (text, select, etc.)
- `options` - JSON array for select/radio/checkbox
- `required` - Whether question is required
- `sort_order` - Display order

## 🎨 **Customization**

### Styling PDFs
Modify the template content with HTML and inline CSS:
```html
<h1 style="color: #1338BE; text-align: center;">Your Title</h1>
<p style="font-size: 12px; color: #666;">Your content</p>
```

### Adding New Templates
1. Create template with placeholders
2. Define questions that fill placeholders
3. Test with sample data
4. Deploy to production

## 🔒 **Security Features**

- Admin-only access to PDF generator
- Input sanitization for all user data
- Secure file storage in `/uploads/generated_pdfs/`
- CSRF protection on forms
- HTML escaping in PDF content

## 📊 **Benefits**

### For Beta Tester Outreach:
- **Personalized Documents** - Each PDF is customized for the recipient
- **Professional Appearance** - Consistent branding and formatting
- **Time Savings** - Generate documents in seconds instead of manual editing
- **Consistency** - Ensure all documents contain required information
- **Scalability** - Easily create documents for multiple testers

### For Your Workflow:
- **Streamlined Process** - Simple questionnaire → PDF generation
- **Template Reuse** - Create once, use many times
- **Quality Control** - Consistent professional documents
- **Audit Trail** - Track what documents were generated when

## 🚀 **Next Steps**

1. **Install the system** using the installation script
2. **Test with sample data** to ensure everything works
3. **Customize templates** for your specific needs
4. **Generate your first PDFs** for beta tester outreach
5. **Expand templates** as needed for other use cases

This system transforms the manual process of creating personalized outreach documents into an automated, professional workflow that saves time while ensuring consistency and quality.
