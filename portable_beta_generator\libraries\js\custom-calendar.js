/**
 * Custom Calendar Implementation
 * 
 * A fully custom calendar implementation for the Events and Shows Management System.
 * This calendar provides month, week, day, and list views with advanced features.
 * 
 * Version: 1.0.0
 */

class CustomCalendar {
    /**
     * Constructor
     * 
     * @param {string} containerId - The ID of the container element
     * @param {Object} options - Calendar options
     */
    constructor(containerId, options = {}) {
        // Store container element
        this.container = document.getElementById(containerId);
        if (!this.container) {
            throw new Error(`Container element with ID "${containerId}" not found`);
        }

        // Default options
        this.defaultOptions = {
            defaultView: 'month',
            weekStartsOn: 0, // 0 = Sunday, 1 = Monday, etc.
            showWeekends: true,
            businessHoursStart: '09:00',
            businessHoursEnd: '17:00',
            timeFormat: '12', // 12 or 24
            dateFormat: 'MM/DD/YYYY',
            defaultEventDuration: 60, // minutes
            enableDragDrop: true,
            enableResize: true,
            defaultCalendarColor: '#3788d8',
            eventSources: [],
            eventClick: null,
            eventDrop: null,
            eventResize: null,
            dateSelect: null,
            loading: null,
            viewChange: null,
            dayHeaderFormat: 'short', // 'short', 'narrow', 'long'
            monthHeaderFormat: 'long', // 'numeric', '2-digit', 'short', 'long'
            timeSlotDuration: 30, // minutes
            snapDuration: 15, // minutes
            slotLabelInterval: 60, // minutes
            slotLabelFormat: 'h:mm a', // 'h:mm a', 'HH:mm'
            allDaySlot: true,
            allDayText: 'All Day',
            nowIndicator: true,
            scrollTime: '08:00:00',
            height: 'auto',
            aspectRatio: 1.35,
            firstHour: 8,
            minTime: '00:00:00',
            maxTime: '24:00:00',
            slotEventOverlap: true,
            eventLimit: true,
            eventLimitText: 'more',
            eventLimitClick: 'popover',
            navLinks: true,
            selectable: true,
            selectMirror: true,
            unselectAuto: true,
            unselectCancel: '',
            selectOverlap: true,
            selectConstraint: {},
            selectAllow: null,
            selectMinDistance: 0,
            editable: true,
            eventStartEditable: true,
            eventDurationEditable: true,
            eventOverlap: true,
            eventConstraint: {},
            eventAllow: null,
            longPressDelay: 1000,
            eventLongPressDelay: 1000,
            selectLongPressDelay: 1000
        };

        // Merge options
        this.options = { ...this.defaultOptions, ...options };

        // Current date and view
        this.currentDate = new Date();
        this.currentView = this.options.defaultView;

        // Events cache
        this.events = [];
        this.filteredEvents = [];

        // Selected calendars
        this.selectedCalendars = [];

        // Initialize calendar
        this.init();
    }

    /**
     * Initialize calendar
     */
    init() {
        // Create calendar structure
        this.createCalendarStructure();

        // Set initial view
        this.setView(this.currentView);

        // Fetch events
        this.fetchEvents();

        // Set up event listeners
        this.setupEventListeners();
    }

    /**
     * Create calendar structure
     */
    createCalendarStructure() {
        // Clear container
        this.container.innerHTML = '';
        this.container.classList.add('custom-calendar');

        // Create header
        this.header = document.createElement('div');
        this.header.className = 'calendar-header';
        this.container.appendChild(this.header);

        // Create title
        this.title = document.createElement('h2');
        this.title.className = 'calendar-title';
        this.header.appendChild(this.title);

        // Create main calendar grid
        this.calendarGrid = document.createElement('div');
        this.calendarGrid.className = 'calendar-grid';
        this.container.appendChild(this.calendarGrid);

        // Create event container for day/week view
        this.eventContainer = document.createElement('div');
        this.eventContainer.className = 'calendar-events';
        this.container.appendChild(this.eventContainer);

        // Create event list for list view
        this.eventList = document.createElement('div');
        this.eventList.className = 'calendar-event-list';
        this.container.appendChild(this.eventList);

        // Create loading indicator
        this.loadingIndicator = document.createElement('div');
        this.loadingIndicator.className = 'calendar-loading';
        this.loadingIndicator.innerHTML = '<div class="spinner"></div><span>Loading...</span>';
        this.loadingIndicator.style.display = 'none';
        this.container.appendChild(this.loadingIndicator);
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Event delegation for calendar cells
        this.calendarGrid.addEventListener('click', (e) => {
            // Handle day cell click
            if (e.target.classList.contains('calendar-day') || e.target.closest('.calendar-day')) {
                const cell = e.target.classList.contains('calendar-day') ? e.target : e.target.closest('.calendar-day');
                const date = new Date(cell.dataset.date);
                
                if (this.options.dateSelect) {
                    this.options.dateSelect({
                        start: date,
                        end: new Date(date.getTime() + 24 * 60 * 60 * 1000),
                        allDay: true
                    });
                }
            }

            // Handle event click
            if (e.target.classList.contains('calendar-event') || e.target.closest('.calendar-event')) {
                const eventEl = e.target.classList.contains('calendar-event') ? e.target : e.target.closest('.calendar-event');
                const eventId = eventEl.dataset.eventId;
                const event = this.events.find(e => e.id == eventId);
                
                if (event && this.options.eventClick) {
                    this.options.eventClick({ event });
                }
            }
        });

        // Event delegation for event list
        this.eventList.addEventListener('click', (e) => {
            // Handle event click in list view
            if (e.target.classList.contains('list-event') || e.target.closest('.list-event')) {
                const eventEl = e.target.classList.contains('list-event') ? e.target : e.target.closest('.list-event');
                const eventId = eventEl.dataset.eventId;
                const event = this.events.find(e => e.id == eventId);
                
                if (event && this.options.eventClick) {
                    this.options.eventClick({ event });
                }
            }
        });

        // Drag and drop for events
        if (this.options.enableDragDrop) {
            this.setupDragAndDrop();
        }
    }

    /**
     * Set up drag and drop functionality
     */
    setupDragAndDrop() {
        // We'll implement this with a more advanced library or custom implementation
        // For now, we'll use a simple implementation
        this.container.addEventListener('mousedown', (e) => {
            if (e.target.classList.contains('calendar-event') || e.target.closest('.calendar-event')) {
                const eventEl = e.target.classList.contains('calendar-event') ? e.target : e.target.closest('.calendar-event');
                const eventId = eventEl.dataset.eventId;
                const event = this.events.find(e => e.id == eventId);
                
                if (!event || !this.options.editable) return;
                
                // Start drag
                const startX = e.clientX;
                const startY = e.clientY;
                const startTop = eventEl.offsetTop;
                const startLeft = eventEl.offsetLeft;
                const startDate = new Date(event.start);
                
                // Create ghost element
                const ghost = eventEl.cloneNode(true);
                ghost.classList.add('dragging');
                ghost.style.position = 'absolute';
                ghost.style.top = startTop + 'px';
                ghost.style.left = startLeft + 'px';
                ghost.style.width = eventEl.offsetWidth + 'px';
                ghost.style.zIndex = 1000;
                ghost.style.opacity = 0.7;
                document.body.appendChild(ghost);
                
                // Hide original
                eventEl.style.opacity = 0.3;
                
                // Track mouse move
                const moveHandler = (e) => {
                    ghost.style.top = (startTop + e.clientY - startY) + 'px';
                    ghost.style.left = (startLeft + e.clientX - startX) + 'px';
                };
                
                // Handle drop
                const upHandler = (e) => {
                    document.removeEventListener('mousemove', moveHandler);
                    document.removeEventListener('mouseup', upHandler);
                    
                    // Remove ghost
                    document.body.removeChild(ghost);
                    
                    // Show original
                    eventEl.style.opacity = 1;
                    
                    // Find drop target
                    const target = document.elementFromPoint(e.clientX, e.clientY);
                    const dayCell = target.classList.contains('calendar-day') ? target : target.closest('.calendar-day');
                    
                    if (dayCell) {
                        const newDate = new Date(dayCell.dataset.date);
                        const diff = newDate.getTime() - startDate.getTime();
                        
                        // Update event dates
                        const newEvent = { ...event };
                        newEvent.start = new Date(new Date(event.start).getTime() + diff);
                        newEvent.end = event.end ? new Date(new Date(event.end).getTime() + diff) : null;
                        
                        // Call eventDrop callback
                        if (this.options.eventDrop) {
                            this.options.eventDrop({
                                event: newEvent,
                                oldEvent: event,
                                revert: () => {
                                    // Revert is handled by refetching events
                                    this.fetchEvents();
                                }
                            });
                        }
                    }
                };
                
                document.addEventListener('mousemove', moveHandler);
                document.addEventListener('mouseup', upHandler);
            }
        });
    }

    /**
     * Set calendar view
     * 
     * @param {string} view - The view to set ('month', 'week', 'day', 'list')
     */
    setView(view) {
        this.currentView = view;
        
        // Update container class
        this.container.className = 'custom-calendar';
        this.container.classList.add(`calendar-view-${view}`);
        
        // Hide/show appropriate containers
        this.calendarGrid.style.display = ['month', 'week', 'day'].includes(view) ? 'grid' : 'none';
        this.eventContainer.style.display = ['week', 'day'].includes(view) ? 'block' : 'none';
        this.eventList.style.display = view === 'list' ? 'block' : 'none';
        
        // Render view
        this.render();
        
        // Call viewChange callback
        if (this.options.viewChange) {
            this.options.viewChange({ view });
        }
    }

    /**
     * Navigate to previous period
     */
    prev() {
        switch (this.currentView) {
            case 'month':
                this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1);
                break;
            case 'week':
                this.currentDate = new Date(this.currentDate.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case 'day':
                this.currentDate = new Date(this.currentDate.getTime() - 24 * 60 * 60 * 1000);
                break;
            case 'list':
                this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1);
                break;
        }
        
        this.render();
    }

    /**
     * Navigate to next period
     */
    next() {
        switch (this.currentView) {
            case 'month':
                this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1);
                break;
            case 'week':
                this.currentDate = new Date(this.currentDate.getTime() + 7 * 24 * 60 * 60 * 1000);
                break;
            case 'day':
                this.currentDate = new Date(this.currentDate.getTime() + 24 * 60 * 60 * 1000);
                break;
            case 'list':
                this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1);
                break;
        }
        
        this.render();
    }

    /**
     * Navigate to today
     */
    today() {
        this.currentDate = new Date();
        this.render();
    }

    /**
     * Render calendar
     */
    render() {
        // Update title
        this.updateTitle();
        
        // Render appropriate view
        switch (this.currentView) {
            case 'month':
                this.renderMonthView();
                break;
            case 'week':
                this.renderWeekView();
                break;
            case 'day':
                this.renderDayView();
                break;
            case 'list':
                this.renderListView();
                break;
        }
    }

    /**
     * Update calendar title
     */
    updateTitle() {
        const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        
        let title = '';
        
        switch (this.currentView) {
            case 'month':
                title = `${months[this.currentDate.getMonth()]} ${this.currentDate.getFullYear()}`;
                break;
            case 'week':
                const weekStart = this.getWeekStart(this.currentDate);
                const weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000);
                
                if (weekStart.getMonth() === weekEnd.getMonth()) {
                    title = `${months[weekStart.getMonth()]} ${weekStart.getDate()} - ${weekEnd.getDate()}, ${weekStart.getFullYear()}`;
                } else if (weekStart.getFullYear() === weekEnd.getFullYear()) {
                    title = `${months[weekStart.getMonth()]} ${weekStart.getDate()} - ${months[weekEnd.getMonth()]} ${weekEnd.getDate()}, ${weekStart.getFullYear()}`;
                } else {
                    title = `${months[weekStart.getMonth()]} ${weekStart.getDate()}, ${weekStart.getFullYear()} - ${months[weekEnd.getMonth()]} ${weekEnd.getDate()}, ${weekEnd.getFullYear()}`;
                }
                break;
            case 'day':
                title = `${days[this.currentDate.getDay()]}, ${months[this.currentDate.getMonth()]} ${this.currentDate.getDate()}, ${this.currentDate.getFullYear()}`;
                break;
            case 'list':
                title = `${months[this.currentDate.getMonth()]} ${this.currentDate.getFullYear()}`;
                break;
        }
        
        this.title.textContent = title;
    }

    /**
     * Render month view
     */
    renderMonthView() {
        // Clear grid
        this.calendarGrid.innerHTML = '';
        
        // Set grid template
        this.calendarGrid.className = 'calendar-grid month-view';
        
        // Get month details
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        
        // Adjust for week start day
        let startOffset = firstDay.getDay() - this.options.weekStartsOn;
        if (startOffset < 0) startOffset += 7;
        
        // Create day headers
        const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        for (let i = 0; i < 7; i++) {
            const dayIndex = (i + this.options.weekStartsOn) % 7;
            const dayHeader = document.createElement('div');
            dayHeader.className = 'calendar-day-header';
            dayHeader.textContent = dayNames[dayIndex];
            this.calendarGrid.appendChild(dayHeader);
        }
        
        // Create day cells
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        // Previous month days
        for (let i = 0; i < startOffset; i++) {
            const day = new Date(year, month, 1 - startOffset + i);
            this.createDayCell(day, 'prev-month');
        }
        
        // Current month days
        for (let i = 1; i <= daysInMonth; i++) {
            const day = new Date(year, month, i);
            this.createDayCell(day, 'current-month');
        }
        
        // Next month days
        const totalCells = Math.ceil((daysInMonth + startOffset) / 7) * 7;
        const remainingCells = totalCells - (daysInMonth + startOffset);
        
        for (let i = 1; i <= remainingCells; i++) {
            const day = new Date(year, month + 1, i);
            this.createDayCell(day, 'next-month');
        }
        
        // Render events
        this.renderMonthEvents();
    }

    /**
     * Create a day cell for month view
     * 
     * @param {Date} date - The date for the cell
     * @param {string} className - Additional class name
     */
    createDayCell(date, className) {
        const cell = document.createElement('div');
        cell.className = `calendar-day ${className}`;
        cell.dataset.date = date.toISOString().split('T')[0];
        
        // Check if today
        const today = new Date();

        // Use timezone helper if available for consistent comparison
        let isToday;
        if (window.TimezoneHelper && window.TimezoneHelper.isSameDay) {
            isToday = window.TimezoneHelper.isSameDay(date, today);
        } else {
            // Fallback: Compare date components in local timezone
            isToday = date.getDate() === today.getDate() &&
                     date.getMonth() === today.getMonth() &&
                     date.getFullYear() === today.getFullYear();
        }

        if (isToday) {
            cell.classList.add('today');
        }
        
        // Check if weekend
        if (date.getDay() === 0 || date.getDay() === 6) {
            cell.classList.add('weekend');
        }
        
        // Add day number
        const dayNumber = document.createElement('div');
        dayNumber.className = 'day-number';
        dayNumber.textContent = date.getDate();
        cell.appendChild(dayNumber);
        
        // Add events container
        const eventsContainer = document.createElement('div');
        eventsContainer.className = 'day-events';
        cell.appendChild(eventsContainer);
        
        this.calendarGrid.appendChild(cell);
    }

    /**
     * Render events for month view
     */
    renderMonthEvents() {
        // Get visible date range
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        
        // Adjust for week start and end
        let startOffset = firstDay.getDay() - this.options.weekStartsOn;
        if (startOffset < 0) startOffset += 7;
        
        const visibleStart = new Date(year, month, 1 - startOffset);
        const totalDays = Math.ceil((lastDay.getDate() + startOffset) / 7) * 7;
        const visibleEnd = new Date(year, month, totalDays - startOffset);
        
        // Filter events for visible range
        const visibleEvents = this.filteredEvents.filter(event => {
            const eventStart = new Date(event.start);
            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);
            
            return eventStart < visibleEnd && eventEnd >= visibleStart;
        });
        
        // Sort events by duration (longer events first)
        visibleEvents.sort((a, b) => {
            const aStart = new Date(a.start);
            const aEnd = a.end ? new Date(a.end) : new Date(aStart.getTime() + this.options.defaultEventDuration * 60 * 1000);
            const aDuration = aEnd.getTime() - aStart.getTime();
            
            const bStart = new Date(b.start);
            const bEnd = b.end ? new Date(b.end) : new Date(bStart.getTime() + this.options.defaultEventDuration * 60 * 1000);
            const bDuration = bEnd.getTime() - bStart.getTime();
            
            return bDuration - aDuration;
        });
        
        // Render each event
        visibleEvents.forEach(event => {
            const eventStart = new Date(event.start);
            let eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);
            
            // Adjust all-day events
            if (event.allDay) {
                eventStart.setHours(0, 0, 0, 0);
                eventEnd.setHours(23, 59, 59, 999);
            }
            
            // Clip to visible range
            const displayStart = new Date(Math.max(eventStart.getTime(), visibleStart.getTime()));
            const displayEnd = new Date(Math.min(eventEnd.getTime(), visibleEnd.getTime()));
            
            // Calculate day span
            const daySpan = Math.ceil((displayEnd.getTime() - displayStart.getTime()) / (24 * 60 * 60 * 1000));
            
            // Find cells for each day
            for (let i = 0; i < daySpan; i++) {
                const day = new Date(displayStart.getTime() + i * 24 * 60 * 60 * 1000);
                const dateStr = day.toISOString().split('T')[0];
                const cell = this.calendarGrid.querySelector(`.calendar-day[data-date="${dateStr}"]`);
                
                if (cell) {
                    const eventsContainer = cell.querySelector('.day-events');
                    
                    // Check if we already have too many events
                    if (eventsContainer.children.length >= 3) {
                        // Check if we already have a "more" indicator
                        if (!eventsContainer.querySelector('.more-events')) {
                            const moreIndicator = document.createElement('div');
                            moreIndicator.className = 'more-events';
                            moreIndicator.textContent = '+ more';
                            eventsContainer.appendChild(moreIndicator);
                        }
                        continue;
                    }
                    
                    // Create event element
                    const eventEl = document.createElement('div');
                    eventEl.className = 'calendar-event';
                    eventEl.dataset.eventId = event.id;
                    
                    // Set event color
                    const color = event.color || event.backgroundColor || event.extendedProps?.calendar_color || this.options.defaultCalendarColor;
                    eventEl.style.backgroundColor = color;
                    eventEl.style.borderColor = color;
                    
                    // Set event title
                    eventEl.textContent = event.title;
                    
                    // Add to container
                    eventsContainer.appendChild(eventEl);
                }
            }
        });
    }

    /**
     * Render week view
     */
    renderWeekView() {
        // Clear grid
        this.calendarGrid.innerHTML = '';
        this.eventContainer.innerHTML = '';
        
        // Set grid template
        this.calendarGrid.className = 'calendar-grid week-view';
        
        // Get week start and end
        const weekStart = this.getWeekStart(this.currentDate);
        const weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000);
        
        // Create time column
        const timeColumn = document.createElement('div');
        timeColumn.className = 'time-column';
        this.calendarGrid.appendChild(timeColumn);
        
        // Create time slots
        const startHour = parseInt(this.options.minTime.split(':')[0]);
        const endHour = parseInt(this.options.maxTime.split(':')[0]);
        
        for (let hour = startHour; hour < endHour; hour++) {
            const timeSlot = document.createElement('div');
            timeSlot.className = 'time-slot';
            
            // Format hour based on time format
            let displayHour = hour;
            let ampm = '';
            
            if (this.options.timeFormat === '12') {
                ampm = hour >= 12 ? 'PM' : 'AM';
                displayHour = hour % 12 || 12;
            }
            
            timeSlot.textContent = `${displayHour}${ampm ? ' ' + ampm : ''}`;
            timeColumn.appendChild(timeSlot);
        }
        
        // Create day columns
        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        for (let i = 0; i < 7; i++) {
            const day = new Date(weekStart.getTime() + i * 24 * 60 * 60 * 1000);
            const dayColumn = document.createElement('div');
            dayColumn.className = 'day-column';
            dayColumn.dataset.date = day.toISOString().split('T')[0];
            
            // Check if today
            let isToday;
            if (window.TimezoneHelper && window.TimezoneHelper.isSameDay) {
                isToday = window.TimezoneHelper.isSameDay(day, today);
            } else {
                // Fallback: Compare date components in local timezone
                isToday = day.getDate() === today.getDate() &&
                         day.getMonth() === today.getMonth() &&
                         day.getFullYear() === today.getFullYear();
            }

            if (isToday) {
                dayColumn.classList.add('today');
            }
            
            // Check if weekend
            if (day.getDay() === 0 || day.getDay() === 6) {
                dayColumn.classList.add('weekend');
            }
            
            // Add day header
            const dayHeader = document.createElement('div');
            dayHeader.className = 'day-header';
            
            const dayName = document.createElement('div');
            dayName.className = 'day-name';
            dayName.textContent = days[day.getDay()];
            
            const dayNumber = document.createElement('div');
            dayNumber.className = 'day-number';
            dayNumber.textContent = day.getDate();
            
            dayHeader.appendChild(dayName);
            dayHeader.appendChild(dayNumber);
            dayColumn.appendChild(dayHeader);
            
            // Add time grid
            for (let hour = startHour; hour < endHour; hour++) {
                const hourCell = document.createElement('div');
                hourCell.className = 'hour-cell';
                hourCell.dataset.hour = hour;
                dayColumn.appendChild(hourCell);
            }
            
            this.calendarGrid.appendChild(dayColumn);
        }
        
        // Render events
        this.renderWeekEvents();
    }

    /**
     * Render events for week view
     */
    renderWeekEvents() {
        // Get visible date range
        const weekStart = this.getWeekStart(this.currentDate);
        const weekEnd = new Date(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000);
        
        // Filter events for visible range
        const visibleEvents = this.filteredEvents.filter(event => {
            const eventStart = new Date(event.start);
            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);
            
            return eventStart < weekEnd && eventEnd >= weekStart;
        });
        
        // Create all-day events container
        const allDayContainer = document.createElement('div');
        allDayContainer.className = 'all-day-container';
        this.eventContainer.appendChild(allDayContainer);
        
        // Separate all-day events
        const allDayEvents = visibleEvents.filter(event => event.allDay);
        const timeEvents = visibleEvents.filter(event => !event.allDay);
        
        // Render all-day events
        allDayEvents.forEach(event => {
            const eventStart = new Date(event.start);
            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + 24 * 60 * 60 * 1000);
            
            // Clip to visible range
            const displayStart = new Date(Math.max(eventStart.getTime(), weekStart.getTime()));
            const displayEnd = new Date(Math.min(eventEnd.getTime(), weekEnd.getTime()));
            
            // Calculate day span and offset
            const startOffset = Math.floor((displayStart.getTime() - weekStart.getTime()) / (24 * 60 * 60 * 1000));
            const daySpan = Math.ceil((displayEnd.getTime() - displayStart.getTime()) / (24 * 60 * 60 * 1000));
            
            // Create event element
            const eventEl = document.createElement('div');
            eventEl.className = 'calendar-event all-day-event';
            eventEl.dataset.eventId = event.id;
            
            // Set event color
            const color = event.color || event.backgroundColor || event.extendedProps?.calendar_color || this.options.defaultCalendarColor;
            eventEl.style.backgroundColor = color;
            eventEl.style.borderColor = color;
            
            // Set event position
            eventEl.style.gridColumn = `${startOffset + 1} / span ${daySpan}`;
            
            // Set event title
            eventEl.textContent = event.title;
            
            // Add to container
            allDayContainer.appendChild(eventEl);
        });
        
        // Render time-based events
        timeEvents.forEach(event => {
            const eventStart = new Date(event.start);
            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);
            
            // Clip to visible range
            const displayStart = new Date(Math.max(eventStart.getTime(), weekStart.getTime()));
            const displayEnd = new Date(Math.min(eventEnd.getTime(), weekEnd.getTime()));
            
            // Calculate day offset
            const dayOffset = Math.floor((displayStart.getTime() - weekStart.getTime()) / (24 * 60 * 60 * 1000));
            
            // Find day column
            const dayColumn = this.calendarGrid.querySelectorAll('.day-column')[dayOffset];
            
            if (dayColumn) {
                // Calculate time position
                const startHour = displayStart.getHours() + displayStart.getMinutes() / 60;
                const endHour = displayEnd.getHours() + displayEnd.getMinutes() / 60;
                const duration = endHour - startHour;
                
                // Create event element
                const eventEl = document.createElement('div');
                eventEl.className = 'calendar-event time-event';
                eventEl.dataset.eventId = event.id;
                
                // Set event color
                const color = event.color || event.backgroundColor || event.extendedProps?.calendar_color || this.options.defaultCalendarColor;
                eventEl.style.backgroundColor = color;
                eventEl.style.borderColor = color;
                
                // Set event position
                const minHour = parseInt(this.options.minTime.split(':')[0]);
                const top = (startHour - minHour) * 60; // 60px per hour
                const height = duration * 60;
                
                eventEl.style.top = `${top}px`;
                eventEl.style.height = `${height}px`;
                eventEl.style.left = `${dayOffset * 14.28}%`; // 7 days = 14.28% per day
                eventEl.style.width = '14.28%';
                
                // Set event title
                const eventTitle = document.createElement('div');
                eventTitle.className = 'event-title';
                eventTitle.textContent = event.title;
                eventEl.appendChild(eventTitle);
                
                // Set event time
                const eventTime = document.createElement('div');
                eventTime.className = 'event-time';
                
                // Format time based on time format
                let startTimeStr = this.formatTime(displayStart);
                let endTimeStr = this.formatTime(displayEnd);
                
                eventTime.textContent = `${startTimeStr} - ${endTimeStr}`;
                eventEl.appendChild(eventTime);
                
                // Add to container
                this.eventContainer.appendChild(eventEl);
            }
        });
    }

    /**
     * Render day view
     */
    renderDayView() {
        // Clear grid
        this.calendarGrid.innerHTML = '';
        this.eventContainer.innerHTML = '';
        
        // Set grid template
        this.calendarGrid.className = 'calendar-grid day-view';
        
        // Create time column
        const timeColumn = document.createElement('div');
        timeColumn.className = 'time-column';
        this.calendarGrid.appendChild(timeColumn);
        
        // Create time slots
        const startHour = parseInt(this.options.minTime.split(':')[0]);
        const endHour = parseInt(this.options.maxTime.split(':')[0]);
        
        for (let hour = startHour; hour < endHour; hour++) {
            const timeSlot = document.createElement('div');
            timeSlot.className = 'time-slot';
            
            // Format hour based on time format
            let displayHour = hour;
            let ampm = '';
            
            if (this.options.timeFormat === '12') {
                ampm = hour >= 12 ? 'PM' : 'AM';
                displayHour = hour % 12 || 12;
            }
            
            timeSlot.textContent = `${displayHour}${ampm ? ' ' + ampm : ''}`;
            timeColumn.appendChild(timeSlot);
        }
        
        // Create day column
        const dayColumn = document.createElement('div');
        dayColumn.className = 'day-column full-width';
        dayColumn.dataset.date = this.currentDate.toISOString().split('T')[0];
        
        // Check if today
        const today = new Date();

        let isToday;
        if (window.TimezoneHelper && window.TimezoneHelper.isSameDay) {
            isToday = window.TimezoneHelper.isSameDay(this.currentDate, today);
        } else {
            // Fallback: Compare date components in local timezone
            isToday = this.currentDate.getDate() === today.getDate() &&
                     this.currentDate.getMonth() === today.getMonth() &&
                     this.currentDate.getFullYear() === today.getFullYear();
        }

        if (isToday) {
            dayColumn.classList.add('today');
        }
        
        // Check if weekend
        if (this.currentDate.getDay() === 0 || this.currentDate.getDay() === 6) {
            dayColumn.classList.add('weekend');
        }
        
        // Add time grid
        for (let hour = startHour; hour < endHour; hour++) {
            const hourCell = document.createElement('div');
            hourCell.className = 'hour-cell';
            hourCell.dataset.hour = hour;
            dayColumn.appendChild(hourCell);
        }
        
        this.calendarGrid.appendChild(dayColumn);
        
        // Render events
        this.renderDayEvents();
    }

    /**
     * Render events for day view
     */
    renderDayEvents() {
        // Get visible date range
        const dayStart = new Date(this.currentDate);
        dayStart.setHours(0, 0, 0, 0);
        const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);
        
        // Filter events for visible range
        const visibleEvents = this.filteredEvents.filter(event => {
            const eventStart = new Date(event.start);
            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);
            
            return eventStart < dayEnd && eventEnd >= dayStart;
        });
        
        // Create all-day events container
        const allDayContainer = document.createElement('div');
        allDayContainer.className = 'all-day-container';
        this.eventContainer.appendChild(allDayContainer);
        
        // Separate all-day events
        const allDayEvents = visibleEvents.filter(event => event.allDay);
        const timeEvents = visibleEvents.filter(event => !event.allDay);
        
        // Render all-day events
        allDayEvents.forEach(event => {
            // Create event element
            const eventEl = document.createElement('div');
            eventEl.className = 'calendar-event all-day-event';
            eventEl.dataset.eventId = event.id;
            
            // Set event color
            const color = event.color || event.backgroundColor || event.extendedProps?.calendar_color || this.options.defaultCalendarColor;
            eventEl.style.backgroundColor = color;
            eventEl.style.borderColor = color;
            
            // Set event title
            eventEl.textContent = event.title;
            
            // Add to container
            allDayContainer.appendChild(eventEl);
        });
        
        // Render time-based events
        timeEvents.forEach(event => {
            const eventStart = new Date(event.start);
            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);
            
            // Clip to visible range
            const displayStart = new Date(Math.max(eventStart.getTime(), dayStart.getTime()));
            const displayEnd = new Date(Math.min(eventEnd.getTime(), dayEnd.getTime()));
            
            // Calculate time position
            const startHour = displayStart.getHours() + displayStart.getMinutes() / 60;
            const endHour = displayEnd.getHours() + displayEnd.getMinutes() / 60;
            const duration = endHour - startHour;
            
            // Create event element
            const eventEl = document.createElement('div');
            eventEl.className = 'calendar-event time-event';
            eventEl.dataset.eventId = event.id;
            
            // Set event color
            const color = event.color || event.backgroundColor || event.extendedProps?.calendar_color || this.options.defaultCalendarColor;
            eventEl.style.backgroundColor = color;
            eventEl.style.borderColor = color;
            
            // Set event position
            const minHour = parseInt(this.options.minTime.split(':')[0]);
            const top = (startHour - minHour) * 60; // 60px per hour
            const height = duration * 60;
            
            eventEl.style.top = `${top}px`;
            eventEl.style.height = `${height}px`;
            eventEl.style.width = '100%';
            
            // Set event title
            const eventTitle = document.createElement('div');
            eventTitle.className = 'event-title';
            eventTitle.textContent = event.title;
            eventEl.appendChild(eventTitle);
            
            // Set event time
            const eventTime = document.createElement('div');
            eventTime.className = 'event-time';
            
            // Format time based on time format
            let startTimeStr = this.formatTime(displayStart);
            let endTimeStr = this.formatTime(displayEnd);
            
            eventTime.textContent = `${startTimeStr} - ${endTimeStr}`;
            eventEl.appendChild(eventTime);
            
            // Add to container
            this.eventContainer.appendChild(eventEl);
        });
    }

    /**
     * Render list view
     */
    renderListView() {
        // Clear list
        this.eventList.innerHTML = '';
        
        // Get visible date range
        const monthStart = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
        const monthEnd = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);
        
        // Filter events for visible range
        const visibleEvents = this.filteredEvents.filter(event => {
            const eventStart = new Date(event.start);
            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);
            
            return eventStart <= monthEnd && eventEnd >= monthStart;
        });
        
        // Sort events by start date
        visibleEvents.sort((a, b) => {
            return new Date(a.start).getTime() - new Date(b.start).getTime();
        });
        
        // Group events by day
        const eventsByDay = {};
        
        visibleEvents.forEach(event => {
            const eventStart = new Date(event.start);
            const dateStr = eventStart.toISOString().split('T')[0];
            
            if (!eventsByDay[dateStr]) {
                eventsByDay[dateStr] = [];
            }
            
            eventsByDay[dateStr].push(event);
        });
        
        // Render events by day
        if (Object.keys(eventsByDay).length === 0) {
            const noEvents = document.createElement('div');
            noEvents.className = 'no-events';
            noEvents.textContent = 'No events to display';
            this.eventList.appendChild(noEvents);
        } else {
            for (const dateStr in eventsByDay) {
                const date = new Date(dateStr);
                
                // Create day header
                const dayHeader = document.createElement('div');
                dayHeader.className = 'list-day-header';
                
                // Format date
                const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
                
                dayHeader.textContent = `${dayNames[date.getDay()]}, ${monthNames[date.getMonth()]} ${date.getDate()}`;
                this.eventList.appendChild(dayHeader);
                
                // Create events for this day
                eventsByDay[dateStr].forEach(event => {
                    const eventEl = document.createElement('div');
                    eventEl.className = 'list-event';
                    eventEl.dataset.eventId = event.id;
                    
                    // Set event color
                    const color = event.color || event.backgroundColor || event.extendedProps?.calendar_color || this.options.defaultCalendarColor;
                    
                    // Create color dot
                    const colorDot = document.createElement('div');
                    colorDot.className = 'event-color-dot';
                    colorDot.style.backgroundColor = color;
                    eventEl.appendChild(colorDot);
                    
                    // Create event content
                    const eventContent = document.createElement('div');
                    eventContent.className = 'event-content';
                    
                    // Create event title
                    const eventTitle = document.createElement('div');
                    eventTitle.className = 'event-title';
                    eventTitle.textContent = event.title;
                    eventContent.appendChild(eventTitle);
                    
                    // Create event time
                    const eventTime = document.createElement('div');
                    eventTime.className = 'event-time';
                    
                    const eventStart = new Date(event.start);
                    const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);
                    
                    if (event.allDay) {
                        eventTime.textContent = 'All day';
                    } else {
                        // Format time based on time format
                        let startTimeStr = this.formatTime(eventStart);
                        let endTimeStr = this.formatTime(eventEnd);
                        
                        eventTime.textContent = `${startTimeStr} - ${endTimeStr}`;
                    }
                    
                    eventContent.appendChild(eventTime);
                    
                    // Create event location if available
                    if (event.extendedProps && event.extendedProps.location) {
                        const eventLocation = document.createElement('div');
                        eventLocation.className = 'event-location';
                        eventLocation.textContent = event.extendedProps.location;
                        eventContent.appendChild(eventLocation);
                    }
                    
                    // Create event calendar if available
                    if (event.extendedProps && event.extendedProps.calendar_name) {
                        const eventCalendar = document.createElement('div');
                        eventCalendar.className = 'event-calendar';
                        eventCalendar.textContent = event.extendedProps.calendar_name;
                        eventContent.appendChild(eventCalendar);
                    }
                    
                    eventEl.appendChild(eventContent);
                    this.eventList.appendChild(eventEl);
                });
            }
        }
    }

    /**
     * Get the start of the week containing the given date
     * 
     * @param {Date} date - The date
     * @return {Date} The start of the week
     */
    getWeekStart(date) {
        const day = date.getDay();
        const diff = (day - this.options.weekStartsOn + 7) % 7;
        return new Date(date.getFullYear(), date.getMonth(), date.getDate() - diff);
    }

    /**
     * Format time based on time format setting
     * 
     * @param {Date} date - The date to format
     * @return {string} Formatted time string
     */
    formatTime(date) {
        if (this.options.timeFormat === '12') {
            const hours = date.getHours();
            const minutes = date.getMinutes();
            const ampm = hours >= 12 ? 'PM' : 'AM';
            const displayHours = hours % 12 || 12;
            
            return `${displayHours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
        } else {
            return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
        }
    }

    /**
     * Fetch events from all sources
     */
    fetchEvents() {
        // Show loading indicator
        this.showLoading(true);
        
        // Call loading callback
        if (this.options.loading) {
            this.options.loading(true);
        }
        
        // Clear events
        this.events = [];
        
        // Create promises for all event sources
        const promises = this.options.eventSources.map(source => {
            if (typeof source === 'string') {
                // URL source
                return fetch(source)
                    .then(response => response.json())
                    .then(events => events);
            } else if (typeof source === 'function') {
                // Function source
                return Promise.resolve(source());
            } else if (Array.isArray(source)) {
                // Array source
                return Promise.resolve(source);
            } else if (typeof source === 'object' && source.url) {
                // Object source with URL
                const params = new URLSearchParams();
                
                // Add start and end parameters
                const rangeStart = this.getRangeStart();
                const rangeEnd = this.getRangeEnd();
                
                params.append('start', rangeStart.toISOString());
                params.append('end', rangeEnd.toISOString());
                
                // Add extra parameters
                if (source.extraParams) {
                    for (const key in source.extraParams) {
                        params.append(key, source.extraParams[key]);
                    }
                }
                
                // Build URL
                const url = `${source.url}${source.url.includes('?') ? '&' : '?'}${params.toString()}`;
                
                // Fetch events
                return fetch(url)
                    .then(response => response.json())
                    .then(events => events);
            }
            
            return Promise.resolve([]);
        });
        
        // Wait for all promises to resolve
        Promise.all(promises)
            .then(results => {
                // Flatten results
                this.events = results.flat();
                
                // Filter events
                this.filterEvents();
                
                // Render calendar
                this.render();
                
                // Hide loading indicator
                this.showLoading(false);
                
                // Call loading callback
                if (this.options.loading) {
                    this.options.loading(false);
                }
            })
            .catch(error => {
                console.error('Error fetching events:', error);
                
                // Hide loading indicator
                this.showLoading(false);
                
                // Call loading callback
                if (this.options.loading) {
                    this.options.loading(false);
                }
            });
    }

    /**
     * Filter events based on selected calendars
     */
    filterEvents() {
        if (this.selectedCalendars.length === 0) {
            this.filteredEvents = this.events;
        } else {
            this.filteredEvents = this.events.filter(event => {
                const calendarId = event.extendedProps?.calendar_id;
                return this.selectedCalendars.includes(calendarId);
            });
        }
    }

    /**
     * Get the start of the visible range
     * 
     * @return {Date} The start of the visible range
     */
    getRangeStart() {
        switch (this.currentView) {
            case 'month':
                const monthStart = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
                const startOffset = monthStart.getDay() - this.options.weekStartsOn;
                return new Date(monthStart.getFullYear(), monthStart.getMonth(), 1 - (startOffset < 0 ? startOffset + 7 : startOffset));
            
            case 'week':
                return this.getWeekStart(this.currentDate);
            
            case 'day':
                return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), this.currentDate.getDate());
            
            case 'list':
                return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
        }
    }

    /**
     * Get the end of the visible range
     * 
     * @return {Date} The end of the visible range
     */
    getRangeEnd() {
        switch (this.currentView) {
            case 'month':
                const monthStart = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
                const monthEnd = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);
                const startOffset = monthStart.getDay() - this.options.weekStartsOn;
                const endOffset = 6 - (monthEnd.getDay() - this.options.weekStartsOn);
                return new Date(monthEnd.getFullYear(), monthEnd.getMonth(), monthEnd.getDate() + (endOffset < 0 ? endOffset + 7 : endOffset));
            
            case 'week':
                const weekStart = this.getWeekStart(this.currentDate);
                return new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate() + 7);
            
            case 'day':
                return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), this.currentDate.getDate() + 1);
            
            case 'list':
                return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);
        }
    }

    /**
     * Show or hide loading indicator
     * 
     * @param {boolean} show - Whether to show or hide the loading indicator
     */
    showLoading(show) {
        this.loadingIndicator.style.display = show ? 'flex' : 'none';
    }

    /**
     * Set selected calendars
     * 
     * @param {Array} calendarIds - Array of calendar IDs
     */
    setSelectedCalendars(calendarIds) {
        this.selectedCalendars = calendarIds;
        this.filterEvents();
        this.render();
    }

    /**
     * Add event source
     * 
     * @param {string|Object|Array|Function} source - Event source
     */
    addEventSource(source) {
        this.options.eventSources.push(source);
        this.fetchEvents();
    }

    /**
     * Remove event source
     * 
     * @param {string|Object|Array|Function} source - Event source to remove
     */
    removeEventSource(source) {
        const index = this.options.eventSources.indexOf(source);
        if (index !== -1) {
            this.options.eventSources.splice(index, 1);
            this.fetchEvents();
        }
    }

    /**
     * Refetch events
     */
    refetchEvents() {
        this.fetchEvents();
    }
}