# Facebook SDK Integration

This directory contains the Facebook SDK for PHP integration.

## Installation

To install the Facebook SDK, run the following command from the project root:

```bash
composer require facebook/graph-sdk
```

This will install the Facebook SDK in the vendor directory.

## Configuration

Make sure to update the Facebook App ID and App Secret in the config.php file:

```php
// Facebook OAuth settings
define('FB_APP_ID', 'your-app-id');
define('FB_APP_SECRET', 'your-app-secret');
define('FB_REDIRECT_URI', BASE_URL . '/auth/facebook-callback');
```

## Usage

The Facebook SDK is used in the AuthController to handle Facebook login and authentication.