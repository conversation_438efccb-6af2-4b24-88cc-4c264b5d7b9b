<?php
/**
 * Facebook Sharing Service
 * 
 * This class provides functionality for sharing content to Facebook,
 * including posts to user timelines, groups, and creating events.
 */
class FacebookSharingService {
    private $fb;
    private $appId;
    private $appSecret;
    private $redirectUri;
    private $graphVersion = 'v18.0';
    private $settingsModel;
    private $settings;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Ensure APPROOT is defined
        if (!defined('APPROOT')) {
            define('APPROOT', dirname(dirname(dirname(__FILE__))));
        }
        
        // Load Facebook Session Manager
        require_once APPROOT . '/libraries/facebook/FacebookSessionManager.php';
        
        // Check for and resolve any Facebook session conflicts
        FacebookSessionManager::clearFacebookSessions();
        
        // Load settings
        $this->settingsModel = new SettingsModel();
        $this->settings = $this->settingsModel->getSocialMediaSettings();
        
        // Use the same App ID for both login and social features
        // This is critical to prevent the "Reconnect" prompt
        $this->appId = FB_APP_ID;
        $this->appSecret = FB_APP_SECRET;
        
        // Always use the same redirect URI for consistency
        $this->redirectUri = BASE_URL . '/social/facebookCallback';
        
        // Log the configuration for debugging
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('FacebookSharingService: Using App ID: ' . $this->appId);
            error_log('FacebookSharingService: Using redirect URI: ' . $this->redirectUri);
        }
        
        // Since we're using the direct PHP API, we don't need to initialize the SDK
        // Just set a flag to indicate the service is available
        $this->fb = true;
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('FacebookSharingService: Using direct PHP API implementation');
        }
        
        // Log Facebook configuration
        if (empty($this->appId) || empty($this->appSecret)) {
            error_log('FacebookSharingService: Facebook App ID or App Secret is not configured');
        }
    }
    
    /**
     * Check if Facebook SDK is available
     * 
     * @return bool
     */
    public function isSdkAvailable() {
        return isset($this->fb);
    }
    
    /**
     * Check if Facebook sharing is enabled
     * 
     * @return bool
     */
    public function isSharingEnabled() {
        return isset($this->settings->enable_facebook_sharing) && $this->settings->enable_facebook_sharing;
    }
    
    /**
     * Check if Facebook events are enabled
     * 
     * @return bool
     */
    public function areEventsEnabled() {
        return isset($this->settings->enable_facebook_events) && $this->settings->enable_facebook_events;
    }
    
    /**
     * Get login URL for sharing
     * 
     * @param string $state CSRF state token
     * @param string $returnUrl URL to return to after authentication
     * @param string $shareType Type of sharing (post, event)
     * @param int $showId Show ID to share
     * @return string
     */
    public function getShareLoginUrl($state, $returnUrl, $shareType = 'post', $showId = null) {
        // Define permissions for business pages
        $permissions = ['public_profile'];
        
        // Add required permissions based on share type
        if ($shareType == 'post') {
            // For posting to pages and groups
            $permissions[] = 'pages_manage_posts';
            $permissions[] = 'pages_read_engagement';
            // publish_to_groups is no longer valid, use groups_access_member_info instead
            $permissions[] = 'groups_access_member_info';
        } elseif ($shareType == 'event') {
            // For creating events on pages
            $permissions[] = 'pages_manage_posts';
            $permissions[] = 'pages_read_engagement';
            $permissions[] = 'pages_show_list';
        }
        
        // Add parameters to state
        $stateParams = [
            'csrf' => $state,
            'return_url' => $returnUrl,
            'share_type' => $shareType
        ];
        
        if ($showId) {
            $stateParams['show_id'] = $showId;
        }
        
        $encodedState = base64_encode(json_encode($stateParams));
        
        // Store the state in session for verification
        FacebookSessionManager::storeState($encodedState);
        
        // Also store in the session variable that SocialController is using
        $_SESSION['facebook_share_state'] = $encodedState;
        
        // Construct the Facebook OAuth URL with URL encoding for the redirect URI
        $encodedRedirectUri = urlencode($this->redirectUri);
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('FacebookSharingService: Generated login URL with redirect URI: ' . $this->redirectUri);
            error_log('FacebookSharingService: URL encoded redirect URI: ' . $encodedRedirectUri);
            error_log('FacebookSharingService: Requested permissions: ' . implode(',', $permissions));
        }
        
        // Add auth_type=rerequest to force permission dialog and prevent "Reconnect" prompt
        return "https://www.facebook.com/{$this->graphVersion}/dialog/oauth" .
               "?client_id={$this->appId}" .
               "&redirect_uri={$encodedRedirectUri}" .
               "&state={$encodedState}" .
               "&scope=" . implode(',', $permissions) .
               "&response_type=code" .
               "&auth_type=rerequest";
    }
    
    /**
     * Get access token from code
     * 
     * @param string $code Authorization code
     * @return string|null
     */
    public function getAccessToken($code) {
        // Manual token retrieval using PHP API
        $tokenUrl = "https://graph.facebook.com/{$this->graphVersion}/oauth/access_token";
        $tokenParams = [
            'client_id' => $this->appId,
            'client_secret' => $this->appSecret,
            'redirect_uri' => $this->redirectUri,
            'code' => $code
        ];
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
        }
        
        $ch = curl_init($tokenUrl . '?' . http_build_query($tokenParams));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        $tokenResponse = curl_exec($ch);
        
        if (curl_errno($ch)) {
            error_log('cURL error: ' . curl_error($ch));
            return null;
        }
        
        curl_close($ch);
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('FacebookSharingService: Token response: ' . $tokenResponse);
        }
        
        $tokenData = json_decode($tokenResponse, true);
        
        if (!isset($tokenData['access_token'])) {
            error_log('Failed to get access token: ' . ($tokenData['error']['message'] ?? 'Unknown error'));
            return null;
        }
        
        // Store the token in session using both methods for consistency
        FacebookSessionManager::storeAccessToken($tokenData['access_token']);
        $_SESSION['fb_access_token'] = $tokenData['access_token'];
        
        // Store token expiration if available
        if (isset($tokenData['expires_in'])) {
            $_SESSION['fb_token_expires'] = time() + $tokenData['expires_in'];
        }
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('FacebookSharingService: Token expires in: ' . ($tokenData['expires_in'] ?? 'unknown'));
        }
        
        return $tokenData['access_token'];
    }
    
    /**
     * Share show to Facebook as a post
     * 
     * @param object $show Show object
     * @param string $accessToken Facebook access token
     * @param string $message Custom message (optional)
     * @param string $targetId Target ID (user, group, page) (optional)
     * @return array Success status and message/error
     */
    public function shareShowAsPost($show, $accessToken, $message = '', $targetId = 'me') {
        if (!$this->isSdkAvailable() || !$this->isSharingEnabled()) {
            return [
                'success' => false,
                'error' => 'Facebook sharing is not available or not enabled'
            ];
        }
        
        try {
            // Prepare show data
            $showUrl = BASE_URL . '/show/view/' . $show->id;
            $showDate = date('F j, Y', strtotime($show->start_date));
            if ($show->start_date != $show->end_date) {
                $showDate .= ' - ' . date('F j, Y', strtotime($show->end_date));
            }
            
            // Use custom message or default template
            if (empty($message)) {
                $message = $this->settings->facebook_default_share_text ?? 'Check out this awesome car show: {show_name} on {show_date} at {show_location}! {show_url}';
                
                // Replace placeholders
                $message = str_replace(
                    ['{show_name}', '{show_date}', '{show_location}', '{show_url}', '{show_description}'],
                    [$show->name, $showDate, $show->location, $showUrl, $show->description],
                    $message
                );
            }
            
            // Prepare post data
            $postData = [
                'message' => $message,
                'link' => $showUrl
            ];
            
            // Get primary image if available
            if (!empty($show->primary_image)) {
                $imagePath = APPROOT . '/uploads/shows/' . $show->primary_image;
                if (file_exists($imagePath)) {
                    $postData['picture'] = BASE_URL . '/uploads/shows/' . $show->primary_image;
                }
            }
            
            // Use direct API call with cURL
            $graphUrl = "https://graph.facebook.com/{$this->graphVersion}/{$targetId}/feed";
            $postData['access_token'] = $accessToken;
            
            $ch = curl_init($graphUrl);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
            $response = curl_exec($ch);
            
            if (curl_errno($ch)) {
                throw new Exception('cURL error: ' . curl_error($ch));
            }
            
            curl_close($ch);
            
            $responseData = json_decode($response, true);
            
            if (isset($responseData['error'])) {
                throw new Exception('Facebook API error: ' . $responseData['error']['message']);
            }
            
            return [
                'success' => true,
                'post_id' => $responseData['id'],
                'message' => 'Show successfully shared to Facebook'
            ];
        } catch (\Facebook\Exceptions\FacebookResponseException $e) {
            error_log('Graph returned an error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Facebook API error: ' . $e->getMessage()
            ];
        } catch (\Facebook\Exceptions\FacebookSDKException $e) {
            error_log('Facebook SDK returned an error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Facebook SDK error: ' . $e->getMessage()
            ];
        } catch (Exception $e) {
            error_log('Error sharing show to Facebook: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Create Facebook event for show
     * 
     * @param object $show Show object
     * @param string $accessToken Facebook access token
     * @param string $pageId Facebook page ID (optional)
     * @return array Success status and message/error
     */
    public function createEventForShow($show, $accessToken, $pageId = null) {
        if (!$this->isSdkAvailable() || !$this->areEventsEnabled()) {
            return [
                'success' => false,
                'error' => 'Facebook events are not available or not enabled'
            ];
        }
        
        try {
            // Prepare show data
            $showUrl = BASE_URL . '/show/view/' . $show->id;
            $startTime = date('Y-m-d\TH:i:s', strtotime($show->start_date . ' 09:00:00'));
            $endTime = date('Y-m-d\TH:i:s', strtotime($show->end_date . ' 17:00:00'));
            
            // Use event template for description
            $description = $this->settings->facebook_event_template ?? '{show_description}\n\nRegistration opens: {registration_start}\nRegistration closes: {registration_end}\n\nMore details at: {show_url}';
            
            // Replace placeholders
            $description = str_replace(
                [
                    '{show_name}', 
                    '{show_date}', 
                    '{show_location}', 
                    '{show_url}', 
                    '{show_description}',
                    '{registration_start}',
                    '{registration_end}'
                ],
                [
                    $show->name, 
                    date('F j, Y', strtotime($show->start_date)), 
                    $show->location, 
                    $showUrl, 
                    $show->description,
                    date('F j, Y', strtotime($show->registration_start)),
                    date('F j, Y', strtotime($show->registration_end))
                ],
                $description
            );
            
            // Get city and state from custom fields if available
            $city = property_exists($show, 'city') ? $show->city : '';
            $state = property_exists($show, 'state') ? $show->state : '';
            
            // Log for debugging
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('FacebookSharingService: Show object properties: ' . print_r(get_object_vars($show), true));
            }
            
            // Prepare event data
            $eventData = [
                'name' => $show->name,
                'description' => $description,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'place' => [
                    'name' => $show->location
                ]
            ];
            
            // Only add location details if city and state are available
            if (!empty($city) && !empty($state)) {
                $eventData['place']['location'] = [
                    'city' => $city,
                    'state' => $state,
                    'country' => 'United States'
                ];
            }
            
            // Determine target (page or user)
            $targetId = 'me';
            if ($pageId) {
                $targetId = $pageId;
            } elseif (!empty($this->settings->facebook_page_id)) {
                $targetId = $this->settings->facebook_page_id;
            }
            
            // Use direct API call with cURL
            $graphUrl = "https://graph.facebook.com/{$this->graphVersion}/{$targetId}/events";
            $postData = array_merge($eventData, ['access_token' => $accessToken]);
            
            // Convert eventData to JSON for debugging
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('FacebookSharingService: Event data: ' . json_encode($eventData));
            }
            
            $ch = curl_init($graphUrl);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
            $response = curl_exec($ch);
            
            if (curl_errno($ch)) {
                throw new Exception('cURL error: ' . curl_error($ch));
            }
            
            curl_close($ch);
            
            $responseData = json_decode($response, true);
            
            if (isset($responseData['error'])) {
                throw new Exception('Facebook API error: ' . $responseData['error']['message']);
            }
            
            return [
                'success' => true,
                'event_id' => $responseData['id'],
                'message' => 'Facebook event successfully created'
            ];
        } catch (\Facebook\Exceptions\FacebookResponseException $e) {
            error_log('Graph returned an error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Facebook API error: ' . $e->getMessage()
            ];
        } catch (\Facebook\Exceptions\FacebookSDKException $e) {
            error_log('Facebook SDK returned an error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Facebook SDK error: ' . $e->getMessage()
            ];
        } catch (Exception $e) {
            error_log('Error creating Facebook event: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get user's Facebook pages
     * 
     * @param string $accessToken Facebook access token
     * @return array List of pages or error
     */
    public function getUserPages($accessToken) {
        if (!$this->isSdkAvailable()) {
            return [
                'success' => false,
                'error' => 'Facebook SDK is not available'
            ];
        }
        
        try {
            // Use direct API call with cURL
            $graphUrl = "https://graph.facebook.com/{$this->graphVersion}/me/accounts?access_token=" . urlencode($accessToken);
            
            $ch = curl_init($graphUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
            $response = curl_exec($ch);
            
            if (curl_errno($ch)) {
                throw new Exception('cURL error: ' . curl_error($ch));
            }
            
            curl_close($ch);
            
            $responseData = json_decode($response, true);
            
            if (isset($responseData['error'])) {
                throw new Exception('Facebook API error: ' . $responseData['error']['message']);
            }
            
            $pages = isset($responseData['data']) ? $responseData['data'] : [];
            
            return [
                'success' => true,
                'pages' => $pages
            ];
        } catch (\Facebook\Exceptions\FacebookResponseException $e) {
            error_log('Graph returned an error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Facebook API error: ' . $e->getMessage()
            ];
        } catch (\Facebook\Exceptions\FacebookSDKException $e) {
            error_log('Facebook SDK returned an error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Facebook SDK error: ' . $e->getMessage()
            ];
        } catch (Exception $e) {
            error_log('Error getting user pages: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get user's Facebook groups
     * 
     * @param string $accessToken Facebook access token
     * @return array List of groups or error
     */
    public function getUserGroups($accessToken) {
        if (!$this->isSdkAvailable()) {
            return [
                'success' => false,
                'error' => 'Facebook SDK is not available'
            ];
        }
        
        try {
            // Use direct API call with cURL
            $graphUrl = "https://graph.facebook.com/{$this->graphVersion}/me/groups?access_token=" . urlencode($accessToken);
            
            $ch = curl_init($graphUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
            $response = curl_exec($ch);
            
            if (curl_errno($ch)) {
                throw new Exception('cURL error: ' . curl_error($ch));
            }
            
            curl_close($ch);
            
            $responseData = json_decode($response, true);
            
            if (isset($responseData['error'])) {
                throw new Exception('Facebook API error: ' . $responseData['error']['message']);
            }
            
            $groups = isset($responseData['data']) ? $responseData['data'] : [];
            
            return [
                'success' => true,
                'groups' => $groups
            ];
        } catch (\Facebook\Exceptions\FacebookResponseException $e) {
            error_log('Graph returned an error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Facebook API error: ' . $e->getMessage()
            ];
        } catch (\Facebook\Exceptions\FacebookSDKException $e) {
            error_log('Facebook SDK returned an error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Facebook SDK error: ' . $e->getMessage()
            ];
        } catch (Exception $e) {
            error_log('Error getting user groups: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Error: ' . $e->getMessage()
            ];
        }
    }
}