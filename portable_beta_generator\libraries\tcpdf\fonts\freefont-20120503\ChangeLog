2012-04-22 16:18 +0000 [r2239-2240]  <PERSON><PERSON><PERSON>_<PERSON>:

	* INSTALL: Improved info on Debian install.

	* notes/troubleshooting.txt: More info on the blurry
	  text in Windows issue.

2012-04-22 11:43 +0000 [r2238]  Stevan_White:

	* tools/report/OpenType/UnicodeRanges.py:
	  Regularized abbreviatons of supplement, extended

2012-04-22 11:06 +0000 [r2237]  <PERSON><PERSON><PERSON>_White:

	* FreeSerifBold.sfd: Arabic: added 3 graphical
	  marks to complete the range.

2012-04-22 10:51 +0000 [r2236]  Steva<PERSON>_White:

	* FreeSerif.sfd: Fixed problems turned up by
	  tests. Somehow alefmaksurainitialarabic hadn't been re-named. Two
	  new Combining Diacritics hadn't been given Unicode.

2012-04-21 17:11 +0000 [r2235]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeMono.sfd,
	  FreeMonoBoldOblique.sfd: Revision of German
	  TTF Names.

2012-04-21 16:52 +0000 [r2231-2234]  <PERSON><PERSON><PERSON>_<PERSON>:

	* INSTALL: More info for Windows.

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd: PS Private: put in extra
	  blues line, adjusted to numerals.

	* tools/report/OpenType/UnicodeRanges.py: Corrected
	  one range

	* tools/generate/TrueType,
	  tools/generate/WOFF,
	  tools/generate/OpenType,
	  tools/generate/MacTT: all: removed launch line
	  WOFF: made quadratic, and autoinstructed TrueType: turned off
	  saving of hints

2012-04-21 11:17 +0000 [r2230]  Stevan_White:

	* FreeSerifBold.sfd: Vietnamese: small
	  adjustment of accents

2012-04-20 12:57 +0000 [r2228-2229]  Stevan_White:

	* AUTHORS, CREDITS: Various
	  corrections, doing the best I could with stale URLs.

	* ChangeLog: Some typos

2012-04-20 11:06 +0000 [r2227]  Stevan_White:

	* ChangeLog: Summary of changes since late last
	  year.

2012-04-19 21:07 +0000 [r2226]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: re-named
	  alefmaksurainitialarabic and alefmaksuramedialarabic as
	  alefmaksurainitialarabic and alefmaksuramedialarabic to avoid a
	  confusing FontForge warning. This was discussed on the FontForge
	  list, and a partial remedy was agreed upon.

2012-04-19 21:02 +0000 [r2225]  Stevan_White:

	* FreeSerif.sfd: GPOS table: removed the
	  Cyrillic kerns with non-Unicode "alternative" Cyrillic, because
	  FontForge warns that Windows apps will have *problems* with
	  mappings that aren't Unicode-Unicode. Most of the kerns were
	  rather small anyway.

2012-04-19 20:39 +0000 [r2224]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMonoBoldOblique.sfd: TTF Names: Fixes bug
	  #36232: Windows 7 strange bug with installation/font control
	  panel. Also improved French names. (Thanks to Emmanuel Vallois)

2012-04-18 07:31 +0000 [r2223]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd: Devanagari: Regularized
	  overlap of horizontal. Cleaned up numerous other letters.

2012-04-17 07:37 +0000 [r2222]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Currency Symbols:
	  Added/corrected/adjusted Indian Rupee.

2012-04-16 22:45 +0000 [r2221]  Stevan_White:

	* notes/maintenance.txt,
	  notes/usage.txt,
	  notes/troubleshooting.txt,
	  notes/webfont_guidelines.txt,
	  notes/features.txt: Added section about where
	  letters are to usage.txt Made sure svn properties are set for
	  others.

2012-04-16 22:26 +0000 [r2220]  Stevan_White:

	* README: Updated information about file types

2012-04-16 22:17 +0000 [r2219]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: resolves bug
	  #36195: Arabic semicolon doesn't match with others

2012-04-15 23:16 +0000 [r2218]  Stevan_White:

	* FreeSerifBold.sfd: Arabic: this was meant to
	  be in previous commit.

2012-04-15 18:30 +0000 [r2217]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: Remedies bug
	  #36196: Arabic ba and teh isolated and final. Also applied
	  similar alteration to some similar letters. Bold: there were some
	  missing ligatures of superscript marks.

2012-04-15 17:52 +0000 [r2216]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: Remedies bug
	  #36198: dots for Arabic letter jeh Also revised placement dots
	  over some other letters.

2012-04-15 13:24 +0000 [r2215]  Stevan_White:

	* FreeSerif.sfd: Arabic: remedies bug #36191,
	  Arabic character DAD final form

2012-04-15 12:23 +0000 [r2214]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: toward bug #36189:
	  discrepancy in different forms of ک and گ

2012-04-15 12:02 +0000 [r2213]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: remedies bug
	  #36190, Arabic question mark too small

2012-04-15 11:16 +0000 [r2212]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd: Devanagari: anchors for
	  recently added letters. Thanks, Zdenek.

2012-04-15 10:44 +0000 [r2211]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: adjusted height of
	  dots on a few glyphs

2012-04-15 10:31 +0000 [r2210]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: remedies bug
	  #36181: initial and final form of heh (U+0647)
	  https://savannah.gnu.org/bugs/?36181

2012-04-14 13:35 +0000 [r2209]  Stevan_White:

	* tools/test/ranges/Arabic/arabic_test.py (added),
	  tools/test/ranges/Arabic/unicode_joining.py
	  (added),
	  tools/test/ranges/Arabic/generate_arabic_shaping.py
	  (added), tools/test/ranges (added),
	  tools/test/ranges/Arabic (added): Scripts to
	  generate comprehensive Arabic tests in HTML, courtesy of Emmanuel
	  Vallois.

2012-04-13 09:56 +0000 [r2208]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSerif.sfd: Devanagari: serif: corrected
	  OpenType glyph type of ru, ruu ligatures to be base ligatures,
	  not marks. sans, sans bold: added ka-ra "vatu" ligature at
	  Zdenek's suggestion. sans: removed empty lookup.

2012-04-10 20:33 +0000 [r2207]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Devanagari: several mark
	  placement issues, pointed out by Zdenek.

2012-04-09 21:58 +0000 [r2206]  Stevan_White:

	* sfd/Makefile, FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: made Arabic and
	  Persion digits 500EM wide.

2012-04-09 18:35 +0000 [r2205]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd: Arabic: messed with digits.
	  Overall, had been building with GIT version of FontForge. found
	  that older distro version 22-Feb-2011-ML shows warning dialogs
	  Internal Error: EITOfNextMajor failed! on Clockwise Test
	  (cleverly omitting the crucial info). Turns out, just rounding to
	  int on certain characters fixes it.

2012-04-09 11:34 +0000 [r2204]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: More kerning: tried
	  to synch up normal and bold. Altered letters especially for
	  Seraiki. Lots of fiddling with heh and heh-doachashmee. Seems the
	  letter transforms very differently for different languages. This
	  is not there yet.

2012-04-08 12:53 +0000 [r2203]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: made 'ccmp' table
	  and some combining marks to handle cases of letters that don't
	  have positional forms in Unicode. Only did a few -- more are
	  needed. Lots of kerning. Fiddled with shapes of several letters.

2012-04-07 19:21 +0000 [r2202]  Stevan_White:

	* FreeSerif.sfd: Arabic: more alterations toward
	  bug #36115: Various little problems with Arabic/Persian glyphs
	  More kerning. Some forms from Pashto are missing.

2012-04-06 17:45 +0000 [r2201]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: made medial form of
	  loop-letters more distinctive from initial forms. (toward bug bug
	  #36115: Various little problems with Arabic/Persian glyphs)

2012-04-05 16:43 +0000 [r2200]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: improvements to
	  digits (Persian primarily) based on patch from Masoud Pourmoosa.

2012-04-04 17:10 +0000 [r2199]  Stevan_White:

	* FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic; (for Farsi) toward
	  bug 34700: made farsi yeh final more like the Unicode samples.
	  https://savannah.gnu.org/bugs/?34700 Arabic/Persian in FreeSerif:
	  ending "Yeh" is annoying

2012-04-04 14:34 +0000 [r2198]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Various attempts to make
	  web fonts work/look better. Sans Devanagari: made relation
	  between overhang and horizontal more consistent for half-forms.
	  Corrected metrics in FreeSansOblique. Some kerning overall, after
	  looking again at sample pages

2012-04-02 13:30 +0000 [r2197]  Stevan_White:

	* FreeSansBold.sfd: Devanagari: toward more
	  consistent spacing, especialy of half-forms

2012-04-02 12:57 +0000 [r2196]  Stevan_White:

	* FreeSans.sfd: Devanagri: better connection of
	  horizontals, especially half forms.

2012-04-01 13:45 +0000 [r2195]  Stevan_White:

	* FreeSerif.sfd: Combining Diacritical Marks
	  Supplement: made glyphs for large part of range. Latin
	  Extended-D: made Latin con and US glyphs.

2012-03-30 13:34 +0000 [r2193-2194]  Stevan_White:

	* tools/generate/OpenType: Turn on PS rounding for
	  Type1 glyphs

	* FreeSerifItalic.sfd,
	  FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoBoldOblique.sfd: Pointwise cleanup
	  toward validation with PS rounding turned on for OpenType.

2012-03-30 11:16 +0000 [r2192]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Devanagari: bold, italic,
	  re-considered vowel naming to avoid FF warnings. Latin: a bit of
	  kerning

2012-03-29 20:39 +0000 [r2191]  Stevan_White:

	* notes/troubleshooting.txt: corrections about
	  recent changes

2012-03-28 19:59 +0000 [r2190]  Stevan_White:

	* notes/troubleshooting.txt: Remarks about
	  LibreOffice's challenges with OpenType features.

2012-03-28 19:38 +0000 [r2189]  Stevan_White:

	* notes/README-downloads.txt: brought up to date,
	  regarding perferred format in various systems, and the WOFF files

2012-03-28 19:31 +0000 [r2185-2188]  Stevan_White:

	* AUTHORS, INSTALL,
	  COPYING: changed SVN keywords

	* ChangeLog: corrections, addition

	* CREDITS: correctons, additions

	* FreeSans.sfd,
	  FreeSansBold.sfd: Devanagari: simplified dist
	  lookup criteria

2012-03-28 19:24 +0000 [r2184]  Stevan_White:

	* tools/report/ligatureLookups.py: better error
	  handling, doc

2012-03-28 19:20 +0000 [r2183]  Stevan_White:

	* FreeSerifBold.sfd: Devanagari: cleaned up a
	  bunch of excess GPOS anchors; tidied lookups.

2012-03-28 09:17 +0000 [r2180-2182]  Stevan_White:

	* Makefile: insert troubleshooting doc

	* notes/troubleshooting.txt (added): A doc
	  explaining how to troubleshoot font problems.

	* notes/usage.txt: Tidied, more explanation of the
	  doc as a whole.

2012-03-26 10:43 +0000 [r2179]  Stevan_White:

	* FreeSerif.sfd: Devanagari: much experimenting
	  with lookups to center western punctuation between words
	  (resulted in several bug reports on firefox and xetex).
	  Currently, uses GSUB 'locl'. GPOS are too problematic in apps to
	  use.

2012-03-25 09:49 +0000 [r2178]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd: Devanagari: added
	  positioning for western punctuation

2012-03-25 09:40 +0000 [r2177]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Devanagari, Bengali: small
	  tweeks to dots

2012-03-24 21:34 +0000 [r2176]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMonoBoldOblique.sfd: TTF Names: Hindi
	  hyphenated term

2012-03-24 10:20 +0000 [r2175]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Devanagari: regarding
	  horizontal spacing, on advice of Zdenek. o, au -- stems were too
	  close. danda, dbldanda: much too near preceding word. Made so
	  that danda-space would space the mark evenly between the words.
	  Made lookups for similarly-spaced question and exclamation marks.
	  Kerning (or dist?): Experiments -- problems include: Firefox
	  doesn't seem to support "dist". Xetex doesn't turn kerning on by
	  default. Fontforge complains Windows doesn't like to kern
	  auxiliaries with Unicode ranges. Nonetheless, it does pack words
	  rather tighter. My own advice: made ra glyph more narrow. It was
	  the worst, spacing wise, on the un-kerned page. Oriya: made
	  similar spacing for its danda.

2012-03-23 12:56 +0000 [r2174]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Devanagari: efforts to
	  improve horizontal spacing, that Zdenek complains about. 1)
	  medium: o, au vowels: stems too close 2) danda, dbldanda, should
	  be widely separated from words, centred between them. 3) bold: on
	  horizontal, bevels extend rather more than they should (reduced
	  in Unicode and through first set of half marks in the
	  auxiliaries... willl complete later.) 4) made special localized
	  forms of question, exclamation mark.

2012-03-22 23:25 +0000 [r2172-2173]  Stevan_White:

	* tools/generate/TrueType,
	  tools/generate/WOFF,
	  tools/generate/OpenType: Set the encoding to
	  UnicodeFull before build (a bit wasteful for faces with no high
	  Unicode, but couldn't think of a better way.)

	* FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd: More re-thinking of OS/2
	  weights. In sans, again adjusted OS/2 Windows metrics to stop
	  clipping. (I'm just sure this will mess up line spacing in
	  OpenOffice, but didn't have time to check. Something has a bug.)

2012-03-22 19:17 +0000 [r2171]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Font Info: filled out
	  TTF Names Fullname for Hindi

2012-03-22 19:00 +0000 [r2170]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Added Hindi term for
	  Font Style in TTF Names (courtesy of Monika Shah)

2012-03-22 18:49 +0000 [r2169]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: FontInfo:
	  re-considered OS/2 and Panose weight descriptions

2012-03-22 15:02 +0000 [r2168]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Devanagari: Replaced a
	  'pres' table that got zeroed by a crash. More adjustments to mark
	  placement.

2012-03-22 12:51 +0000 [r2167]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Devanagari: Replaced lost
	  'pres' lookup tables.

2012-03-22 10:35 +0000 [r2166]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Overall: Several
	  measures toward reducing warnings from Font Validator. In "roman"
	  faces, change TTF Name: SubFamily to "Regular". (FontForge binds
	  this to PSNames:Weight --? so changed that.) Experimenting with
	  General:Name For Humans (Fiddled with Family Name too...but
	  changed it back.) Changes to OS/2:Sub/Super Subscript Offset --
	  made positive. Some fiddling with Panose info. Also: previously
	  OS/2 Charsets missed 708, Arabic ASMO 708. (Bug in
	  FontForge-patch reported.) In sans and mono oblique, bold, and
	  boldoblique, added some math operators to fill an Apple codepage.
	  Sinhala: FreeSerifBold -- implemented same change moving 'ccmp'
	  to 'akhn' as in roman face. Has to do with bug in OpenOffice/ICU.

2012-03-20 08:25 +0000 [r2165]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: OS/2 small tweeks:
	  set "weight, width, slope only"

2012-03-19 18:34 +0000 [r2164]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: OS/2 Charsets: made serif,
	  serifbold, mono to support ASMO 708 MS Codepage for Arabic (small
	  bug in FontForge) OS/2 Version: changed from Automatic to 3 for
	  all, only to satisfy old MS Font Validator, which chokes on
	  higher versions.

2012-03-19 09:45 +0000 [r2163]  Stevan_White:

	* FreeMono.sfd: correction-something went wrong
	  with the encoding two commits ago. (I switched encoding from
	  custom to BMP and back again.) This should encorporate the recent
	  changes and fix the problem.

2012-03-19 09:07 +0000 [r2162]  Stevan_White:

	* tools/report/OpenType/UnicodeRanges.py: comments
	  containing new Unicode ranges without ulUnicodeRange bits

2012-03-19 08:41 +0000 [r2161]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: OS/2 version: set to
	  3 (was "Automatic") mostly for MS Font Validator. Arabic
	  Presentation Forms B: (regular) made lam-alif ligatures,
	  completing range.

2012-03-18 15:57 +0000 [r2160]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Devanagari: more toward
	  positioning of marks.

2012-03-18 15:09 +0000 [r2159]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd: Devanagari: resolved some
	  dfficult cases reported by Zdenek. (esp. that esign and reph
	  could come in either order.)

2012-03-18 06:37 +0000 [r2158]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSerif.sfd: Devanagari: More toward proper
	  mark placement.

2012-03-16 17:39 +0000 [r2157]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Devanagari: more issues
	  regarding mark placement.

2012-03-15 23:39 +0000 [r2156]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Devanagari: toward fixing
	  mark positioning problems Zdenek pointed out.

2012-03-15 10:28 +0000 [r2155]  Stevan_White:

	* FreeSerif.sfd: Latin: got i-ogonek-acute to
	  remove dot over i again (for Navaho). Problem was evidently a
	  DFLT{dflt} entry in lookup language tags.

2012-03-15 10:03 +0000 [r2153-2154]  Stevan_White:

	* FreeSans.sfd: Devanagari: fixes for some mark
	  issues pointed out by Zdenek.

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin: corrected
	  longstanding problem that made stacked Vietnamese accents fail.
	  Had to do with long list of languages filter of default ligature
	  lookup, excluding Turkish, and rendering logic I don't
	  understand. Devanagari: toward correcting marks stacked below
	  rakaar.

2012-03-13 16:29 +0000 [r2152]  Stevan_White:

	* FreeSerif.sfd: Mathematical Alphanumeric
	  Symbols (especially) named some characters

2012-03-13 10:46 +0000 [r2151]  Stevan_White:

	* FreeSerifBold.sfd: Devanagari: ha-rrvocalic
	  and improved ha-rakaar, as in medium

2012-03-12 21:59 +0000 [r2150]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Mathematical Operators:
	  reduced horizontal spacing of complement operator

2012-03-12 21:28 +0000 [r2149]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Mathematical Operators:
	  re-thought dimensions of "complement" symbol (after seeing it in
	  Dieudonné's analysis book)

2012-03-12 18:49 +0000 [r2148]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Devanagari: various adjustments
	  to marks. serif: added ha-llvocalic, rplaced ha_ra conjunct,
	  positioned virama separately from fowels.

2012-03-11 22:39 +0000 [r2147]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Bengali: adjustments to marks;
	  GPOS tables to position them.

2012-03-11 16:37 +0000 [r2146]  Stevan_White:

	* FreeSansOblique.sfd: Bengali: fixed some
	  horizontal spacing issues

2012-03-11 00:56 +0000 [r2145]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSerif.sfd: Made to pass tests -- mostly
	  little pointwise tweeks. One range of auxiliaries had wandered
	  out of the Private Use area.

2012-03-10 23:55 +0000 [r2144]  Stevan_White:

	* FreeMonoOblique.sfd: Armenian: corrected width
	  of one glyph

2012-03-10 18:53 +0000 [r2143]  Stevan_White:

	* FreeSans.sfd: Bengali: completed pointwise
	  clean-up of auxiliaries. Found a couple more with quite wrong
	  rignt bounds.

2012-03-10 13:15 +0000 [r2142]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: OS/2 vertical metrics:
	  another change of heart: made vertical metrics like other fonts I
	  looked at, and tightened line spacing. Bengali: (sans) point-wise
	  clean-up down to bn_l_dd_ra (before I pooped out) Fixed right
	  bound of several auxiliaries, such as bn_p_ma, bn_m_p_la

2012-03-09 19:04 +0000 [r2141]  Stevan_White:

	* FreeSerifItalic.sfd: Removed some unintended
	  empty contours. Pointwise clean-up of points too close (but gave
	  up on Bengali range.)

2012-03-09 18:45 +0000 [r2139-2140]  Stevan_White:

	* tools/generate/TrueType: Made splines quadratic

	* FreeMonoOblique.sfd: Pointwise cleanup of
	  "points too close" problems.

2012-03-09 18:35 +0000 [r2138]  Stevan_White:

	* FreeSerifBold.sfd: Removed an unintended empty
	  contour

2012-03-09 16:52 +0000 [r2137]  Stevan_White:

	* tools/generate/TrueType,
	  tools/generate/WOFF,
	  tools/generate/OpenType,
	  tools/generate/buildutils.py,
	  tools/generate/MacTT: Updated copyrights

2012-03-09 16:49 +0000 [r2136]  Stevan_White:

	* sfd/Makefile: Made to properly report progress for
	  OpenType target

2012-03-09 16:08 +0000 [r2135]  Stevan_White:

	* FreeSerif.sfd: Mahjong Tiles: The previous
	  commit didn't quite do it. Sometimes, but starting the generate
	  process from scratch, it still crashed. But this "circle" glyph
	  was excessively elaborate anyway. Made it simpler, cleaned it up,
	  and now the crash isn't happening.

2012-03-09 13:12 +0000 [r2134]  Stevan_White:

	* FreeSerif.sfd: Mahjong Tiles: work-around for
	  FontForge crash, by use of references. Crash: Select All,
	  AutoHint. Generate Fonts, OpenType Options, check Postscript
	  Hints. Generate. segfault.

2012-03-09 12:04 +0000 [r2133]  Stevan_White:

	* FreeSerif.sfd: Mahjong Tiles: named the glyphs

2012-03-09 01:17 +0000 [r2132]  Stevan_White:

	* FreeSans.sfd: Corrected mixed reference and
	  contour. Overall point-wise cleanup "points too close"

2012-03-07 23:27 +0000 [r2131]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Font Info General:
	  changed copyright 2011 to 2012 since no public release was made
	  last year.

2012-03-07 21:06 +0000 [r2130]  Stevan_White:

	* Makefile: Source tarball now includes all build
	  scripts etc.

2012-03-07 14:50 +0000 [r2129]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: .notdef: made clearer
	  that something is missing...

2012-03-06 19:07 +0000 [r2128]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Devanagari: mostly
	  adjustments to mark placement.

2012-03-06 11:22 +0000 [r2127]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd: Devanagari: various
	  alterations toward ligatures as in Velthuis manual, and for
	  legibility.

2012-03-05 21:18 +0000 [r2126]  Stevan_White:

	* FreeSansBold.sfd: Devanagari: made half-na a
	  bit wider on Monika Shah's recommendation. Tweeked several other
	  auxiliaries for legibility.

2012-03-05 20:25 +0000 [r2125]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd: Devanagari: Several issues
	  reported by Monika Shah, including rakaar being placed
	  improperly, and some Sanskrit ligatures leaking into Hindi.

2012-03-05 16:41 +0000 [r2124]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd: Devanagari: combined
	  half-form issues, reported by Zdenek Wagner.

2012-03-05 15:07 +0000 [r2123]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Devanagari: Several
	  alterations based on a report by Zdenek Wagner. Anusvara in
	  conjunction with other above marks, u and uumatra in conjunction
	  with ha and nuktas.

2012-03-05 01:22 +0000 [r2122]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansOblique.sfd: Bengali: Several
	  ligatures with ba had lost the holes in their ba. (bn_k_ba, etc);
	  also bn_asamib_r_ukaar. Copied holes from similar glyphs. A
	  similar thing may have happend to a couple of other ligs
	  (bn_ss_tta1, bn_ss_tt_ra1). I don't know how these should look,
	  but they seem to be unused anyway.

2012-03-04 18:24 +0000 [r2121]  Stevan_White:

	* FreeSerifItalic.sfd: general clean-up of
	  points too close. Bogged down on Bengali.

2012-03-04 18:01 +0000 [r2120]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Currency Symbols: added
	  Rupee to italic. general clean-up of points too close.

2012-03-04 13:18 +0000 [r2119]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Gurmukhi: adjusted
	  placement of dots. re-named tables and rounded to int in bold.

2012-03-04 12:25 +0000 [r2118]  Stevan_White:

	* FreeSerif.sfd: Devanagari Extended: completed
	  range

2012-03-04 11:25 +0000 [r2117]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoOblique.sfd: Armenian: added dhahma
	  (and ran clear hints)

2012-03-04 11:21 +0000 [r2116]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd, FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Armenian: added drahma

2012-03-04 10:45 +0000 [r2115]  Stevan_White:

	* FreeSerifBold.sfd: Devanagari: completed main
	  Unicode range.

2012-03-04 10:17 +0000 [r2114]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Gurmukhi: Added range in
	  bold. Serif: Many point-wise cleanups, in Gurmukhi and other
	  ranges.

2012-03-02 22:19 +0000 [r2113]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Devanagari: starting to
	  converge. In serif, added ra-u and ra-uu and tables, raised
	  anusvara. Various small alterations to sans letters.

2012-03-02 15:18 +0000 [r2112]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Devanagari: corrected
	  rakaar on letter da

2012-03-02 14:56 +0000 [r2111]  Stevan_White:

	* FreeSansBold.sfd: Devanagari: glyph clean-up
	  pass through auxiliaries.

2012-03-02 12:22 +0000 [r2110]  Stevan_White:

	* FreeSansBold.sfd: Devanagari: clean-up pass
	  through Unicode range. Added a few ligatures.

2012-03-02 03:35 +0000 [r2107-2109]  Stevan_White:

	* FreeSansBold.sfd: Devanagari: implemented by
	  increasing weight of letters from Sans. Subset of auxiliaries in
	  Sans: plan is to do more with tables.

	* FreeSans.sfd: Devanagari: re-instated
	  dev_ha_rvocalic.blws

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Devanagari: corrected table
	  having to do with rakaar on round consonants

2012-03-01 19:50 +0000 [r2106]  Stevan_White:

	* FreeSans.sfd: Devanagari: Regularized
	  horizontal bar height and width, as well as size and shape of dot
	  marks (made all diamonds). More use of references. (With GPOS
	  tables, most of the nuktas and halents would go away.) Latin:
	  made comma a bit less spindly, and likewise with derived marks.

2012-02-29 22:29 +0000 [r2105]  Stevan_White:

	* FreeSerif.sfd: Bengali: some insignificant
	  clean-up. what to do about these glyphs?

2012-02-29 19:12 +0000 [r2104]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd: Various small corrections
	  to get test suite to run

2012-02-29 17:02 +0000 [r2103]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Devanagari: corrected table
	  tag for style sets. Made substitutions for Marathi more like
	  those for Hindi than Sanskrit.

2012-02-29 11:21 +0000 [r2102]  Stevan_White:

	* FreeSerifBold.sfd: Devanagari: should now have
	  same coverage as regular face.

2012-02-29 07:10 +0000 [r2101]  Stevan_White:

	* FreeSerif.sfd: Devanagari: replaced
	  auxiliaries; added positioning anchors.

2012-02-25 13:52 +0000 [r2100]  Stevan_White:

	* Makefile: include usage notes in packages

2012-02-25 10:40 +0000 [r2099]  Stevan_White:

	* notes/usage.txt: Further notes

2012-02-25 10:30 +0000 [r2098]  Stevan_White:

	* notes/usage.txt (added),
	  notes/features.txt: Usage notes -- primarily about
	  discretionary and language-specific features, what they're for
	  and how to use them.

2012-02-24 15:58 +0000 [r2097]  Stevan_White:

	* FreeSerif.sfd: Oriya: Special form for ukar
	  under some consonants, ligatures for several consonants with
	  aakar, with lookups.

2012-02-24 12:21 +0000 [r2096]  Stevan_White:

	* FreeSerif.sfd: Oriya: found auxiliaries that
	  had excaped clean-up.

2012-02-24 11:47 +0000 [r2095]  Stevan_White:

	* FreeSerif.sfd: Oriya: Switched yophola and
	  yyophola for about the 3rd time. This time, following doc for
	  ortex. Clean-up of "points too close".

2012-02-23 23:37 +0000 [r2094]  Stevan_White:

	* FreeSerif.sfd: Oriya: removed overlap of
	  components, validated.

2012-02-23 23:25 +0000 [r2093]  Stevan_White:

	* FreeSerif.sfd: Oriya: glyph cleanup of
	  auxiliaries complete.

2012-02-23 19:55 +0000 [r2092]  Stevan_White:

	* FreeSerif.sfd: Oriya: one removed glyph had
	  stowed away in a chaining lookup list

2012-02-23 18:09 +0000 [r2091]  Stevan_White:

	* FreeSerif.sfd: Oriya: Corrected lookup tag for
	  'haln' form for 'no'. More glyph cleanup in auxiliaries.

2012-02-23 16:19 +0000 [r2090]  daniel_j:

	* FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd: Set correct codepoint
	  and Unicode name for uniA7AA (hooked H) in sans bold and
	  bold-oblique

2012-02-23 14:52 +0000 [r2089]  Stevan_White:

	* FreeSerif.sfd: Oriya: corrected blown-out
	  letter in main range, renamed an auxiliary.

2012-02-23 14:11 +0000 [r2088]  Stevan_White:

	* FreeSerif.sfd: Oriya: worked on vowels.
	  adjusted ii and ai_length to improve connections. Made table for
	  i sign beneath tho, dho.

2012-02-23 13:35 +0000 [r2087]  Stevan_White:

	* FreeSerif.sfd: Oriya: in auxiliary glyphs, a
	  bit more clean-up, regularizing stem widths. Moved previous
	  'half' to 'haln', as 'half' isn't recognized by Pango.

2012-02-23 12:46 +0000 [r2086]  Stevan_White:

	* FreeSerif.sfd: Oriya: Re-shaped a few letters
	  with over-thick round parts. Made vowels a bit closer to base
	  letter.

2012-02-23 11:53 +0000 [r2085]  Stevan_White:

	* FreeSerif.sfd: Oriya: made verticals in main
	  range of more consistent width.

2012-02-22 22:32 +0000 [r2084]  Stevan_White:

	* FreeSerif.sfd: Oriya: more lookups seem to be
	  working, but there is a big question about why the halent
	  switches position. Is this the shaping engine, or something I've
	  done with the tables?

2012-02-22 19:44 +0000 [r2083]  Stevan_White:

	* FreeSerif.sfd: Oriya: regularized glyph
	  bearings

2012-02-22 19:18 +0000 [r2082]  Stevan_White:

	* FreeSerif.sfd: Oriya: enabled lookups for lots
	  more ligatures. Still concerned that akhans aren't activating.
	  Some adjustment of stem widths, etc.

2012-02-22 09:38 +0000 [r2081]  Stevan_White:

	* FreeSerif.sfd: Oriya: made anusvara a spacing
	  mark, made tables and spacing versions of below base marks to
	  avoid collisions with subscripted consonants, adjusted vowel e to
	  be less angular and a bit lighter.

2012-02-22 00:44 +0000 [r2080]  Stevan_White:

	* FreeSerif.sfd: Oriya: a lot of work with
	  tables getting examples on MS Typography pages on Oriya to work.
	  Glyph clean-up, mark adjustments.

2012-02-21 10:39 +0000 [r2079]  Stevan_White:

	* FreeSerif.sfd: Oriya: completed Unicode range;
	  more glyph clean-up. yophola and zophola were exchanged -- fixed.
	  made look-up for reph+canrabindu.

2012-02-20 22:39 +0000 [r2078]  Stevan_White:

	* FreeSans.sfd: Devanagari: better use of
	  references in auxiliary range

2012-02-20 08:41 +0000 [r2077]  Stevan_White:

	* FreeSerif.sfd: Oriya: more glyph clean-up

2012-02-19 20:52 +0000 [r2076]  Stevan_White:

	* FreeMono.sfd,
	  FreeSerifBold.sfd: Currency Symbols: New
	  Indian Rupee.

2012-02-19 20:27 +0000 [r2075]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Currency Symbols: Rupee ought
	  to be shaped as a Devanagari ra with two horizontals, as pointed
	  out by Santhosh Thottingal.

2012-02-19 20:05 +0000 [r2074]  Stevan_White:

	* FreeSans.sfd: Malayalam: corrected l_voc_mal
	  and ll_voc_mal, which were completely wrong, as pointed out by
	  Santhosh Thottingal.

2012-02-19 19:37 +0000 [r2073]  Stevan_White:

	* FreeSerif.sfd: Oriya: Made vowels combine more
	  correctly. More glyph clean-up.

2012-02-19 11:03 +0000 [r2072]  Stevan_White:

	* FreeSerif.sfd: Oriya: more glyph clean-up

2012-02-19 00:51 +0000 [r2071]  Stevan_White:

	* FreeSerif.sfd: Oriya: clean-up, glyph
	  validation of main Unicode range.

2012-02-18 21:50 +0000 [r2070]  Stevan_White:

	* FreeSerif.sfd: Gujarati: Removed overlaps,
	  cleaned up resulting issues.

2012-02-18 19:34 +0000 [r2069]  Stevan_White:

	* FreeSerif.sfd: Gujarati: main glyph clean-up
	  done. some mods to glyph shapes . added a few i-widening table
	  entries.

2012-02-18 18:16 +0000 [r2068]  Stevan_White:

	* FreeSans.sfd: Malayalam: positioning tables
	  for below marks l_voc_mal and ll_voc_mal, as well as for reph.
	  (consult latest development version of Meera). Currency: made
	  indian Rupee sign a bit wider

2012-02-18 00:02 +0000 [r2067]  Stevan_White:

	* FreeSerif.sfd: Gujarati: further glyph
	  clean-up (almost there!). Adjusted marks. Made new lookups to
	  pick letter i based on width of the letter it goes over.

2012-02-17 14:42 +0000 [r2066]  Stevan_White:

	* FreeSerif.sfd: Gujarati: mostly messing with
	  mark positioning: got rid of carat-reph forms.

2012-02-17 11:28 +0000 [r2065]  Stevan_White:

	* FreeSerif.sfd: Gujarati: further glyph
	  clean-up, adjustments to mark placement.

2012-02-17 01:44 +0000 [r2064]  Stevan_White:

	* FreeSerif.sfd: Gujarati: replaced ha_half,
	  added some lookups for above marks.

2012-02-17 00:14 +0000 [r2062-2063]  Stevan_White:

	* FreeSerif.sfd: Correction: kerning tables were
	  blown out in commit 2056. There were several bad crashes about
	  that time. The effect seems to be: all kern table entries are set
	  to 0.

	* sfd/Makefile, FreeSansBold.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: kerning tables restored.
	  Problem happened in commit 2096

2012-02-16 23:41 +0000 [r2061]  Stevan_White:

	* FreeSerif.sfd: Gujarati: more glyph clean-up,
	  mostly in auxiliaries. Just noticed all kern tables have been
	  zeroed again (thank you, FontForge).

2012-02-16 21:11 +0000 [r2060]  Stevan_White:

	* FreeSerif.sfd: Gujarati: glyph clean-up of
	  Unicode range, some of auxiliary glyphs.

2012-02-15 20:16 +0000 [r2059]  Stevan_White:

	* FreeSerif.sfd: Gujarati: more glyph clean-up,
	  re-naming. much more to do.

2012-02-15 17:12 +0000 [r2058]  Stevan_White:

	* FreeSerif.sfd: Gujurati: removed anusvara and
	  nukta lookups in preference of positioning. general glyph
	  clean-up. deleted a bunch of unused characters. Oriya: general
	  glyph clean-up.

2012-02-14 23:59 +0000 [r2057]  Stevan_White:

	* FreeSerif.sfd: Gujarati: Again, from Samak
	  Gujarati font (full GPL 3.0 with font exception) Scaled slightly
	  to fit bounds, moved some letters, renamed a few. Lots more to
	  do.

2012-02-14 22:35 +0000 [r2056]  Stevan_White:

	* FreeSerif.sfd: reverting previous commit.
	  lookup tables somehow got damaged

2012-02-14 20:40 +0000 [r2055]  Stevan_White:

	* FreeSerif.sfd: Gujarati: From Samak Gujarati
	  font (full GPL 3.0 with font exception) Scaled slightly to fit
	  bounds, moved some letters, renamed a few. Lots more to do.

2012-02-14 19:37 +0000 [r2054]  Stevan_White:

	* FreeSans.sfd: Gujarati: Deleted range, over
	  questions of its provenance. Tut mir leid.

2012-02-13 23:16 +0000 [r2052-2053]  Stevan_White:

	* FreeSerif.sfd: Tamil: cleaned up 'virama'
	  glyph.

	* FreeSerif.sfd: Gurmukhi: cleaned up 'aa'
	  glyph.

2012-02-13 23:11 +0000 [r2051]  Stevan_White:

	* FreeSerif.sfd: Gurmukhi: cleaned up 'a' glyph.

2012-02-13 23:06 +0000 [r2049-2050]  Stevan_White:

	* FreeSerif.sfd: Oriya: compacted auxiliary
	  glyphs a bit.

	* FreeSerif.sfd: Oriya: removed most holonto and
	  nukta forms in preference for positioning lookups

2012-02-13 21:08 +0000 [r2048]  Stevan_White:

	* FreeSans.sfd: Gujarati: made to validate

2012-02-13 20:55 +0000 [r2047]  Stevan_White:

	* FreeSans.sfd: Gujarati: regularized horizontal
	  placement and spacing, mostly increasing spacing a bit. Got rid
	  of nukta substitution table in favor of positioning lookup;
	  likewise with all anusvara lookups. The glyphs of this range are
	  still very erratic, and the weight is substantially greater than
	  that of the rest of the face.

2012-02-13 13:56 +0000 [r2046]  Stevan_White:

	* FreeSans.sfd: Gujarati: shifted entire range
	  up by 44EM, so it didn't dangle so. Fiddled with mark placement.

2012-02-13 00:01 +0000 [r2045]  Stevan_White:

	* FreeSerif.sfd: Gurmukhi: improved vowel a.

2012-02-12 23:29 +0000 [r2044]  Stevan_White:

	* FreeSerif.sfd: Gurmukhi: replaced range with
	  glyphs from 'singh' metafont by Amarjit Singh
	  http://www.tex.ac.uk/tex-archive/language/gurmukhi/ The 'singh'
	  font is properly GPL. And the overall look is much more like
	  serif than what had been there. Status: added a few letters and
	  tables. Two marks were unrepresented in 'singh', and are
	  leftovers. I made a ya_halant and a ra below-form. Also made
	  substitution and positioning tables. Worst: the vowel 'a' is very
	  messed up -- much too heavy; hand-drawn. Will have to replace it.
	  Also need to review the missing marks. Pity there's no bold...

2012-02-12 22:02 +0000 [r2043]  Stevan_White:

	* tools/utility/metafont/bulk_eps_import.py (added),
	  tools/utility/metafont (added): Tools for
	  extracting glyphs from metafont files

2012-02-10 23:22 +0000 [r2042]  Stevan_White:

	* FreeSansBold.sfd: Gurmukhi: more of same.

2012-02-10 23:18 +0000 [r2041]  Stevan_White:

	* FreeSansBold.sfd: Gurmukhi: adjusted GPOS
	  entries

2012-02-10 22:58 +0000 [r2040]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd: Gurmukhi: various tweeks to
	  ligatures. Gujarati: various tweeks to ligatures.

2012-02-10 19:01 +0000 [r2039]  Stevan_White:

	* FreeSansBold.sfd: Gurmukhi: toward version in
	  medium. Geometric Shapes: made dotted circle.

2012-02-10 17:57 +0000 [r2038]  Stevan_White:

	* FreeSerif.sfd: Oriya: added letters to
	  complete range. re-named letters for consistency with other indic
	  languages. fixed a broken table.

2012-02-10 16:46 +0000 [r2037]  daniel_j:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Unicode 6.1 addition in
	  FreeSerif: H-hook (U+A7AA) for Chadian languages

2012-02-10 15:46 +0000 [r2036]  Stevan_White:

	* FreeSerif.sfd: Oriya: Re-introduced, using
	  Samak Oriya, a GPL v.3 + font exception.
	  http://sarovar.org/projects/samyak/ Already the original is
	  altered in several ways. The glyphs need a lot of work.

2012-02-10 12:03 +0000 [r2035]  Stevan_White:

	* tools/report/OpenType/UnicodeRanges.py: corrected
	  Private Use range

2012-02-09 18:50 +0000 [r2034]  Stevan_White:

	* FreeSans.sfd: Gujarati: added abbreviation
	  character

2012-02-09 18:29 +0000 [r2033]  Stevan_White:

	* FreeSans.sfd: Gurmukhi: named glyphs better.
	  Malayalam: corrected problems caused by recent inconsistent
	  naming.

2012-02-09 16:36 +0000 [r2032]  Stevan_White:

	* FreeSans.sfd: Gujurati: named characters
	  better -- makes it easier to see table problems. Found one error
	  in tables, fixed. There are others.

2012-02-08 19:55 +0000 [r2031]  Stevan_White:

	* FreeSerif.sfd: Tamil: several new tables in
	  effort to resolve spacing bug.

2012-02-08 15:37 +0000 [r2030]  Stevan_White:

	* FreeSerif.sfd: Tamil: fixed shrii ligature

2012-02-08 15:15 +0000 [r2029]  Stevan_White:

	* FreeSerif.sfd: Tamil: Toward resolution of bug
	  #35479: Tamil in FreeSerif: some ligatures wrong
	  https://savannah.gnu.org/bugs/index.php?35479 Removed GSUB tables
	  about positioning the virama; implemented this with a GPOS table
	  and anchor points. Corrected tables for sshri, kss ligatures.
	  Toward resolution of bug #35480: Tamil in FreeSerif: placement of
	  vowel marks poor https://savannah.gnu.org/bugs/index.php?35480
	  Made a vertical ii sign, made a contextual chain that replaces
	  the normal ii with this one for certain letters.

2012-02-08 01:41 +0000 [r2028]  Stevan_White:

	* FreeSerif.sfd: Malayalam: Added subtable for
	  nta (mal_n1rh), Separated 'psts' subtable into u and uu ones, and
	  everything else, hoping somehow to deal with the orthographic
	  reform.

2012-02-07 23:52 +0000 [r2026-2027]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Malayalam: some additions from
	  recent Unicode (historical letters) Rearranged akhan lookups.

	* tools/report/OpenType/UnicodeRanges.py: Recent
	  Unicode additions

2012-02-06 19:24 +0000 [r2025]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd: Latin: Fixed broken
	  kerning. Problem was: there was an entry for 'f' as the first
	  letter in two subtables of the same lookup. The second subtable
	  was ignored, even though there was no match for the second letter
	  in the first lookup. That is, within a kern lookup, the first
	  letters of the pairs should be unique.

2012-02-06 14:41 +0000 [r2023-2024]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Latin: put dflt back in
	  Standard Ligature language list

	* FreeSerif.sfd: Devanagari: added glyphs for
	  Bompay, Calcutta, Nepali styles from velthuis. made Style Set
	  lookups for them. Latin: put dflt back in the Standard Ligatures
	  language list

2012-02-05 23:59 +0000 [r2022]  Stevan_White:

	* FreeSerifBold.sfd: Devanagari: named more
	  Unicode glyphs, tried to make tables more like latest Serif. Know
	  of one inconsistency remaining.

2012-02-05 12:18 +0000 [r2021]  Stevan_White:

	* FreeSerif.sfd: Devanagari: re-worked overlarge
	  reph-anudatta. Regularized table naming.

2012-02-04 21:50 +0000 [r2019-2020]  Stevan_White:

	* FreeSerif.sfd: Devanagari: lookup tables --
	  for the first time, test matches big table of ligatures in
	  velthuis (wrt Sanskrit and Hindi).

	* FreeSans.sfd: Devanagari: named a bunch of
	  Unicode glyphs

2012-02-03 15:09 +0000 [r2018]  Stevan_White:

	* FreeSerif.sfd: Devanagari: another
	  re-arrangement of tables for velthuis compliance. problems
	  persist.

2012-02-01 20:59 +0000 [r2017]  Stevan_White:

	* FreeSerif.sfd: Devanagari: complete
	  re-structuring of tables in attempt to resemble better the specs
	  about Indic tables, and produce ligatures of the velthuis font.
	  Hebrew: Support raising of Yiddish vowel marks.

2012-02-01 19:29 +0000 [r2016]  daniel_j:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Unicode 6.1 additions
	  in FreeSans: - Tifinagh Ye, Yo - Armenian dram - African H with
	  hook

2012-01-31 12:02 +0000 [r2015]  Stevan_White:

	* FreeSansBoldOblique.sfd: Hebrew: last commit
	  accidentlly widened right side of many letters

2012-01-31 11:51 +0000 [r2014]  Stevan_White:

	* FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Hebrew: more
	  adjustment of points: bold--shifted letters to right

2012-01-31 11:08 +0000 [r2013]  Stevan_White:

	* FreeSansOblique.sfd: Hebrew: some adjustment
	  of mark positioning

2012-01-31 10:58 +0000 [r2012]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Hebrew: adjustments to
	  positioning marks; italic moved letter yod

2012-01-31 10:33 +0000 [r2011]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Hebrew: deleted incorrect
	  ligature lookup

2012-01-31 10:24 +0000 [r2010]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Hebrew: deleted
	  incorrect ligature lookup

2012-01-31 10:13 +0000 [r2009]  Stevan_White:

	* FreeSansBold.sfd: Hebrew: adjustments to mark
	  positioning, a bit of tidying of letters. Latin: deleted a
	  mistakenly copied table.

2012-01-31 09:47 +0000 [r2008]  Stevan_White:

	* FreeSansBold.sfd: Hebrew: added mark
	  positioning

2012-01-31 00:15 +0000 [r2007]  Stevan_White:

	* FreeSans.sfd: Hebrew: should rememdy bug
	  #35409: Incorrect placement of khirik-yud for Yiddish in FreeSans
	  (At least for FreeSans medium)

2012-01-28 14:26 +0000 [r2006]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Devanagari: roman--adjusted
	  auxiliary glyph names to better fit bold bold: several
	  corrections

2012-01-28 12:37 +0000 [r2005]  Stevan_White:

	* FreeSerifBold.sfd: Devanagari: various repairs
	  to glyphs and tables

2012-01-28 01:36 +0000 [r2004]  Stevan_White:

	* FreeSerifBold.sfd: Devanagari: added glyphs
	  from velthuis font's dvngb10.pfb, added subtables from FreeSerif.
	  Still plenty missing.

2012-01-27 22:13 +0000 [r2003]  Stevan_White:

	* FreeSerif.sfd: Devanagari: a couple more
	  tables to handle velthuis corner cases.

2012-01-26 22:52 +0000 [r2002]  Stevan_White:

	* FreeSerif.sfd: Devanagari: fixed a few lookup
	  problems. several more remain.

2012-01-25 14:24 +0000 [r2000-2001]  Stevan_White:

	* FreeSerif.sfd: Devanagari: adjustments to mark
	  positioning

	* FreeSans.sfd: Latin: adjustments to accents
	  Devanagari: re-working tables for better support of Hindi vs
	  Sanskrit

2012-01-25 12:09 +0000 [r1999]  Stevan_White:

	* FreeSerif.sfd: Devanagari: Named some letters;
	  some table clean-up. Devanagari Extended: Added combining
	  cantillation marks.

2012-01-24 15:02 +0000 [r1998]  Stevan_White:

	* FreeSerif.sfd: Devanagari: Most of the
	  previous commits about Hindi were in error: two columns of the
	  velthuis manual for Modern Hindi and Old Hindi were swapped. This
	  now implements Modern Hindi, except for one conjunct. Old Hindi
	  conjuncts are moved to separate tables, but Old Hindi is not
	  specifically supported.

2012-01-23 18:48 +0000 [r1997]  Stevan_White:

	* FreeSerif.sfd: Devanagari: several table
	  corrections, based on new tests.

2012-01-23 10:18 +0000 [r1996]  Stevan_White:

	* FreeSerif.sfd: Devanagari: table for
	  consonants that don't take half-form, other conjunct table
	  tweeks.

2012-01-21 23:20 +0000 [r1995]  Stevan_White:

	* FreeSerif.sfd: Devanagari: many small
	  corrections to GSUB.

2012-01-20 13:44 +0000 [r1994]  Stevan_White:

	* FreeSerif.sfd: Devanagari: Sanskrit/Hindi
	  distinction now seems to be working. (Was order of two 'akhn'
	  tables, one specialized for Sanskrit. Don't understand--thought
	  always all matching tables were executed.)

2012-01-20 01:41 +0000 [r1993]  Stevan_White:

	* FreeSerif.sfd: Devanagari: some clean-up,
	  split the akhn table into general (modern Hindi) and Sanskrit.
	  But not the Sanskrit doesn't activate in any environment, with
	  lang='sa'. However, seems to work for Hindi.

2012-01-19 16:00 +0000 [r1992]  Stevan_White:

	* FreeSerif.sfd: Devanagari: more tweeks to
	  rakaar slash

2012-01-19 15:30 +0000 [r1991]  Stevan_White:

	* FreeSerif.sfd: Devanagari: corrected simple
	  Rakaar forms again

2012-01-19 13:52 +0000 [r1990]  Stevan_White:

	* FreeSerif.sfd: Devanagari: corrected spacing
	  problem with combining rakaar slash.

2012-01-19 00:52 +0000 [r1989]  Stevan_White:

	* FreeSerif.sfd: Devanagari: fixed last
	  consonant-virama-ra conjuncts

2012-01-19 00:32 +0000 [r1988]  Stevan_White:

	* FreeSerif.sfd: Devanagari: fixed
	  consonant-virama-consonant-virama conjuncts

2012-01-19 00:04 +0000 [r1987]  Stevan_White:

	* FreeSerif.sfd: Devanagari: much done on tables
	  for compound letters. a couple of compounds were corrected.

2012-01-18 14:40 +0000 [r1986]  Stevan_White:

	* FreeSerif.sfd: Devanagari: Much messing with
	  combined forms for vowels+rakh, finally got features going for
	  distinction of Rakaar slash vs Rakaar below mark.

2012-01-16 00:36 +0000 [r1985]  Stevan_White:

	* FreeSerif.sfd: Devanagari: more lookups
	  working. Still a couple of letters wrong.

2012-01-14 22:43 +0000 [r1984]  Stevan_White:

	* FreeSerif.sfd: Devanagari: with help of Z.
	  Wagner, and a TeX cmap file making the connection betwen
	  conjuncts in the Velthuis font and Unicode, inserted the Velthuis
	  au auxiliary glyphs and made lookup tables Mostly working.

2012-01-13 12:08 +0000 [r1983]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Devanagari: (sans) better names
	  for a couple of characters, (serif) made names more like sans,
	  added Kashmiri letters to complete Unicode 6.0 range

2012-01-13 00:25 +0000 [r1982]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: PS: Added UniqueId TT
	  Names: Added UniqueID Devanagari: (serif) added ABVM, BLWM tables
	  and anchors Devanagari: (sans) small tweaks mostly regarding mark
	  placement

2012-01-10 17:17 +0000 [r1981]  Stevan_White:

	* FreeSans.sfd: Devanagari: a couple of
	  touch-ups: corrected 'abvs' entry for ausign-anusvera.

2012-01-10 16:12 +0000 [r1980]  Stevan_White:

	* FreeSans.sfd: Devanagari: re-named most
	  auxiliary glyphs to make tables more sensible.

2012-01-10 12:56 +0000 [r1979]  Stevan_White:

	* FreeSans.sfd: Devanagari: simplified and
	  regularized some glyph names

2012-01-10 02:07 +0000 [r1978]  Stevan_White:

	* FreeSans.sfd: Gurmukhi: tried to get marks
	  under control. Added GPOS tables, anchors.

2012-01-10 00:48 +0000 [r1977]  Stevan_White:

	* FreeSans.sfd: Devanagari: copied a few
	  ligatures that were in gargi but not in FreeSans. There are still
	  a few more, but I'm not clear about them. Tables: moved auxiliary
	  letters for Indic around a bit.

2012-01-09 20:56 +0000 [r1976]  Stevan_White:

	* FreeSans.sfd: Indic tables: rearranged
	  Devanagari: re-named the GPOS anchors Lagin: iogonek lookup had
	  gotten somehow fouled up

2012-01-08 01:32 +0000 [r1975]  Stevan_White:

	* FreeSerif.sfd: Devanagari: copied in rest of
	  Unicode chars from velthuis font dvng10.pfb with advice from
	  Zdeněk Wagner. Next plan: copy in ligatures from same file and
	  use a cmap file provided by Zdeněk to construct a basic GSUB
	  table.

2012-01-08 00:25 +0000 [r1974]  Stevan_White:

	* FreeSerif.sfd: Latin, Cyrillic: tweeks to
	  accent marks. Devanagari: beginning to replace range with
	  Velthuis font.

2012-01-07 19:42 +0000 [r1973]  Stevan_White:

	* FreeSans.sfd: Devanagari: widened space
	  between verticals in U+0906 by request. Made a lot of references
	  in range. Added Kashmiri letters from latest Unicode version.

2012-01-03 20:17 +0000 [r1972]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Combining Diacritics:
	  forgot to add Cyrillic mark for previous commit

2012-01-03 20:08 +0000 [r1971]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Cyrillic: Added mark
	  for diacriticals on capital letters. toward bug #35225: Combining
	  Diacritical Marks improperly positioned for Cyrillic capital
	  letters https://savannah.gnu.org/bugs/?35225 Latin: re-thought
	  spacing in bold, bold oblique, and re-positioned many accents for
	  legibility

2011-12-31 20:09 +0000 [r1970]  Stevan_White:

	* FreeSans.sfd: Miscellaneous Symbols: hammer
	  and sickle, smileface, frown face, courtesy Sander van Geloven

2011-12-28 14:05 +0000 [r1969]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Mathematical Operators:
	  revision of v. and h. placement and spacing. Reduced height of
	  partialdiff in some faces.

2011-12-28 13:40 +0000 [r1968]  Stevan_White:

	* FreeSerif.sfd: Mathematical Operators: lowered
	  nabla, made partialdiff of normal height

2011-12-25 13:54 +0000 [r1967]  Stevan_White:

	* FreeSans.sfd: Miscellaneous Symbols: uni2639
	  -- a (first) contribution by Sander van Geloven

2011-12-25 13:37 +0000 [r1966]  Stevan_White:

	* FreeSerif.sfd: Mathematical Operators:
	  improved symbols having vertical lines

2011-12-19 13:55 +0000 [r1965]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: General Punctuation:
	  regularize width of bullet chars Geometrical Shapes: made new and
	  regularized width of bullet chars Mathematical Operators: messed
	  with bullet-lige operators

2011-12-18 17:35 +0000 [r1964]  Stevan_White:

	* FreeSerif.sfd: Mathematical Operators: fixed
	  some spacing issues.

2011-12-18 13:52 +0000 [r1963]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd: Latin, Punctuation:
	  slash and some vertical lines were wrongly spaced.

2011-12-17 01:39 +0000 [r1962]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Mathematical Operators: in
	  sans, added some angle glyphs by request. General vertical
	  metrecs: fixed some mistakes, re-thought overall.

2011-12-16 12:17 +0000 [r1961]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Technical Symbols: made
	  extensible mathematical symbol pieces for sans, adjusted
	  horizontal bracket in serif.

2011-12-16 00:10 +0000 [r1960]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Sans: glyphwise same
	  vertical adjustments as with Serif, same caveats. All faces:
	  re-thought metrics. Hope they make better sense now.

2011-12-15 18:02 +0000 [r1959]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: General alterations to make
	  fit in vertical bounds (-300,900). Problems remain in Sinhala and
	  Malayalam. Esp. note two Bengali "vocalic" glyphs, used only for
	  transcription of Sanskrit, were altered and moved.

2011-12-14 22:31 +0000 [r1958]  Stevan_White:

	* FreeSerif.sfd: Malayalam: some glyph clean-up

2011-12-14 21:49 +0000 [r1957]  Stevan_White:

	* FreeSerif.sfd: Changed ascent and descent of a
	  number of letters out of bounds (several in Arabic, also
	  Devnagari). The worst are in Malayalam though, and they remain.

2011-12-14 15:59 +0000 [r1956]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Supplementary
	  Punctuation: made a few new ones

2011-12-12 20:52 +0000 [r1955]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd: Latin Extended-B:
	  Same with tz ligature as in roman

2011-12-12 18:42 +0000 [r1954]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin Extended-B:
	  re-thought replacement for tz ligature in German. Note: the
	  Unicode tz character is used in Mayanist texts.

2011-12-11 23:25 +0000 [r1953]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Complete set of lowercse
	  latin super/subscripts for normal and bold, with 'subs' and
	  'sups' lookups.

2011-12-11 20:59 +0000 [r1952]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin: (roman) re-thought
	  tz ligature Number Forms: adjusted vulgar fractions table, to
	  make all work in xelatex

2011-12-09 11:22 +0000 [r1951]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin: avoid fi, fl etc
	  ligatures in Turkish. Re-thought TZ, tz ligatures.

2011-12-09 00:08 +0000 [r1950]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Proportional Numbers,
	  Slashed Zero

2011-12-08 23:57 +0000 [r1948-1949]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Greek: added digamma

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Serif faces: lots of work
	  on numeric substitutions 'onum' Oldstyle Figures 'pnum'
	  Proportional Numbers 'tnum' Tabular Numbers 'lnum' Lining Figures
	  'zero' Slashed Zero Cyrillic for Bulgarian: activated new-style
	  forms with 'ss01' Style Set 1 Cyrillic regular: Bit of kerning.
	  (FontForge still complains about non-Unicode glyphs)

2011-12-06 22:40 +0000 [r1947]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: More additions to 'sups'
	  table in roman, bold. Added a few Phonetic Extensions to
	  italic... but this should wait for a set of small caps.

2011-12-06 21:47 +0000 [r1945-1946]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Changed Bulgarian lookup
	  tag from cv00 to ss01 bold: adjusted some superscript letter
	  spacings, minute, prime spacing normal: added proportional
	  numerals, tables tnum, lnum, pnum adjusted widths of some
	  superscript letters, tweeked some others added tables sups, subs

	* FreeSans.sfd: Latin: some kerning

2011-12-05 18:50 +0000 [r1944]  Stevan_White:

	* FreeSansBold.sfd: Latin etc: re-spaced,
	  re-kerned letter a

2011-12-05 13:19 +0000 [r1943]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Cyrillic: tweeks to glyphs,
	  spacing for alternate Bulgarian letters.

2011-12-05 12:47 +0000 [r1942]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: Cyrillic: kerning and spacing.

2011-12-05 10:27 +0000 [r1941]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Cyrillic: more work on
	  alternative letters for Bulgarian, Serbian

2011-12-04 23:46 +0000 [r1939-1940]  Stevan_White:

	* FreeSerif.sfd: Cyrillic: adjust spacing of
	  some Bulgarin letters

	* FreeSerifBold.sfd: Latin: bit o kerning

2011-12-04 14:00 +0000 [r1938]  Stevan_White:

	* FreeSerif.sfd: Cyrillic: expanded/corrected
	  letters for Bulgarian alternate forms.

2011-12-03 22:45 +0000 [r1937]  Stevan_White:

	* FreeSerif.sfd: Cyrillic: alternative small
	  letters for Bulgarian, with lookup table.

2011-12-03 18:04 +0000 [r1936]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMonoBoldOblique.sfd: glyph clean-ups
	  related to recent commits, toward validation

2011-12-03 17:29 +0000 [r1935]  Stevan_White:

	* FreeSansBoldOblique.sfd: Mathematical
	  Operators: two were out of place

2011-12-03 17:25 +0000 [r1934]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Arrows: made new
	  ones, corrected others

2011-12-03 15:48 +0000 [r1933]  Stevan_White:

	* FreeSerif.sfd: Currency Symbols: added
	  spesmilo

2011-12-03 15:04 +0000 [r1932]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeSerifBold.sfd: Arrows: lots of additions,
	  corrections

2011-12-03 13:32 +0000 [r1931]  Stevan_White:

	* FreeSansOblique.sfd: Arrows: made glyphs so
	  range is similar to rest of Sans.

2011-12-03 12:59 +0000 [r1930]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Geometric Shapes:
	  made minimal set. Mathematical Operators: made a few more glyphs,
	  corrected a couple. Number Forms: deleted range in bold faces --
	  can't properly represent all.

2011-12-03 01:02 +0000 [r1929]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMonoOblique.sfd: Miscellaneous Technical
	  Symbols: made a few important ones.

2011-12-03 00:33 +0000 [r1928]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Mathematical
	  Operators: made a few more glyphs. some clean-up.

2011-12-02 19:12 +0000 [r1927]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Mathematical
	  Operators: revised a few glyphs in medium, got a sort of minimal
	  set into other styles

2011-12-02 15:03 +0000 [r1926]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Mathematical
	  Operators: made yet more.

2011-12-02 13:57 +0000 [r1925]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Mathematical Operators:
	  revised a few in medium (weight and spacing), made a lot of new
	  characters for other styles.

2011-12-02 10:53 +0000 [r1924]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd: Mathematical Operators
	  (etc): more operators (bold), revisions (medium)

2011-12-02 02:32 +0000 [r1923]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd: Mathematical Operators:
	  tried to make of more uniform weight (medium) made several new
	  (bold)

2011-12-01 20:22 +0000 [r1921-1922]  Stevan_White:

	* FreeSerif.sfd: Combining Half Marks: adjusted
	  mark anchors

	* FreeMono.sfd: Cyrillic: fix to old letter
	  Arabic: hah_final made all contours- no refs

2011-12-01 19:34 +0000 [r1919-1920]  Stevan_White:

	* tools/generate/TrueType,
	  tools/generate/OpenType,
	  tools/generate/MacTT: vim-style file type comment

	* tools/generate/buildutils.py: Version string to
	  comply with OpenType recommendation.

2011-12-01 18:37 +0000 [r1918]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMonoBoldOblique.sfd: Misc. Math. Symbols:
	  made reverse logical not (req. for some MS codepages) Math.
	  Operators: made a few while I was around

2011-11-30 20:50 +0000 [r1917]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoOblique.sfd: Cyrillic: made uk letters
	  digraph, due to complaint by user

2011-11-30 13:20 +0000 [r1916]  Stevan_White:

	* tools/report/OpenType/UnicodeRanges.py: Corrected
	  Combining Half Marks

2011-11-30 13:15 +0000 [r1915]  Stevan_White:

	* FreeSerif.sfd: Combining Half Marks: made
	  letters, filled range, added mark anchors

2011-11-30 12:26 +0000 [r1914]  Stevan_White:

	* Makefile: targets for woff packages

2011-11-30 12:15 +0000 [r1913]  Stevan_White:

	* README: Added lines on WOFF format.

2011-11-30 12:05 +0000 [r1912]  Stevan_White:

	* notes/webfont_guidelines.txt: added WOFF

2011-11-28 11:41 +0000 [r1911]  Stevan_White:

	* FreeSerif.sfd: Latin: some light kerning

2011-11-28 00:17 +0000 [r1910]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Grid Fitting: made
	  uniformly Version 1 ("Optimized for Cleartype" won't stay on in
	  FontForge ?)

2011-11-27 20:52 +0000 [r1909]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: general glyph alterations
	  toward validation. some of these are due to some bug in FontForge
	  algorithms -- nothing was apparently wrong with the glyphs.

2011-11-27 00:34 +0000 [r1907-1908]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin: some kerning

	* sfd/Makefile,
	  tools/test/checkGlyphNumbers.py: Made to function
	  either from directory or from Makefile.

2011-11-25 11:38 +0000 [r1906]  Stevan_White:

	* FreeSerifBold.sfd: correction to 'ccmp' table
	  for Vietnamese 2

2011-11-25 11:29 +0000 [r1905]  Stevan_White:

	* FreeSerif.sfd: Added some fractions to the
	  'frac' lookup

2011-11-25 11:17 +0000 [r1904]  Stevan_White:

	* FreeSerif.sfd: corrected some entries in
	  'frac' lookup

2011-11-25 11:08 +0000 [r1903]  Stevan_White:

	* FreeSerif.sfd: Number Forms: adjusted diagonal
	  fractions Cyrillic: clean-up of Ksi contour

2011-11-25 10:36 +0000 [r1901-1902]  Stevan_White:

	* tools/report/kernclasses.py (added): Tabular
	  report of kerning classes in fonts

	* FreeSerifBoldItalic.sfd: Latin: named 2 glyphs
	  referred to in kern tables

2011-11-25 10:16 +0000 [r1900]  Stevan_White:

	* tools/test/checkGlyphNumbers.py: corrected for
	  directory move

2011-11-24 18:35 +0000 [r1899]  Stevan_White:

	* FreeSans.sfd: Cyrillic: made Ksi, ksi

2011-11-24 17:58 +0000 [r1898]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd: Latin: for f and
	  other hooked letters: special punctuation lookup

2011-11-24 17:39 +0000 [r1896-1897]  Stevan_White:

	* FreeSerif.sfd: General Punctuation: adjusted,
	  regularized spacing of primes

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd: Latin: f and other
	  hooked letters: put more of hook into kerning

2011-11-24 16:51 +0000 [r1895]  Stevan_White:

	* FreeSerif.sfd: Cyrillic: improve spacing of
	  Ksi

2011-11-24 15:05 +0000 [r1894]  Stevan_White:

	* FreeSans.sfd: Cyrillic: Made some Old Church
	  Slavonic letters, by request.

2011-11-23 15:12 +0000 [r1893]  Stevan_White:

	* FreeSans.sfd: Malayalam: glyph clean-up toward
	  validation

2011-11-23 01:19 +0000 [r1892]  Stevan_White:

	* FreeSans.sfd: Malayalam conjuncts: numerous
	  contour improvements toward validation

2011-11-22 00:25 +0000 [r1890-1891]  Stevan_White:

	* tools/utility/log2changelog_CVS.pl (removed): This
	  is what I meant to do in the previous commit

	* tools/utility/log2changelog.pl (removed),
	  tools/utility/log2changelog_CVS.pl (added): No
	  need for this script now the VC is changed to SVN; there are
	  other tools for SVN that perform the same task.

2011-11-21 19:01 +0000 [r1889]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSansOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: More individual glyph
	  tweeks toward validation. (Discovered trigger to one FontForge
	  bug: contour 1st node at sharp point.)

2011-11-21 11:15 +0000 [r1888]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Overall glyph tweeks
	  to improve FontForge validation. Note some of these can be called
	  messy node problems, others are due to some malfunction of
	  FontForge.

2011-11-20 14:36 +0000 [r1887]  Stevan_White:

	* ChangeLog: brought up to date

2011-11-18 09:55 +0000 [r1886]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Unfortunately several
	  unrelated changes got balled up in this commit. Generally, slight
	  re-wording of license (program ->font) Serif, Sans: Latin, Greek,
	  Cyrillic and phonetic: substantial changes to glyph bearings,
	  attempt to normalize; re-did kerning angled slightly
	  hungarianumlaut and double-grave accents toward one another.
	  lcaron, dcaron, tcaron: prefer an acute accent to apostrophe.
	  Note this messes up some letters d, l, t with acute, for
	  languages using IPA. Serif: Cyrillic Ouk: fixed bug #34687: Bad
	  shape of Cyrillic U+0478 & u+0479 Cyrillic Extended-A: increased
	  hight of combining marks slightly (regular face only) Italic
	  Cyrillic: re-did Serbian sha and te with longer line. Bengali,
	  Tamil, Thai, Ethiopic, Georgian, Cyrillic Extended-B, Latin
	  Extended-D, Arabic Presentation forms, Letterlike and
	  Miscellaneous Math Symbols: glyph clean-ups longs: fixed bug
	  #34841: U+017F LONG S upright a bit wrong Sans: acute, grave,
	  hungarianumlaut and double-grave accents on capitals somewhat
	  shorter to better fit in the line,

2011-11-18 09:46 +0000 [r1885]  Stevan_White:

	* FreeMono.sfd: Arabic: positional
	  substitutions; added a couple of characters

2011-11-12 14:48 +0000 [r1883-1884]  Stevan_White:

	* sfd/Makefile: target for making WOFF files

	* tools/generate/WOFF: removed one unused package
	  better documentation

2011-11-12 14:37 +0000 [r1882]  Stevan_White:

	* tools/generate/WOFF: removed debug strings

2011-11-12 14:34 +0000 [r1881]  Stevan_White:

	* tools/generate/WOFF (added): Generator for Web
	  Open Font Format files

2011-11-05 16:43 +0000 [r1880]  Stevan_White:

	* FreeMono.sfd: Arabic: addded three letters

2011-11-05 15:06 +0000 [r1879]  Stevan_White:

	* FreeMono.sfd: Arabic: Many incorrectly 0-width
	  chars made full width. Implemented marks, mostly for vertical
	  mark placement.

2011-11-04 22:28 +0000 [r1878]  Stevan_White:

	* FreeMono.sfd: Arabic: added several
	  characters. nearing functionality of serif version.

2011-11-04 14:55 +0000 [r1877]  Stevan_White:

	* FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: fixed several
	  substitution and glyph problems. See discussion
	  https://savannah.gnu.org/bugs/?34190

2011-11-03 17:55 +0000 [r1876]  Stevan_White:

	* FreeSerifBold.sfd: Arabic: improved meemfinal

2011-11-03 16:07 +0000 [r1875]  Stevan_White:

	* FreeSerif.sfd: Punctuation Suppl: a few
	  additions. Geometrical Shapes, Math Suppl: mostly spacing.

2011-11-03 13:18 +0000 [r1873-1874]  Stevan_White:

	* tools/report/OpenType/UnicodeRanges.py: typo in
	  doc

	* FreeSerif.sfd: Removed excess entry for
	  uni29b4.

2011-11-03 12:55 +0000 [r1872]  Stevan_White:

	* tools/report/OpenType/UnicodeRanges.py: Added to
	  Supplemental Punctuation

2011-11-03 12:15 +0000 [r1871]  Stevan_White:

	* FreeSerif.sfd: Punctuation: named some glyphs,
	  re-sized some spaces, added some Supplemental

2011-11-03 09:48 +0000 [r1870]  Stevan_White:

	* tools/utility/fontforge-interp.sh: more doc

2011-11-03 01:40 +0000 [r1869]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Glyph clean-up, and change
	  of problematic glyph names

2011-11-03 01:21 +0000 [r1868]  Stevan_White:

	* tools/test/validate.py: Better exception handling

2011-11-03 01:09 +0000 [r1867]  Stevan_White:

	* FreeSerifBold.sfd: Some glyph clean-up

2011-11-03 00:59 +0000 [r1866]  Stevan_White:

	* FreeSerif.sfd: Some glyph clean-up.

2011-11-03 00:51 +0000 [r1862-1865]  Stevan_White:

	* tools/utility/fontforge-interp.sh (added): Wrapper
	  for fontforge.

	* tools/test/findBackLayers.py,
	  tools/test/validate.py,
	  tools/test/checkGlyphNumbers.py,
	  tools/test/isMonoMono.py: Call wrapper for
	  fontforge rather than calling a specific instance.

	* tools/generate/TrueType,
	  tools/generate/OpenType,
	  tools/generate/MacTT: Call wrapper for fontforge
	  rather than calling specific instance

	* tools/report/private_use.py,
	  tools/report/range_report.py,
	  tools/report/ligatureLookups.py: Use wrapper for
	  fontforge rather than calling explicit instance.

2011-11-03 00:45 +0000 [r1861]  Stevan_White:

	* sfd/Makefile: Made to execute fontforge from any
	  instance in PATH

2011-11-02 18:32 +0000 [r1859-1860]  Stevan_White:

	* FreeSerif.sfd: Arabic: named some glyphs

	* FreeMono.sfd: Arabic: corrected slot for one
	  letter.

2011-11-02 00:56 +0000 [r1858]  Stevan_White:

	* FreeMono.sfd: Arabic: most of intermediate
	  forms with lookups: A and B. Some general contour cleanup.

2011-10-31 22:12 +0000 [r1857]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Cyrillic: added
	  'locl' lookup for Serbian/Macedonian 'be'

2011-10-31 19:34 +0000 [r1855-1856]  Stevan_White:

	* FreeSerif.sfd: Spacing Modifier Letters:
	  horizontal space adjustments

	* FreeSans.sfd: Latin, Diacritics, etc:
	  hungarian umlaut, double grave adjustments

2011-10-31 19:27 +0000 [r1854]  Stevan_White:

	* FreeMono.sfd: Arabic: Drew most of main range.

2011-10-31 17:22 +0000 [r1853]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Latin, Diacriticals, etc:
	  adjustment on hungarian umlaut, double grave

2011-10-30 16:25 +0000 [r1852]  Stevan_White:

	* FreeSerif.sfd: Arabic: removed Arabic-only
	  ligatures lookup, as it seems to confuse Pango.

2011-10-30 12:51 +0000 [r1851]  Stevan_White:

	* FreeSerif.sfd: Arabic: changed size of tatweel

2011-10-30 11:07 +0000 [r1850]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd: Cyrillic: cursive
	  Macedonian gje. See bug 34688.
	  https://savannah.gnu.org/bugs/?34688

2011-10-29 23:12 +0000 [r1849]  Stevan_White:

	* FreeSerif.sfd: Arabic Presentation Forms-B:
	  adjustment to meem-final.

2011-10-29 22:01 +0000 [r1848]  Stevan_White:

	* FreeSerif.sfd: Spacing Modifier/Combining
	  Diacritics: double-gramve and Hungarian umlaut, to make them less
	  extremely wide and hard to accommodate. Made less steep, at
	  slightly different angles, following advice at
	  http://typophile.com/node/78623
	  http://diacritics.typo.cz/index.php?id=24

2011-10-29 18:53 +0000 [r1847]  Stevan_White:

	* FreeSerif.sfd: Arabic: Re-worked final meem.
	  Put Arabic-only ligatures in Arabic-only lookup. Put Allah in a
	  'dlig' lookup. Made tatweel shorter.

2011-10-29 12:09 +0000 [r1846]  Stevan_White:

	* FreeSerif.sfd: Arabic: marked some ending
	  ligatures as Arabic-language only, on advice from a Persan
	  speaker (comments to bug #34190)

2011-10-29 06:18 +0000 [r1845]  Stevan_White:

	* FreeSerif.sfd: General Punctuation: made
	  bullet-bullets same width as bullet

2011-10-29 04:44 +0000 [r1844]  Stevan_White:

	* FreeSerif.sfd: Punctuation, Geometric Shapes,
	  Miscellaneous Symbols, etc.: regularize bullet-like characters
	  somewhat in width and weight

2011-10-28 23:51 +0000 [r1843]  Stevan_White:

	* FreeSerifBold.sfd: Arabic Extended-A: Added 2
	  ligatures

2011-10-28 22:53 +0000 [r1842]  Stevan_White:

	* FreeSans.sfd: Cyrillic Supplement: letters for
	  Abkhaz and Azerbaijani, and Nivkh.

2011-10-28 22:16 +0000 [r1841]  Stevan_White:

	* FreeMono.sfd: Cyrillic Supplement: letters for
	  Abkhaz and Azerbaijani

2011-10-28 21:53 +0000 [r1840]  Stevan_White:

	* FreeMono.sfd: Cyrillic: Added Nivkh letters

2011-10-28 18:10 +0000 [r1839]  Stevan_White:

	* tools/utility/special-purpose/makeBraille.py
	  (added), tools/utility/special-purpose (added),
	  tools/utility/makeBraille.py (removed):
	  special-purpose directory for one-shot scripts, included as
	  examples.

2011-10-28 12:29 +0000 [r1838]  Stevan_White:

	* FreeSansBold.sfd: Latin: small adjustment to
	  letter spacing

2011-10-28 11:50 +0000 [r1837]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: several lookup
	  corrections thanks to Emmanuel Vallois, see comments to [bug
	  #34190] Persian characters in FreeSerif not connecting right In
	  bold, lots of re-namings to make lookups copied from roman work.

2011-10-27 22:01 +0000 [r1836]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeMono.sfd: Latin (Vietnamese): adjusted
	  some marks. Latin: SansBold attempts to regularize spacing.

2011-10-27 16:34 +0000 [r1834-1835]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin: kerning, a bit of
	  letter spacing

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Latin: much messing
	  with accents for Vietnamese, some kerning

2011-10-26 22:42 +0000 [r1833]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin: efforts to limit
	  height of accents: Making shorter glyphs for some accents for
	  capital letters. Some compacting of taller stacked glyphs. Made
	  all lowercase letters' accents fall in the bounds.

2011-10-26 14:44 +0000 [r1832]  Stevan_White:

	* FreeSerifBold.sfd: Combining Diacritics:
	  adjustments for Serbian Cyrillic

2011-10-26 14:36 +0000 [r1830-1831]  Stevan_White:

	* FreeSans.sfd: Latin, General Punctuation:
	  attempts to regularize widths and spaces

	* FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Combinind
	  Diacriticals: corrections to mark placement for Cyrillic Serbian

2011-10-26 12:39 +0000 [r1829]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Latin: Replaced d-, l-, t-caron
	  mark with one distinguishable from punctuation Sans: implemented
	  shorter acute, grave, circumflex, caron for caps

2011-10-26 10:20 +0000 [r1828]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Cyrillic: adjusted
	  mark anchors

2011-10-26 02:16 +0000 [r1827]  Stevan_White:

	* FreeSansBoldOblique.sfd: General Punctuation:
	  got rid of minor build warning

2011-10-26 02:13 +0000 [r1826]  Stevan_White:

	* FreeMono.sfd: Greek: got rid of build warning

2011-10-26 01:56 +0000 [r1825]  Stevan_White:

	* FreeSerif.sfd: Punctuation, Spacing
	  Diacritical Marks: worked on widths

2011-10-25 21:28 +0000 [r1822-1824]  Stevan_White:

	* tools/generate/TrueType,
	  tools/generate/OpenType,
	  tools/generate/buildutils.py,
	  tools/generate/MacTT: Again trying to set keywords

	* tools/generate/TrueType,
	  tools/generate/OpenType,
	  tools/generate/buildutils.py,
	  tools/generate/MacTT: Added Date svn keyword

	* tools/generate/TrueType,
	  tools/generate/OpenType,
	  tools/generate/buildutils.py,
	  tools/generate/MacTT: Added Revision keyword

2011-10-25 21:13 +0000 [r1821]  Stevan_White:

	* tools/generate/TrueType,
	  tools/generate/OpenType,
	  tools/generate/buildutils.py (added),
	  tools/generate/MacTT: Implemented cleaned-up
	  Revision string

2011-10-25 20:10 +0000 [r1820]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Cyrillic: added marks for
	  Serbian; adjusted others

2011-10-25 19:19 +0000 [r1819]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Cyrillic: added marks
	  for Serbian to roman, added mark positioning to non-roman

2011-10-25 19:02 +0000 [r1818]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd: Adjusted the font family
	  class. some kerning adjusted some marks.

2011-10-25 18:42 +0000 [r1817]  Stevan_White:

	* FreeSerifItalic.sfd: Spacing
	  Modifiers/Diacritics: regularized spacing some

2011-10-25 14:42 +0000 [r1816]  Stevan_White:

	* FreeSerifItalic.sfd: Latin: shifted grave and
	  acute accents so they could be used independently. Lots more
	  kerning, More adjustment of extreme characters.

2011-10-24 20:16 +0000 [r1815]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd: Cyrillic: for Serbian
	  'salt' form for italic 'sha' with line below

2011-10-24 15:02 +0000 [r1814]  Stevan_White:

	* FreeSerifItalic.sfd: Cyrillic: removed lookup
	  for supposed Serbian gje, leaving only the Macedonian form.

2011-10-23 12:06 +0000 [r1812-1813]  Stevan_White:

	* FreeSerif.sfd: Latin, IPA: kerning

	* FreeSerifItalic.sfd: Latin, IPA: horizontal
	  positioning, kerning Greek, extended: re-positioning of all marks

2011-10-22 18:24 +0000 [r1811]  Stevan_White:

	* FreeSerifItalic.sfd: Cyrillic: re-kerned a bit

2011-10-21 22:53 +0000 [r1810]  Stevan_White:

	* FreeSerifItalic.sfd: Greek, Cyrillic: working
	  on fallout from re-spacing of Latin

2011-10-21 20:18 +0000 [r1809]  Stevan_White:

	* FreeSerifItalic.sfd: Latin: kerning

2011-10-20 17:13 +0000 [r1808]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd: Greek, Cyrillic: made Phi,
	  Ef more distinct, made some overwide glyphs narrower

2011-10-20 16:44 +0000 [r1806-1807]  Stevan_White:

	* FreeSerifItalic.sfd: Latin: starting to mess
	  with spacing of extreme letters: f g j y

	* FreeMono.sfd: Greek, Cyrillic: make Phi, Ef
	  more distinct

2011-10-19 17:24 +0000 [r1805]  Stevan_White:

	* FreeSerif.sfd: Diacriticals: corrected
	  acutecmb placement

2011-10-19 14:56 +0000 [r1804]  Stevan_White:

	* FreeSerif.sfd: Latin/IPA: Reduced size of
	  hooks on many letters. Reduced spacing of grave, acute (big
	  overhaul). kerning in IPA for African, Americanist alphabets

2011-10-18 23:35 +0000 [r1802-1803]  Stevan_White:

	* notes/webfont_guidelines.txt: Propset id

	* notes/webfont_guidelines.txt: formatting, propset
	  ID

2011-10-18 23:19 +0000 [r1801]  Stevan_White:

	* notes/README-downloads.txt,
	  notes/webfont_guidelines.txt (added),
	  notes/features.txt: updates added webfont
	  guidelines

2011-10-18 22:22 +0000 [r1800]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin: more fiddling with
	  kerning. Fixed longstanding bug with "equals" (had mysterious
	  mkmk anchors)

2011-10-18 00:03 +0000 [r1799]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin: more fiddling with
	  kerning

2011-10-17 23:36 +0000 [r1798]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin: More with j and f.
	  Got rid of small caps kerning, which didn't make much sense
	  anyway. Tried to space small caps letters a bit better.

2011-10-17 19:13 +0000 [r1797]  Stevan_White:

	* FreeSerif.sfd: Latin: a bit experimental: got
	  rid of overlap of tails on f and j, compensating with kerning.
	  More kerning. Increased width of quotesingle.

2011-10-17 18:10 +0000 [r1796]  Stevan_White:

	* FreeSerif.sfd: Latin kerning: added letters to
	  classes. Have not yet done Latin Extended Additional

2011-10-17 12:40 +0000 [r1795]  Stevan_White:

	* FreeSerifBold.sfd: Latin: kerning re-done

2011-10-15 18:28 +0000 [r1794]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: Latin: more adjustments to
	  spacing, and re-introduced kerning, completely re-done since the
	  disappearance of the tables. Improvement all around.

2011-10-15 15:28 +0000 [r1793]  Stevan_White:

	* FreeSerif.sfd: Latin, Greek, Cyrillic: changes
	  to L and R bearings to most characters.

2011-10-15 12:01 +0000 [r1792]  Stevan_White:

	* FreeSerifItalic.sfd: Latin, Cyrillic: spacing
	  of a couple of letters

2011-10-15 11:24 +0000 [r1791]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd: Latin, Cyrillic:
	  spacing and kerning of a few letters

2011-10-15 09:38 +0000 [r1790]  Stevan_White:

	* FreeSerifItalic.sfd: Cyrillic: some kerning,
	  esp. of Serbian letters

2011-10-15 01:40 +0000 [r1789]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd: Cyrillic: adjusted
	  boundaries of alternate te

2011-10-14 11:59 +0000 [r1788]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerifBold.sfd: Cyrillic: Made
	  Serb/Macedonian 'be' and lookups for it

2011-10-13 11:20 +0000 [r1787]  Stevan_White:

	* FreeSans.sfd: Latin, Greek, Cyrillic: attempt
	  to make capitals with straight sides spaced even Some clean-up of
	  Greek letterforms

2011-10-13 10:18 +0000 [r1786]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: Cyrillic: made special 'be' for
	  Serbian/Macedonian lookup for gje for italic

2011-10-12 22:29 +0000 [r1785]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd: Cyrillic: more tweeks
	  to Serbian Cyrillic glyphs and similar, re-named Cyrillic
	  auxiliary glyphs.

2011-10-12 18:40 +0000 [r1784]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSansOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Cyrillic: Lookups for
	  localized forms for Serbian/Macedonian. Remedies bug #34537:
	  Serbian Localized Glyphs https://savannah.gnu.org/bugs/?34537
	  Most look-ups were for existing glyphs. In italic also made glyph
	  for te, like a rotated m with macron. Re-named cyrillic auxiliary
	  glyphs.

2011-10-12 12:31 +0000 [r1783]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin, Diacriticals:
	  adjustments for Kwak̕wala

2011-10-11 20:48 +0000 [r1782]  Stevan_White:

	* CREDITS: to myself, for Old Italic

2011-10-11 17:55 +0000 [r1781]  Stevan_White:

	* tools/report/OpenType/UnicodeRanges.py: Update for
	  Old Italic

2011-10-11 17:00 +0000 [r1780]  Stevan_White:

	* FreeMono.sfd: Old Italic: I made letters for
	  this alphabet based on bits of existing ones.

2011-10-11 09:55 +0000 [r1779]  Stevan_White:

	* FreeSerif.sfd: IPA: adjustment to kerning,
	  named a few marked letters

2011-10-11 09:45 +0000 [r1778]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd: Phonetic Extensions:
	  additions for Saanich/Musqueam

2011-10-11 09:40 +0000 [r1777]  Stevan_White:

	* FreeMono.sfd: Runic: completely re-did range,
	  to make stem width like rest of font. (ends are still square or
	  angular rather than round...) Consulted many inscriptions, and
	  changed form where seemed appropriate.

2011-10-09 15:26 +0000 [r1776]  Stevan_White:

	* FreeSerif.sfd: Kerning of IPA symbols for
	  Saanich/Musqueam

2011-10-09 14:03 +0000 [r1775]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Latin, IPA
	  Extensions, Diacriticals: toward Sannich/Musqueam

2011-10-09 10:52 +0000 [r1774]  Stevan_White:

	* notes/maintenance.txt: about adding binaries to
	  cvs for web pages

2011-10-08 22:48 +0000 [r1773]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin/IPA: further tweeks
	  to letter forms

2011-10-08 19:36 +0000 [r1772]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin, Phonetic symbols,
	  Diacriticals: tweeks for Musqueam/Saanich

2011-10-08 17:33 +0000 [r1771]  Stevan_White:

	* FreeSans.sfd: couple of validation issues

2011-10-08 16:47 +0000 [r1770]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin, Combining Marks,
	  IPA: lots of changes for Saanich/Musqueam.

2011-10-08 01:28 +0000 [r1769]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: Latin/IPA: tweeks for
	  Saanich/Musqueam

2011-10-07 12:13 +0000 [r1768]  Stevan_White:

	* FreeSerif.sfd: Latin and IPA, Phonetic
	  Extensions: For display of Saanich language, 1) made for
	  combining right comma on small Latin, some IPA letters, and on
	  the c-hacek. 2) made ligatures for d, l, and t combined with
	  U+0313, 3) Adjusted the widths of some of the IPA letters.

2011-09-30 23:31 +0000 [r1767]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: more kerning,
	  re-think letter ae

2011-09-30 19:50 +0000 [r1766]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerifBold.sfd: Basic Latin: emboldened
	  commercial at sign, to remedy bug #23574: Serif Bold: some
	  symbols not bold https://savannah.gnu.org/bugs/index.php?23574

2011-09-30 18:59 +0000 [r1765]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeSerif.sfd: Bengali: made a Ganda mark, to
	  remedy bug #33983: Bengali Ganda mark is missing
	  https://savannah.gnu.org/bugs/index.php?33983

2011-09-30 18:17 +0000 [r1764]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: more work on
	  connecting letters. Re-thought tatween.

2011-09-30 13:00 +0000 [r1763]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: un-linking more
	  auxiliary dependencies. Re-thought 'v' decoration. Hebrew: dagesh
	  was a little small in bold.

2011-09-29 22:39 +0000 [r1762]  Stevan_White:

	* FreeSerifBold.sfd: Arabic: removed more
	  unnecessary auxiliary references

2011-09-29 20:59 +0000 [r1761]  Stevan_White:

	* FreeSerifBold.sfd: Arabic: emboldended the
	  last of the letters (I hope) un-linked some auxiliaries that were
	  represented by unicode

2011-09-29 13:37 +0000 [r1760]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: alterations to
	  kaf/keheh, kerning to improve spacing

2011-09-29 10:43 +0000 [r1759]  Stevan_White:

	* FreeSerifBold.sfd: Arabic: lots of alterations
	  to letter shapes, spacing

2011-09-28 23:03 +0000 [r1758]  Stevan_White:

	* FreeSerifBold.sfd: Arabic: moved a lot of
	  improvements from roman to bold

2011-09-28 14:13 +0000 [r1757]  Stevan_White:

	* FreeSerif.sfd: Arabic: reduced awful space on
	  left side of free-standing letters

2011-09-28 00:10 +0000 [r1756]  Stevan_White:

	* FreeSerif.sfd: Arabic: regularized alef
	  variants, maddah, etc.

2011-09-27 20:47 +0000 [r1755]  Stevan_White:

	* FreeSerif.sfd: Arabic: More ligatures, a
	  couple alternative parentheses, re-worked hah medial

2011-09-27 09:11 +0000 [r1754]  Stevan_White:

	* FreeSerif.sfd: Arabic: added a few ligatures,
	  re-thought jeem medial, touched up some other letters

2011-09-26 15:28 +0000 [r1753]  Stevan_White:

	* FreeSerif.sfd: Arabic: more small stem width
	  adjustments

2011-09-26 14:51 +0000 [r1752]  Stevan_White:

	* FreeSerif.sfd: Arabic: kerning was in wrong
	  direction.

2011-09-26 13:01 +0000 [r1751]  Stevan_White:

	* tools/report/range_report.py,
	  tools/report/OpenType/UnicodeRanges.py: updated
	  copyright

2011-09-26 07:06 +0000 [r1750]  Stevan_White:

	* FreeSerif.sfd: Arabic: trying to regularize
	  stem widths

2011-09-25 22:33 +0000 [r1749]  Stevan_White:

	* FreeSerif.sfd: Arabic: more adjustment,
	  kerning

2011-09-25 15:45 +0000 [r1748]  Stevan_White:

	* FreeSerif.sfd: Arabic: more tweeks to shapes,
	  kerning

2011-09-25 15:16 +0000 [r1747]  Stevan_White:

	* FreeSerif.sfd: Arabic: tweeks to letter
	  shapes, spacing, mark placement

2011-09-25 01:13 +0000 [r1746]  Stevan_White:

	* FreeSerif.sfd: Arabic: shape adjustments to
	  dal and noon

2011-09-24 14:58 +0000 [r1745]  Stevan_White:

	* FreeSerif.sfd: Arabic: further adjustment of
	  letter shapes, dot placement

2011-09-22 21:28 +0000 [r1744]  Stevan_White:

	* CREDITS: Credit to Tim Erickson for Syriac glyphs.
	  Marked many links as being broken with FIXMEs

2011-09-22 20:48 +0000 [r1743]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Thaana: made bold version,
	  adjusted spacing

2011-09-22 20:41 +0000 [r1742]  Stevan_White:

	* FreeSerif.sfd: Thaana: made new character from
	  parts

2011-09-22 20:28 +0000 [r1741]  Stevan_White:

	* tools/report/OpenType/UnicodeRanges.py: correct
	  Runic

2011-09-22 18:54 +0000 [r1740]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Arabic: numerous tweeks to
	  letter shapes. In roman, some more kerning

2011-09-22 18:16 +0000 [r1739]  Stevan_White:

	* FreeSans.sfd: Syriac: filled in some missing
	  Unicode using Tim Erikson's Carlo Ator font (by his written
	  permission!)

2011-09-21 10:13 +0000 [r1738]  Stevan_White:

	* FreeSerif.sfd: Arabic: fixed lam-alef ligature
	  (again) (I think) Made most dots of uniform size adjusted height
	  of final, isolated hah started on a kerning table

2011-09-20 17:52 +0000 [r1737]  Stevan_White:

	* FreeSerif.sfd: Arabic: made a few more
	  low-numbered glypys, to finish off the range.

2011-09-20 12:47 +0000 [r1736]  Stevan_White:

	* tools/report/OpenType/OpenType.py (removed),
	  tools/report/range_report.py,
	  tools/report/ranges (removed),
	  tools/report/OpenType/UnicodeRanges.py (added),
	  tools/report/OpenType (added): Better naming of
	  packages

2011-09-19 23:23 +0000 [r1735]  Stevan_White:

	* FreeSerif.sfd: Arabic: added some low-numbered
	  characters: Subtending marks, Arabic ray, Poetic marks

2011-09-19 17:54 +0000 [r1734]  Stevan_White:

	* FreeSerif.sfd: Arabic: drew some more Koranic
	  annotations. re-thought sizing and placement of some others

2011-09-19 13:55 +0000 [r1733]  Stevan_White:

	* FreeSerif.sfd: Arabic: raised letter noon more
	  attempts to regularize placement of marks

2011-09-18 21:22 +0000 [r1732]  Stevan_White:

	* FreeSerif.sfd: Arabic: many adjustments,
	  mostly of marks

2011-09-18 14:30 +0000 [r1731]  Stevan_White:

	* FreeSerif.sfd: Arabic: adjusted marks, added
	  anchor points for new marks, made a couple of new letters got
	  ligatures working again replaced Allah and Rial ligatures

2011-09-17 18:51 +0000 [r1730]  Stevan_White:

	* FreeSerif.sfd: Arabic: mostly messing with
	  marks: shadda combinations seem to be working.

2011-09-16 19:45 +0000 [r1729]  Stevan_White:

	* FreeSerif.sfd: Arabic: corrected some
	  diacriticals in Presentation Forms-B especially FBBC-FBC1 were
	  messed up, unfortunately so were a lot of references to those

2011-09-16 18:36 +0000 [r1728]  Stevan_White:

	* FreeSerif.sfd: Arabic: small spacing issues,
	  tweeks to shapes of medial letters

2011-09-16 17:56 +0000 [r1727]  Stevan_White:

	* FreeSerif.sfd: Arabic: re-did several
	  combining forms (which I think I drew myself) added some
	  diacriticals to main range and finished Presentation Forms-B

2011-09-16 13:54 +0000 [r1726]  Stevan_White:

	* FreeSerif.sfd: Arabic: more fussing with marks

2011-09-16 13:35 +0000 [r1725]  Stevan_White:

	* FreeSerif.sfd: Arabic: more fiddling with
	  marks

2011-09-16 13:23 +0000 [r1724]  Stevan_White:

	* FreeSerif.sfd: Arabic: much fussing with mark
	  placement. horizontal spacing of waw

2011-09-16 12:07 +0000 [r1723]  Stevan_White:

	* FreeSerif.sfd: Arabic: added mark positioning.
	  will need to re-think some of the marks.

2011-09-16 10:18 +0000 [r1722]  Stevan_White:

	* notes/maintenance.txt: Changed some CVS info to
	  SVN info. Still need to test and describe tagging in SVN.

2011-09-16 09:42 +0000 [r1721]  Stevan_White:

	* README: Replaced list of character sets with
	  character ranges, which makes more sense at this time.

2011-09-15 23:22 +0000 [r1720]  Stevan_White:

	* FreeSerif.sfd: Arabic: big experiment
	  adjusting baselines of many of the deeper characters. from
	  looking at "Arabic Calligraphy, Nash script for beginners". Also
	  shortened reh.

2011-09-15 21:32 +0000 [r1719]  Stevan_White:

	* FreeSerif.sfd: Arabic: a couple more
	  positional forms, a couple of lookups

2011-09-15 19:22 +0000 [r1718]  Stevan_White:

	* FreeMono.sfd,
	  FreeSerif.sfd: Arabic Supplement: populated

2011-09-15 14:12 +0000 [r1717]  Stevan_White:

	* FreeSerif.sfd: Arabic: much concern over
	  proper old Arabic kah U+0643 vs the more modern "Persian" keheh
	  U+06a9. Fiddling with hamzas.

2011-09-15 11:53 +0000 [r1716]  Stevan_White:

	* FreeSerif.sfd: Arabic: added a few obsolete or
	  unexplained characters. much adjustment of combining marks and
	  dots for better consistency.

2011-09-14 23:24 +0000 [r1715]  Stevan_White:

	* FreeSerif.sfd: Arabic: much fiddling with
	  medial forms, a bit with lookups (seems ligatures are failing
	  altogether)

2011-09-14 19:40 +0000 [r1714]  Stevan_White:

	* FreeSerif.sfd: Arabic: added 2 radical signs
	  Seems the digits in Arabic have been all along the Indo-Persian
	  forms. Now there is a separate range for those. So I movde the
	  old letters, and drew new Arabic versions of those that differed.

2011-09-14 17:26 +0000 [r1713]  Stevan_White:

	* tools/report/ranges/OpenType.py: Brought Arabic up
	  to date

2011-09-14 09:37 +0000 [r1712]  Stevan_White:

	* FreeSerif.sfd: Arabic: further clean-up; made
	  swash kaf to connect as initial form

2011-09-14 08:52 +0000 [r1711]  Stevan_White:

	* FreeSerif.sfd: Arabic: further cleanup, added
	  pedagogical symbols (dots, etc)

2011-09-14 01:04 +0000 [r1710]  Stevan_White:

	* FreeSerif.sfd: Arabic: added letters for Malay
	  Big clean-up of glyphs. Still to do one section of presentation
	  forms.

2011-09-13 20:46 +0000 [r1709]  Stevan_White:

	* FreeSerif.sfd: Arabic: made letters for
	  Parkari. Added lookups init, fina, medi for letters with forms
	  that vary in the font.

2011-09-13 19:43 +0000 [r1708]  Stevan_White:

	* FreeSerif.sfd: Arabic: made main Arabic area
	  characters and presentation forms for Uighur, Kazakh, Kirghiz,
	  Sindhi, and Lahnda, as well as the kaf swash

2011-09-13 19:40 +0000 [r1707]  Stevan_White:

	* tools/report/range_report.py: corrected font paths

2011-09-13 18:10 +0000 [r1706]  Stevan_White:

	* FreeSerif.sfd: Arabic: added more presentation
	  forms mostly for Urdu and Sindhi

2011-09-13 13:34 +0000 [r1705]  Stevan_White:

	* FreeSerif.sfd: Arabic: made some presentation
	  forms for Urdu

2011-09-13 11:05 +0000 [r1704]  Stevan_White:

	* tools/utility/hex_range.py: doc

2011-09-13 09:37 +0000 [r1703]  Stevan_White:

	* tools/ranges.py (removed),
	  tools/report/range_report.py (added),
	  tools/report/ranges (added),
	  tools/range_report.py (removed),
	  tools/ranges (removed): rearrangement, got rid of
	  duplicate script

2011-09-13 09:29 +0000 [r1702]  Stevan_White:

	* tools/ranges/OpenType.py: documented, removed
	  copied text

2011-09-13 09:20 +0000 [r1701]  Stevan_White:

	* tools/freefont-ttf.spec (removed),
	  tools/utility/freefont-ttf.spec (added):
	  Rearranged, added a little doc

2011-09-13 09:15 +0000 [r1700]  Stevan_White:

	* tools/OS2UnicodeRange (removed),
	  tools/KerningNumerals.pl (removed),
	  tools/utility/KerningNumerals.pl (added),
	  tools/report/OS2UnicodeRange (added): Rearranged
	  some old utilities. Put in some doc of who wrote what.

2011-09-12 23:37 +0000 [r1699]  Stevan_White:

	* tools/script-menu (added),
	  tools/hex_range.py (removed),
	  tools/report/private_use.py (added),
	  tools/utility/log2changelog.pl (added),
	  tools/script-menu/nameBySlot.py (added),
	  tools/utility/hex_range.py (added),
	  tools/script-menu/unnameBySlot.py (added),
	  tools/utility/makeBraille.py (added),
	  tools/utility (added),
	  tools/nameBySlot.py (removed): Added some scripts,
	  reorganized

2011-09-12 22:12 +0000 [r1698]  Stevan_White:

	* tools/ranges/OpenType.py: Brought a few ranges up
	  to date

2011-09-12 22:06 +0000 [r1697]  Stevan_White:

	* FreeSerif.sfd: Arabic, etc: Added a lot of
	  letters, mostly following existing patterns. Especially a lot of
	  combinations for Urdu, Sindhi, Pashto, Kazakh, Kurdish, Kirghiz,
	  Turkic, Uighur Berber, Maghrib Arabic, Tunisian Arabic, Moroccan
	  Arabic, Not all forms are there yet. Very few lookups were
	  added...this has to be done too.

2011-09-12 15:13 +0000 [r1696]  Stevan_White:

	* FreeSerif.sfd: Arabic: added some Urdu letters
	  (drawn by myself, with apologies)

2011-09-12 13:07 +0000 [r1695]  Stevan_White:

	* FreeSerif.sfd: Arabic: added keheh and
	  presentation forms, added lookup entries for them.

2011-09-12 12:25 +0000 [r1694]  Stevan_White:

	* tools/test/findBackLayers.py (added),
	  sfd/Makefile,
	  tools/test/MES-Conformance (added),
	  tools/test/MES-Conformance/CheckConformance.pl,
	  tools/test/validate.py (added),
	  tools/generate/MacTT (added),
	  tools/findBackLayers.py (removed),
	  tools/test/MES-Conformance/WGL4.lst,
	  tools/report (added),
	  tools/generate/TrueType (added),
	  tools/MES-Conformance (removed),
	  FreeSansOblique.sfd, tools/test
	  (added), tools/ligatureLookups.py (removed),
	  tools/validate.py (removed),
	  FreeMonoOblique.sfd,
	  FreeSerifItalic.sfd,
	  tools/hex_range.py,
	  FreeSerifBoldItalic.sfd,
	  tools/generate (added),
	  tools/test/MES-Conformance/MES-1.txt,
	  tools/report/ligatureLookups.py (added),
	  tools/test/MES-Conformance/MES-2.txt,
	  tools/GenerateTrueType (removed),
	  tools/freefont-ttf.spec,
	  tools/generate/OpenType (added),
	  FreeSansBold.sfd,
	  tools/test/MES-Conformance/MES-3B.txt,
	  FreeMono.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBold.sfd,
	  tools/test/isMonoMono.py (added),
	  tools/checkGlyphNumbers.py (removed),
	  tools/ranges/__init__.py,
	  tools/ranges/OpenType.py,
	  tools/GenerateOpenType (removed),
	  tools/KerningNumerals.pl,
	  tools/test/MES-Conformance/MES-1.lst,
	  FreeSansBoldOblique.sfd,
	  tools/test/MES-Conformance/MES-2.lst,
	  tools/test/MES-Conformance/mes-list-expand.pl,
	  tools/test/MES-Conformance/MES-3B.lst,
	  FreeMonoBoldOblique.sfd,
	  tools/GenerateMacTT (removed),
	  tools/test/checkGlyphNumbers.py (added),
	  tools/isMonoMono.py (removed): Rearranged tools/
	  directory. Made paths in sfd/Makefile agree with changes.

2011-09-12 12:10 +0000 [r1693]  Stevan_White:

	* FreeSerif.sfd: Latin Extended-B: adjusted
	  Hungarian umlauts Dingbats: drew several characters to fill out
	  range for Unicode 6

2011-09-12 11:40 +0000 [r1692]  Stevan_White:

	* FreeSans.sfd: Osmanya: corrected Unicode
	  values Letterlike symbols: added one, made some references

2011-09-02 12:07 +0000 [r1691]  Stevan_White:

	* FreeSans.sfd, FreeMono.sfd,
	  FreeSerif.sfd: Control Pictures: U+2404 and
	  U+2405 glyphs are reversed. This is due to the icons for them in
	  FontForge being also reversed. in response to
	  http://bugs.debian.org/cgi-bin/bugreport.cgi?bug=632746

2011-09-02 01:15 +0000 [r1690]  Stevan_White:

	* FreeSerif.sfd: Arabic: (really Persian) lookup
	  tables added init, medi, fina lookups peh, tcheh, gaf and fina
	  lookup for jeh In response to ubuntu bug report
	  https://bugs.launchpad.net/ubuntu/+source/ttf-freefont/+bug/730770
	  Not sure if this completely resolves the issue.

2011-08-31 18:16 +0000 [r1689]  Stevan_White:

	* FreeSerif.sfd: Letterlike Symbols: added a
	  few, made better use of small caps for others

2011-08-31 17:24 +0000 [r1688]  Stevan_White:

	* tools/ranges/OpenType.py: mostly revised some
	  comments

2011-08-31 17:19 +0000 [r1687]  Stevan_White:

	* FreeSerif.sfd: Miscellaneous Technical:
	  several additions Supplemental Punctuation: several additions

2011-08-29 23:44 +0000 [r1686]  Stevan_White:

	* tools/ranges/OpenType.py: typos

2011-08-27 20:23 +0000 [r1685]  Stevan_White:

	* FreeSans.sfd: Osmanya: resolves bug #34155:
	  FreeSans: Osmanya digits shifted two code positions
	  https://savannah.gnu.org/bugs/index.php?34155

2011-08-27 20:08 +0000 [r1684]  Stevan_White:

	* FreeSans.sfd: Greek: added lunate letters

2011-08-24 21:40 +0000 [r1683]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Mathematical Operators: added a
	  few in sans, named a few in serif

2011-08-24 11:30 +0000 [r1682]  Stevan_White:

	* FreeSerifBold.sfd: Phonetic Extensions: more

2011-08-24 10:11 +0000 [r1681]  Stevan_White:

	* FreeSerifBold.sfd: Public Use: script-size
	  latin Sup-sub: implemented with script-size letters Phonetic
	  Extensions: filled in many of them

2011-08-24 01:09 +0000 [r1680]  daniel_j:

	* FreeSans.sfd: Added U+1400. Fixes bug #34008

2011-08-17 20:05 +0000 [r1679]  Stevan_White:

	* FreeSerifItalic.sfd: named a few characters

2011-08-14 08:55 +0000 [r1677-1678]  Stevan_White:

	* tools/ranges/OpenType.py: some changes to Latin
	  Extended ranges

	* tools/ranges/OpenType.py: added point for Bengali
	  gandha mark

2011-08-13 23:15 +0000 [r1676]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoBoldOblique.sfd: Currency Symbols:
	  added a few more

2011-08-13 19:53 +0000 [r1675]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Currency Symbols:
	  added several

2011-08-13 14:10 +0000 [r1674]  Stevan_White:

	* FreeSans.sfd, FreeMono.sfd,
	  FreeSerif.sfd: Control Pictures: filled out
	  range

2011-08-12 08:59 +0000 [r1673]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Specials: filled out
	  range

2011-08-12 08:21 +0000 [r1672]  Stevan_White:

	* FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoOblique.sfd: Cyrillic, Extensions:
	  added a few archaic letters, messed with accents

2011-08-10 22:13 +0000 [r1671]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Super/Subscripts (mono and
	  serif roman) re-design of super numerals. Phonetic ranges:
	  fiddled a lot with superscript letters. Cyrillic: adjusted some
	  marks especially on antiquated letters.

2011-08-05 16:52 +0000 [r1670]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeSerif.sfd: Phonetic Extensions: some
	  tweeks to small caps letters Subscripts and superscripts: tweeks

2011-08-04 21:27 +0000 [r1669]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: superscripts,
	  subscripts, diagonal fractions: all faces Re-thought and
	  regularized positioning, fixed a few bugs. All the "superior"
	  numerals and a few superscript latin letters got positioned so
	  they go over the em height. Latin ordinals are positioned
	  parallel to em height, and now both a and o are underlined in all
	  faces. Diagonal fractions are likewise regularized.

2011-07-31 13:42 +0000 [r1668]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: General Punctuation: remedy
	  bug #33905: NARROW NO-BREAK SPACE too wide In one face u+202F was
	  zero width, in others much too wide. Also: set both it and
	  thinspace to 1/6 em.

2011-07-23 21:38 +0000 [r1667]  Stevan_White:

	* FreeSerif.sfd: Sinhala: further simplification
	  of ccmp tables

2011-07-23 09:58 +0000 [r1666]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Latin: Better optical
	  centering of j

2011-07-22 20:25 +0000 [r1665]  Stevan_White:

	* FreeSerif.sfd: Indic ranges, mostly Sinhala:
	  attempts to get it working in Windows. Problem: some of the
	  ligature lookups map to things that by themselves could not
	  render properly. e, o and O vowels around consonents, especially.
	  Seems layout engine in Linux is responsible for making it work in
	  the past... Cleaned up some lookups. In other ranges, added
	  script ranges for MS "new layout engine", e.g. dev2, bng2. Note:
	  Malayalam is mlm2, not mly2 as in Fontforge.

2011-07-18 20:45 +0000 [r1664]  Stevan_White:

	* FreeSerif.sfd: Greek: filled out range for
	  Unicode 6 special mark lookups for Greek re-named some characters

2011-07-18 19:02 +0000 [r1663]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSansBold.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Made more to validate
	  Corrected one more wrong unicode in small caps

2011-07-18 18:11 +0000 [r1662]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd: Private Use:
	  corrected small caps unicode SansBoldOblique: made to validate

2011-07-18 17:58 +0000 [r1661]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Cyrillic Extended: finished
	  4 new letters (normal face) Combining Diacritical Marks: lots of
	  small tweeks. Latin: fiddled with a few accent marks Private Use:
	  small caps glyphs corrected unicode Added missing single-digit
	  diagonal fractions, with lookups Added lookup to remove longs
	  from end of words. Still isn't right.

2011-07-16 11:24 +0000 [r1660]  Stevan_White:

	* notes/features.txt (added): Initial document about
	  OpenType features used in FreeFont.

2011-07-16 11:12 +0000 [r1659]  Stevan_White:

	* FreeSerif.sfd: Non-unicode vulgar fractions
	  (2/7, 5/9, etc) with 'frac' table entries

2011-07-16 10:28 +0000 [r1658]  Stevan_White:

	* FreeSans.sfd, FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Added glyph for zeroslash,
	  and lookup 'zero' for it. Mono: added 'frac' lookup.

2011-07-16 09:45 +0000 [r1657]  Stevan_White:

	* FreeSerifItalic.sfd: Correction to ccmp table
	  for iogonek

2011-07-16 08:38 +0000 [r1656]  Stevan_White:

	* notes/maintenance.txt: put a title on it, a little
	  re-wording of new paragraphs

2011-07-16 08:33 +0000 [r1655]  Stevan_White:

	* notes/maintenance.txt: Added section on doing
	  tests.

2011-07-15 12:34 +0000 [r1654]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Serif: Made the diacritical
	  lookups previously reserved for Latin to be generic, removed
	  special marks lookup for Greek/Coptic, use instead generic one.
	  Added a couple of new letters to Latin Extended-D Bold: Fiddling
	  with diactitics and marks. Something has gone very wrong with the
	  lookups... still not clear what.

2011-07-13 09:20 +0000 [r1653]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Combining Diacritical
	  Marks: a bugfix and a few positioning tweeks

2011-07-12 14:24 +0000 [r1652]  Stevan_White:

	* FreeSans.sfd: *** empty log message ***

2011-07-12 14:17 +0000 [r1651]  Stevan_White:

	* FreeSans.sfd: Latin: all letters now have
	  'above' mark.

2011-07-12 14:04 +0000 [r1650]  Stevan_White:

	* FreeSerif.sfd: Combining diacritical marks:
	  fiddled with positioning

2011-07-01 10:56 +0000 [r1648-1649]  Stevan_White:

	* FreeSans.sfd: Hebrew: tweeks to vowel points

	* FreeMono.sfd: Alphabetic Presentation Forms:
	  widened Hebrew wide letters

2011-07-01 07:59 +0000 [r1647]  Stevan_White:

	* FreeSerif.sfd: Hebrew: tweaks to absolute
	  placement of marks

2011-06-30 22:47 +0000 [r1646]  Stevan_White:

	* FreeSerif.sfd: Hebrew: small tweeks to
	  cantillation marks, etc

2011-06-30 11:47 +0000 [r1645]  Stevan_White:

	* FreeSerif.sfd: Hebrew: lower cantillation
	  marks to left of vowels

2011-06-27 00:20 +0000 [r1644]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Hebrew: got rid of spurious
	  tables.

2011-06-27 00:06 +0000 [r1643]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Hebrew: got yidddish CCMP with
	  yod and yodyod working in Serif

2011-06-26 22:32 +0000 [r1642]  Stevan_White:

	* FreeSans.sfd: Hebrew: issue with Yiddish vs
	  std. Hebrew scripts for MARK lookups. Somehow gets very confused
	  by specific ranges, in different ways on different systems.
	  Disabled distinction between scripts.

2011-06-26 18:20 +0000 [r1641]  Stevan_White:

	* FreeSans.sfd: Hebrew: corrected side of above
	  vowel. More vowel marks.

2011-06-26 17:56 +0000 [r1640]  Stevan_White:

	* FreeSerif.sfd: Hebrew: corrected placement of
	  top vowel. fiddled with cantellatin marks.

2011-06-25 17:27 +0000 [r1639]  Stevan_White:

	* FreeSerif.sfd: Hebrew: further fiddling with
	  points

2011-06-25 14:49 +0000 [r1638]  Stevan_White:

	* FreeSerif.sfd: Hebrew: more fiddling with
	  vowel and cantillation points

2011-06-25 11:12 +0000 [r1637]  Stevan_White:

	* FreeSerif.sfd: Hebrew: more vowel and
	  cantillation point marks

2011-06-25 09:01 +0000 [r1636]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Hebrew: disabled distinction
	  between Yiddish and Std. vowel marks

2011-06-24 14:57 +0000 [r1635]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Removed angle
	  brackets from licence text, because it caused some trouble for
	  some buggy software, and the offsetting could be done better with
	  a newline.

2011-06-24 13:17 +0000 [r1634]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd, FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Hebrew: worked on MARK
	  tables in Sans and Serif in response to further mails regarding
	  bug 25887. Split one table up so lookups wre properly executed in
	  both Yiddish and regular Hebrew. Some adjustment of letter
	  spacing and punctuation in other faces.

2011-06-12 07:14 +0000 [r1633]  Stevan_White:

	* INSTALL, README: Fixed a couple of
	  goofs

2011-06-11 08:12 +0000 [r1632]  Stevan_White:

	* INSTALL: General update: Esp. concerning OpenType
	  support using FreeType2, and on Win 7 and Mac OS X.

2011-06-11 08:07 +0000 [r1631]  Stevan_White:

	* README: Removed remarks about how to install
	  (better for INSTALL file) General re-phrasing and tidying.

2011-06-10 09:34 +0000 [r1630]  Stevan_White:

	* INSTALL: Correct name of X Windows system. Also
	  added Microsoft and Apple to their product names for clarity and
	  consistency.

2011-06-10 09:31 +0000 [r1629]  Stevan_White:

	* README: Correct name of X Window system. Added web
	  page and contact info. Removed Primoz' e-mail.

2011-06-08 11:34 +0000 [r1628]  Stevan_White:

	* INSTALL: Removed remark about "cost", slight
	  rewording, pursuant to discussion in bug #33449: INSTALL file not
	  conform to FSF's policy

2011-05-04 16:13 +0000 [r1627]  Stevan_White:

	* tools/ranges/OpenType.py: new letters to Cyrillic
	  Supplement

2011-04-29 10:33 +0000 [r1626]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Latin: a few more small
	  adjustments to accents

2011-04-29 10:13 +0000 [r1625]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Latin: small adjustments to
	  some accents

2011-04-29 09:48 +0000 [r1624]  Stevan_White:

	* notes/maintenance.txt: info about ssh keys on
	  Savannah

2011-02-03 21:13 +0000 [r1623]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Latin ^ Extensions:
	  various fiddling with spacing and accents for greater uniformity.
	  Also fixed a couple of errors.

2011-02-02 21:33 +0000 [r1622]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Remedies bug #32326, about
	  alignment for combining diacritics on dottedcircle Just put main
	  'mark' anchors on the dottedcircle character.

2011-01-30 13:28 +0000 [r1621]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Latin: Much fiddling
	  with accents, also with width of accented i letters

2011-01-29 18:19 +0000 [r1620]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Accents macron,
	  acute, grave: made more consistent

2011-01-29 17:37 +0000 [r1619]  Stevan_White:

	* FreeSerif.sfd: Chess pieces: made all same
	  width, and in a square

2011-01-28 21:05 +0000 [r1618]  Stevan_White:

	* FreeSans.sfd: Made grave, acute, and macron a
	  little more consistent

2011-01-26 20:28 +0000 [r1617]  Stevan_White:

	* FreeSerifBold.sfd: Made a few accents more
	  consistent, re bug #32220

2011-01-25 22:03 +0000 [r1616]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: Latin, Cyrillic, Greek: more
	  toward bug #32220

2011-01-24 22:54 +0000 [r1615]  Stevan_White:

	* FreeSerif.sfd: Latin, Cyrillic: preferred
	  acute, grave and dieresis to the spacing modifier versions,
	  somewhat in response to bug #32220
	  http://savannah.gnu.org/bugs/?32220

2011-01-21 22:12 +0000 [r1614]  Stevan_White:

	* FreeSerifBold.sfd: Sinhala: various tweeking
	  of auxiliary glyphs to avoid self-intersection

2011-01-19 00:21 +0000 [r1613]  Stevan_White:

	* FreeSerifBold.sfd: as prev.

2011-01-19 00:14 +0000 [r1612]  Stevan_White:

	* FreeSerifBold.sfd: Sinhala: trying to make
	  widths like those of normal

2011-01-18 23:26 +0000 [r1610-1611]  Stevan_White:

	* FreeSerifBold.sfd: Geometric Shapes: re-did
	  dotted circle, made some triangles bigger. Singhala: slight
	  modification to widths, to make it more like normal.

	* FreeSerif.sfd: Geometric Shapes: felt some
	  triangles were over-large. Scaled, and fiddled with their
	  references

2011-01-18 08:53 +0000 [r1609]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Copyright: corrected
	  on Karl Berry's advice.

2011-01-18 08:41 +0000 [r1608]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: kerning

2011-01-16 13:24 +0000 [r1606-1607]  Stevan_White:

	* FreeSerif.sfd: Sinhala: fixed one
	  self-intersection

	* FreeSerifBold.sfd: Sinhala: Now ccmp tables
	  are working. Improved many gloppy self-intersections in the main
	  Unicode range, but many remain in the replacement area.

2011-01-16 11:35 +0000 [r1605]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Copyright notice:
	  included 2011

2011-01-15 21:35 +0000 [r1604]  Stevan_White:

	* FreeSerifBold.sfd: Sinhala: mostly emboldened
	  the FreeSerif glphs by 30 en. Many glyphs are pretty gloppy. Also
	  the rak descender isn't connecting properly.

2011-01-15 17:24 +0000 [r1603]  Stevan_White:

	* FreeSerif.sfd: Miscellaneous Mathematical
	  Symbols-A Mathematical Operators: a few more additions

2011-01-09 15:08 +0000 [r1602]  Stevan_White:

	* tools/ranges/OpenType.py: corrected/updated a
	  couple of OpenType ranges

2011-01-09 12:58 +0000 [r1601]  Stevan_White:

	* FreeSerif.sfd: Mathematical Symbols: replaced
	  some outlines with references Miscellaneous Mathematical
	  Symbols-A: added a few

2011-01-08 23:56 +0000 [r1600]  Stevan_White:

	* FreeSerif.sfd: Miscellaneous Mathematical
	  Symbols A & B: more

2011-01-08 14:24 +0000 [r1599]  Stevan_White:

	* FreeSerif.sfd: Miscellaneous Mathematical
	  Symbols-A Miscellaneous Mathematical Symbols-B Supplemental
	  Mathematical Operators: a few more characters

2011-01-08 13:02 +0000 [r1598]  Stevan_White:

	* FreeSerif.sfd: Miscellaneous Math Symbols-A:
	  added a few easy symbols

2011-01-08 00:21 +0000 [r1597]  Stevan_White:

	* FreeSerif.sfd: Supplemental Mathematical
	  Operators: Miscellaneous Mathematical Operators-B: a few more
	  symbols

2011-01-06 22:03 +0000 [r1596]  Stevan_White:

	* FreeSerif.sfd: Miscellaneous Mathematical
	  Symbols-B: added some easy ones

2011-01-03 20:50 +0000 [r1595]  Stevan_White:

	* FreeSerifBold.sfd: Greek Extended: U+1FC3
	  should not have had a tilde resolves bug #32041: Error in
	  FreeSerifBold.ttf character U+1FC3 (8131)

2010-10-31 23:35 +0000 [r1594]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Added c2sc tables, and smcp
	  tables for Turkish (dotted vs dotted i distinction)

2010-10-29 06:57 +0000 [r1593]  Stevan_White:

	* FreeSerif.sfd: Currency: Indian Rupee courtesy
	  of Daniel Johnson Named some of the other characters.

2010-10-19 23:01 +0000 [r1592]  Stevan_White:

	* FreeSansBoldOblique.sfd: Latin: a bit
	  o'kerning

2010-10-19 19:25 +0000 [r1591]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Filled in IBM
	  Classifications

2010-10-19 07:27 +0000 [r1590]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSerif.sfd: roman: Mahjong made dot rounder
	  sans: corrected missing extrema on ssharp

2010-10-18 19:02 +0000 [r1589]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Combining Diacritical
	  Marks: completed range

2010-10-17 10:16 +0000 [r1588]  Stevan_White:

	* FreeMono.sfd: Combining Diacritical Marks:
	  completed range

2010-10-12 21:50 +0000 [r1587]  Stevan_White:

	* FreeMono.sfd: Miscellaneous Symbols: drew a
	  few more.

2010-10-12 18:27 +0000 [r1586]  Stevan_White:

	* FreeMono.sfd: Miscellaneous Symbols: Corrected
	  comet, drew a snowman. Solves bug #31315
	  (https://savannah.gnu.org/bugs/?31315)

2010-10-11 22:01 +0000 [r1585]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Malayalam: mass rename of
	  auxiliary glyphs in Public Use Area

2010-10-11 21:14 +0000 [r1584]  Stevan_White:

	* FreeSerif.sfd: Adjusted spacing of some
	  conjunct letter parts.

2010-10-10 23:22 +0000 [r1583]  Stevan_White:

	* FreeSerif.sfd: Malayalam: Corrected a couple
	  of glyph names.

2010-10-10 08:58 +0000 [r1582]  Stevan_White:

	* FreeSans.sfd: Incorporated the Meera_04 font,
	  of the GPL project Swathanthra Malayalam Computing
	  http://savannah.nongnu.org/projects/smc/ Copyright (c) 2007,
	  Hussain K H, Suresh P, Swathanthra Malayalam Computing Drew
	  several additions for the modern Unicode standard. Cleaned up
	  many glyphs, and re-named those in the Unicode range.
	  Re-connected the lookups for Chillu letters to point to those in
	  Unicode, and deleted the counterparts in the auxiliary range.

2010-10-10 08:48 +0000 [r1581]  Stevan_White:

	* FreeSerif.sfd: Malayalam: Re-named some glyphs
	  so the commonly used ones were shorter. pba_mal renamed to ba_mal
	  chandrakkala renamed to candrakala Cleared NameMe_195551 as it
	  did nothing Raised ascenders on both i_sign_mal and ii_sign_mal,
	  and made the latter go over preceding letter, based on other
	  fonts and samples.

2010-10-07 06:21 +0000 [r1580]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Armenian: Changed U+FB13-7
	  to 'dlig' lookup, based on a native speaker telling me this is
	  not normally seen in printed form, and on the last lines of sect
	  7.7 of the Unicode standard. (sans face too will follow)

2010-10-06 20:34 +0000 [r1579]  Stevan_White:

	* FreeSerif.sfd: Bengali: cleaned up a few
	  conjunct glyphs

2010-10-06 18:59 +0000 [r1578]  Stevan_White:

	* FreeSerif.sfd: Phonetic Extensions Supplement:
	  Corrected AD9B, raised all superscripts to capital height.
	  (Thanks to Emmanuel Vallois for noticing!)

2010-10-06 18:43 +0000 [r1577]  Stevan_White:

	* FreeSerif.sfd: Cyrillic Extended-B: Corrected
	  Payerok (pajerok) A67D. (Thanks to Emmanuel Valois for pointing
	  it out!)

2010-10-06 18:28 +0000 [r1576]  Stevan_White:

	* FreeSerif.sfd: Tamil: Corrected position of
	  pulli over some letters. (Thanks to Emmanuel Valois for pointing
	  it out!)

2010-10-05 21:58 +0000 [r1575]  Stevan_White:

	* FreeSerif.sfd: Arabic: added some Pashto
	  letters. Need init, final, medial forms.

2010-10-05 19:40 +0000 [r1574]  Stevan_White:

	* FreeSerif.sfd: Buginese: On advice of Emmanuel
	  Vallois, corrected iya ligature per
	  http://www.unicode.org/versions/Unicode5.2.0/ch11.pd

2010-10-05 07:34 +0000 [r1573]  Stevan_White:

	* FreeSerif.sfd: Buginese: added iya ligature
	  based on WAZU test page
	  http://www.wazu.jp/gallery/Test_Buginese.html

2010-10-03 16:31 +0000 [r1572]  Stevan_White:

	* FreeSerif.sfd: old-style numerals and lookup

2010-10-03 15:47 +0000 [r1571]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: combining diacritical
	  marks: More corrections, making dots on i and j to be replaced by
	  mark.

2010-10-03 10:19 +0000 [r1570]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: combining diacritical
	  marks: in serif, made more marks to remove dot on i, j. Filled
	  out range in nono-roman faces.

2010-10-02 22:05 +0000 [r1569]  Stevan_White:

	* FreeSerif.sfd: small-caps: tweeks

2010-10-02 21:39 +0000 [r1568]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: ssharp: made thinner,
	  deeper, and renamed

2010-10-02 20:31 +0000 [r1567]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: small-caps: kerning

2010-09-30 21:37 +0000 [r1566]  Stevan_White:

	* FreeSerif.sfd: Tamil: rounded to int on the
	  Private Use ligatures

2010-09-30 18:55 +0000 [r1565]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: Latin: a touch of kerning

2010-09-29 22:16 +0000 [r1564]  Stevan_White:

	* FreeMono.sfd: Braille Patterns: made the dots
	  bigger

2010-09-29 21:38 +0000 [r1563]  Stevan_White:

	* FreeMono.sfd: Braille Patterns: Replaced to
	  conform better to the Unicode Standard, sect. 15.10 and the
	  Unicode glyph samples: like it or not, the dots are meant to be
	  hollow and filled, representing 'off' and 'on'. All now reference
	  auxiliary characters, for consistency and flexibility.

2010-09-28 23:16 +0000 [r1562]  Stevan_White:

	* FreeSansBold.sfd: small caps

2010-09-28 08:21 +0000 [r1561]  Stevan_White:

	* FreeSerifBold.sfd: small caps: Touch-ups

2010-09-27 20:02 +0000 [r1560]  Stevan_White:

	* FreeSerifBold.sfd: small caps

2010-09-26 16:50 +0000 [r1559]  Stevan_White:

	* FreeSerifBold.sfd: Added PS Private OtherBlues

2010-09-26 10:55 +0000 [r1558]  Stevan_White:

	* FreeSerifItalic.sfd: Latin: tweeked a couple
	  of stacked accents

2010-09-26 09:52 +0000 [r1557]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin: More revision of
	  spacing and letter forms. Still lots to do.

2010-09-25 23:13 +0000 [r1556]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin: kerning for African
	  characters

2010-09-25 22:54 +0000 [r1555]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd: Latin: spacing and glyph form
	  tweeks in Extended-C and IPA for African scripts. Re-worked
	  upsilonlatin in serif roman.

2010-09-25 18:36 +0000 [r1554]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Latin Extended-B:
	  re-worked Oi, oi, and yhook; kerning and other tweeks for African
	  scripts.

2010-09-25 14:04 +0000 [r1553]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: lookups for Latin as
	  in serif, to deal with accented i and the dot.

2010-09-25 13:39 +0000 [r1552]  Stevan_White:

	* FreeSerifItalic.sfd: Latin: tweeked kerning
	  and spacing, especially of apostrophe and quotes.

2010-09-25 12:22 +0000 [r1551]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: IPA Extensions: Completed
	  range in bold italic. bold: characters with low right hook, made
	  not so extreme yhook: made narrower by bending right arm in a
	  bit. replacement lookups for i with accents and i-ogonek with
	  accents, so that accents replace the dot rather than floating
	  over it.

2010-09-23 22:17 +0000 [r1550]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin: more fiddling with
	  letter forms and spacing

2010-09-23 20:24 +0000 [r1549]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: blues values: made descender
	  value a bit wider (also did this in previous commit to bold
	  italic. Latin: more adjustments and corrections of hastily drawn
	  glyphs.

2010-09-23 19:27 +0000 [r1548]  Stevan_White:

	* FreeSerif.sfd: Latin: Addressed several issues
	  pointed out by Daniel Johnson concerning African and Navaho
	  samples.

2010-09-23 08:25 +0000 [r1547]  Stevan_White:

	* FreeSerifBoldItalic.sfd: Latin: Re-worked
	  various glyphs for consistency (mostly they appeared to have been
	  skewed versions from bold--or worse). Still more to be done here.

2010-09-22 22:20 +0000 [r1546]  Stevan_White:

	* FreeSerifItalic.sfd: Latin: some marks for
	  African languages

2010-09-22 22:04 +0000 [r1545]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin: various measures to
	  improve the look of African-language samples. Kerned several
	  letters in IPA Extensions range. Adjusted spacing of several
	  letters. Re-worked Eng a bit, as well as gammalatin. Put some new
	  letters in bold italic.

2010-09-22 08:42 +0000 [r1544]  Stevan_White:

	* FreeSans.sfd: Latin Extended Additional: added
	  four letters small caps: made basic set and smcp lookup table

2010-09-22 06:09 +0000 [r1543]  Stevan_White:

	* Makefile: Was putting ttf files into the otf
	  tarball. One character fixes it.

2010-09-21 19:34 +0000 [r1541-1542]  Stevan_White:

	* FreeSerif.sfd: Latin Extended-C: three new
	  letters; two are additions for Shona

	* tools/ranges/OpenType.py: three new letters in
	  Latin Extended-C range

2010-09-21 19:16 +0000 [r1540]  Stevan_White:

	* FreeSerif.sfd: small caps: forms for
	  Vietnamese lookup tables for accessing alternate forms in Greek
	  and in Latin

2010-09-21 08:29 +0000 [r1539]  Stevan_White:

	* FreeSerif.sfd: small caps: tweeked spacing

2010-09-20 22:45 +0000 [r1538]  Stevan_White:

	* FreeSerif.sfd: Small caps replacements for
	  Latin, with the smcp table to do it. (Haven't seen this actually
	  function yet... )

2010-09-19 13:22 +0000 [r1536]  Stevan_White:

	* FreeSerifBold.sfd: Fixed validation problem.

2010-09-19 13:15 +0000 [r1535]  Stevan_White:

	* ChangeLog: today's commits

2010-09-19 12:40 +0000 [r1534]  Stevan_White:

	* FreeSerif.sfd: Sinhala: Fixed one lookup table
	  mistake, and a couple of glpyh omissions, that Harshula pointed
	  out.

2010-09-19 11:48 +0000 [r1533]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Sans: Applied Daniel
	  Johnsons improvement to capital Eng to Sans, as described in
	  previous commits.

2010-09-19 10:32 +0000 [r1532]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Coptic: added alphabet to
	  bold. Accent marks not working -- a mystery. Some tweeks in
	  medium face as well. glyph cleanup -- points too close, some
	  glyphs outside bounding boxes.

2010-09-18 08:50 +0000 [r1531]  Stevan_White:

	* tools/ligatureLookups.py: mostly code style
	  improvements

2010-09-18 07:54 +0000 [r1529-1530]  Stevan_White:

	* tools/ligatureLookups.py: mostly code tidying

	* ChangeLog: added latest changes, and also changes
	  to the tools/ directory since 2008

2010-09-17 18:38 +0000 [r1528]  Stevan_White:

	* FreeSerif.sfd: Sinhala: tightened up left side
	  of na and derivatives.

2010-09-17 08:27 +0000 [r1527]  Stevan_White:

	* FreeSerif.sfd: Sinhala: tighened up left sides
	  of u, lla, na, dha, ndha, nya, jnya

2010-09-17 08:09 +0000 [r1526]  Stevan_White:

	* FreeSerif.sfd: Sinhala: tightened up some of
	  the vowel conjuncts, and left side of nna.

2010-09-16 20:05 +0000 [r1525]  Stevan_White:

	* FreeSerif.sfd: Coptic: fiddled about with
	  letter forms, striving for a kind of consistency. Sinhala:
	  Harshula noticed four incorrect combined forms. Fixed these.

2010-09-16 08:09 +0000 [r1524]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Latin Extended-A:
	  Applied Daniel Johnson's patch that made two versions of capital
	  Eng. His explanation: ' http://en.wikipedia.org/wiki/%C5%8A The
	  form of capital Eng currently in all three FreeFont faces is that
	  used by the Sami people, resembling a capital N with a hooked
	  right descender. However, the form preferred for African and
	  Amerindian languages resembles a tall lowercase n with a hook
	  that almost meets the left vertical. (See wikipedia link above
	  for a visual.) Recommend moving the existing Sami-style Eng to a
	  PUA or non-Unicode slot, creating African-style Eng at U+014A,
	  and adding a LOCL table with substitution rules for Inari Sami
	  (ISM), Lule Sami (LSM), Northern Sami (NSM) and Skolt Sami (SKS).
	  ' Latin Extended-B: Also in his patch: added U+0220 (Latin
	  Capital Letter N with long leg)

2010-09-15 17:02 +0000 [r1523]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin for Sami: Daniel
	  Johnson's patch added alternative form of Eng for African
	  languages and a 'local' table for the Sami form.

2010-09-15 16:23 +0000 [r1522]  Stevan_White:

	* notes/maintenance.txt: Updated upload and release
	  procedures.

2010-09-15 08:05 +0000 [r1521]  Stevan_White:

	* FreeSerif.sfd: Sinhala: regularized kundaliya
	  a bit.

2010-09-15 07:24 +0000 [r1520]  Stevan_White:

	* FreeSans.sfd: Put stray lookup into its proper
	  subtable.

2010-09-14 23:24 +0000 [r1519]  Stevan_White:

	* FreeSerif.sfd: Sinhala: re-named a few more
	  ligature glyphs, for consistency

2010-09-14 22:50 +0000 [r1518]  Stevan_White:

	* FreeSans.sfd: Gurmukhi: improved some names of
	  ligature glyphs, and found one apparently doubled table entry
	  (kagurmukhi_nuktagurmukhi.nukt now gur_ka.nukt)

2010-09-14 22:17 +0000 [r1517]  Stevan_White:

	* tools/GenerateTrueType: prev. commit had one space
	  too many

2010-09-14 22:08 +0000 [r1516]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd,
	  FreeSerif.sfd: Various measures to fix
	  problems FontForge Find Problems reports under ATT, including a
	  mass glyph rename of Sinhala ligatures.

2010-09-14 13:02 +0000 [r1514-1515]  Stevan_White:

	* tools/ranges.py,
	  tools/hex_range.py,
	  tools/findBackLayers.py,
	  tools/ranges/OpenType.py,
	  tools/GenerateTrueType,
	  tools/GenerateOpenType,
	  tools/range_report.py,
	  tools/ligatureLookups.py,
	  tools/validate.py,
	  tools/GenerateMacTT,
	  tools/checkGlyphNumbers.py,
	  tools/isMonoMono.py,
	  tools/nameBySlot.py: Added license and other tags
	  to python files.

	* Makefile: target for tarball for OpenType

2010-09-13 19:21 +0000 [r1513]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeMonoOblique.sfd: Cyrillic: A few more old
	  letters.

2010-09-13 08:25 +0000 [r1512]  Stevan_White:

	* tools/ranges/OpenType.py: updated some Web links.

2010-09-12 22:57 +0000 [r1511]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Supplemental
	  Punctuation: One character for consistency with other faces.

2010-09-12 21:18 +0000 [r1510]  Stevan_White:

	* FreeSans.sfd, FreeMono.sfd:
	  Control Pictures: Added because it was easy.

2010-09-12 20:49 +0000 [r1509]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Alphabetic
	  Presentation Forms: filled out range in sans faces.

2010-09-12 19:55 +0000 [r1508]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeMonoBoldOblique.sfd: Cleared hints.

2010-09-12 19:43 +0000 [r1507]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Dozens of measures to
	  pass general validation tests, including . cleanup of many glyphs
	  for extrema and self-intersection problmes . re-naming glyphs
	  whose PS names are too long. . removing blank glyphs . giving
	  Unicode points to glyphs missing them. There are still a few
	  lookup table issues in FreeSerif and FreeSans though.

2010-09-12 18:42 +0000 [r1505-1506]  Stevan_White:

	* tools/nameBySlot.py: Corrected output for high
	  Unicode.

	* tools/checkGlyphNumbers.py: Made aware of special
	  TrueType slots.

2010-09-12 16:50 +0000 [r1504]  Stevan_White:

	* FreeSerif.sfd: Re-named some of the Sinhala
	  lookups in somewhat better way. Control Pictures: added a bunch
	  (but... is it worth it?)

2010-09-12 15:01 +0000 [r1503]  Stevan_White:

	* tools/range_report.py: Added a Private Use total
	  and better explanation of totals generally. Some code clean-up.

2010-09-11 13:24 +0000 [r1500-1502]  Stevan_White:

	* ChangeLog: The sfd changes since the last release

	* AUTHORS, CREDITS: Added some recent
	  contributors

	* tools/ranges/OpenType.py: made not to complain
	  about extra TrueType characters.

2010-09-11 09:52 +0000 [r1499]  Stevan_White:

	* FreeSerif.sfd: Coptic: Widened bowl of Khei.

2010-09-11 09:48 +0000 [r1498]  Stevan_White:

	* FreeSerif.sfd: Coptic: deepened descenders of
	  small letters to same depth as in Latin Expanded bowl of Fei and
	  fei to resemble other samples.

2010-09-10 21:38 +0000 [r1497]  Stevan_White:

	* FreeSerif.sfd: Cyrillic: mostly positioning of
	  marks for old Cyrillic alphabets

2010-09-10 08:05 +0000 [r1496]  Stevan_White:

	* FreeSerif.sfd: Coptic: Replaced acoptic with
	  something more dramatic, fiddled with kerning, tidied some other
	  letters

2010-09-08 22:50 +0000 [r1495]  Stevan_White:

	* AUTHORS, CREDITS: mention of recent
	  additions

2010-09-08 22:18 +0000 [r1494]  Stevan_White:

	* FreeSerifBold.sfd: Block Elements: replaced
	  long-missing shade blocks

2010-09-08 22:12 +0000 [r1493]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Block Elements: replaced
	  long-missing shade blocks

2010-09-08 08:18 +0000 [r1492]  Stevan_White:

	* tools/range_report.py: More explanation in output.

2010-09-08 07:41 +0000 [r1491]  Stevan_White:

	* tools/ranges/OpenType.py,
	  tools/range_report.py: another consistency check
	  added UCAS Extended range

2010-09-07 18:30 +0000 [r1490]  Stevan_White:

	* FreeSerif.sfd: Buginese: lookup tables and
	  glyphs to re-order vowels, courtesy of Mohammad Anwari.

2010-09-07 17:39 +0000 [r1489]  Stevan_White:

	* FreeSans.sfd: Osmanya: courtesy of Daniel
	  Johnson. Note this script is slanted in every sample I've seen.
	  That is justification enough that the glyphs in this range look
	  slated, although the face isn't oblique.

2010-09-07 17:19 +0000 [r1488]  Stevan_White:

	* FreeSans.sfd: UCAS Extended: by Daniel Johnson

2010-09-07 16:43 +0000 [r1487]  Stevan_White:

	* FreeSerif.sfd: Sinhala: complete replacement
	  of range with glyphs extracted more directly from Yannis
	  Haralambous' sinha TeX font (last version was also derived from
	  this font, but had undergone detrimental processing). Implements
	  "rak" glyphs using three GSUB lookup steps.

2010-09-04 23:52 +0000 [r1486]  Stevan_White:

	* FreeSerif.sfd: Fix for FontForge warning (v
	  20090923) "On Windows many apps will have problems with this
	  font's kerning, because because 58 of its glyph kern pairs cannot
	  be mapped to unicode-BMP kern pairs" This was due to kern tables
	  for the Musical Symbols range, which isn't in the Unicode Basic
	  Multilingual Plane. Removed this lookup. Might consider another
	  way to position these symbols.

2010-08-29 22:59 +0000 [r1485]  Stevan_White:

	* FreeSerif.sfd: Sinhala: patch by Harshula
	  Jayasuriya to correct the writing system direction in some
	  lookups.

2010-08-29 22:52 +0000 [r1484]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Currency Symbols: additions
	  and tweeking

2010-08-24 19:15 +0000 [r1483]  Stevan_White:

	* FreeSerif.sfd: Sinhala: added one ligature
	  glyph, from Harshula Jayasuriya

2010-08-20 20:19 +0000 [r1482]  Stevan_White:

	* FreeSerif.sfd: Resolves class of validation
	  problems in Sinhala: Bad Glyph Name. This is a FontForge bug,
	  that appears when certain types of problems occur in the glyph
	  contours. Probably an arithmetic error causes some corruption.

2010-08-20 18:17 +0000 [r1481]  Stevan_White:

	* FreeSerif.sfd: corrected missing extrema in
	  recent Latin Extended-D range

2010-08-20 17:56 +0000 [r1480]  Stevan_White:

	* FreeSerif.sfd: Sinhala: re-did previous patch
	  using a more recent one. Un-did some other glyph changes that
	  didn't help much anyway.

2010-08-20 11:05 +0000 [r1479]  Stevan_White:

	* FreeMono.sfd: Named letters in several
	  ranges... should write a script for this...

2010-08-20 09:52 +0000 [r1478]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Added full GNU
	  copyright announcement to TT Names. Updated Copyright date.

2010-08-20 09:16 +0000 [r1477]  Stevan_White:

	* FreeSerif.sfd: Sinhala: Applied a patch from
	  Harshula Jayasuriya, improving glyph names. I tried to tidy up
	  some of the glyphs, but it's a big job... Latin Extended B: fixed
	  one case of missing extrema

2010-08-18 09:48 +0000 [r1476]  Stevan_White:

	* FreeSerif.sfd: Latin Extended-D: yet a few
	  more easily made letters

2010-08-18 09:32 +0000 [r1475]  Stevan_White:

	* FreeSerif.sfd: Latin Extended-D: made a few
	  more letters

2010-08-09 06:34 +0000 [r1474]  Stevan_White:

	* FreeSerif.sfd: Supplemental Arrows-A:
	  completed

2010-08-09 05:50 +0000 [r1473]  Stevan_White:

	* FreeSerif.sfd: Arrows: completed

2010-08-08 22:38 +0000 [r1472]  Stevan_White:

	* FreeSerif.sfd: Added more Arrows

2010-08-04 17:43 +0000 [r1471]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoBoldOblique.sfd: Combining Diacritics:
	  some additions in BoldOblique, corrections in other faces.
	  Several improvements to Greek letter forms in BoldOblique

2010-08-03 21:59 +0000 [r1470]  Stevan_White:

	* FreeMonoBold.sfd: Arrows, a few math
	  operators, and one more combining diacritical

2010-08-03 16:03 +0000 [r1469]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd: Added some combining
	  diacriticals to bold, touched up some spacing modifiers. Minor
	  corrections to roman.

2010-08-03 15:06 +0000 [r1468]  Stevan_White:

	* FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Marks, spacing
	  modifier letters: corrected placement of marks, added some
	  spacing modifier letters and combining marks.

2010-08-03 12:53 +0000 [r1467]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Better Vietnamese
	  support, using 'ccmp' and 'mark' lookup tables. MonoBoldOblique:
	  added Extended Latin ranges

2010-08-03 09:12 +0000 [r1466]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Vietnamese: Much adjustment
	  of horn letters, also general adjustemen of marks

2010-08-02 21:49 +0000 [r1465]  Stevan_White:

	* FreeSerif.sfd: Vietnamese: inadvertently broke
	  the main face. This fixes it.

2010-08-02 18:25 +0000 [r1464]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Vietamese: In roman and
	  sans, lookups are basically functional now. Used 'ccmp' Ligature
	  Substitution to form stacked marks (inasmuch as glyphs for the
	  stacked Unicode codepoints are available) Adjusted positioning of
	  some marks. Named many letters and combining marks. roman italic.
	  Re-designed horn of O and U. Other roman faces could also benefit
	  from a horn job.

2010-08-02 09:43 +0000 [r1462-1463]  Stevan_White:

	* tools/ranges/OpenType.py: Correction regarding NKo
	  range.

	* tools/range_report.py: made not to complain about
	  special TrueType slots 0x00, 0x01, 0x0D

2010-08-02 08:38 +0000 [r1461]  Stevan_White:

	* FreeMono.sfd,
	  FreeSerif.sfd: roman: correction to one math
	  operator mono: Mathematical Operators complete (up to some
	  pre-5.2 Unicode version)

2010-08-01 22:01 +0000 [r1460]  Stevan_White:

	* FreeMono.sfd: Mathematical Symbols: range
	  nearly finished

2010-08-01 17:17 +0000 [r1459]  Stevan_White:

	* FreeMono.sfd,
	  FreeSerif.sfd: roman: corrected direction of 2
	  math operators mono: added several math operators

2010-08-01 14:04 +0000 [r1458]  Stevan_White:

	* FreeSerif.sfd: more improvements to marks for
	  Vietnamese

2010-08-01 13:25 +0000 [r1457]  Stevan_White:

	* FreeSerif.sfd: Worked on mark positioning for
	  Vietnamese, Cyrillic

2010-07-30 11:59 +0000 [r1456]  Stevan_White:

	* FreeMono.sfd: Reduced height of two-part
	  Summation symbols https://savannah.gnu.org/bugs/?23064

2010-07-30 10:53 +0000 [r1455]  Stevan_White:

	* FreeSerif.sfd: Added some Miscellaneous
	  Technical Symbols

2010-07-30 09:24 +0000 [r1454]  Stevan_White:

	* FreeMono.sfd: Corrected U+2626 Orthodox Cross
	  https://savannah.gnu.org/bugs/?27579#discussion

2010-07-29 19:29 +0000 [r1453]  Stevan_White:

	* FreeSerif.sfd: Put in some scan lines
	  23BA-23BD, based on 2500, as discussed in
	  https://savannah.gnu.org/bugs/?23064 I still have no idea what
	  the application for these glyphs would be...

2010-07-17 20:07 +0000 [r1452]  Stevan_White:

	* FreeSerif.sfd: added mark anchors for acute,
	  grave in cyrillic (other faces already had them)

2010-07-13 12:30 +0000 [r1451]  Stevan_White:

	* FreeMono.sfd: Made germandbls (ess-zet) a bit
	  wider, moved a bit to left

2010-07-10 20:42 +0000 [r1450]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Resolves bug 30344: "Greek
	  Final Sigma could be shortened"
	  https://savannah.gnu.org/bugs/index.php?30344 Problem only
	  affected roman, but touched up other faces anyway.

2010-05-30 16:20 +0000 [r1449]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin-extended-B: fiddled
	  with some of the horizontal strokes

2010-05-29 16:22 +0000 [r1448]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin: accents on ogonek
	  letters (for Navaho) a little adjustment of apostrophe (Navaho,
	  Hawaiian)

2010-05-29 15:05 +0000 [r1447]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin mark problems * low
	  dots in bold uneven * ring in bold smaller than in roman, and not
	  heavier * apostrophe in italic too far to right (Navaho,
	  Hawaiian)

2010-05-29 09:19 +0000 [r1446]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin: fixed problems with
	  various marks: ogonek, hook, comma below, some kerning, esp. of
	  non-ascii characters

2010-05-29 07:39 +0000 [r1445]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Sans: made dot accents same
	  height as i-dot, kerned y with comma Both: made low comma accent
	  lower, smaller (esp. for Romanian)

2010-05-27 19:13 +0000 [r1444]  Stevan_White:

	* FreeSans.sfd: Latin: make caron of more
	  uniform height

2010-05-27 18:58 +0000 [r1443]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansOblique.sfd: Latin: made ring accent
	  heavier, esp. for Swedish

2010-05-27 18:41 +0000 [r1442]  Stevan_White:

	* FreeSans.sfd: Latin: i was somehow displaced,
	  dotlessi was much too wide for Turkish

2010-05-10 20:17 +0000 [r1441]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Latin: kerning tweeks

2010-05-09 18:54 +0000 [r1440]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: As discussed in
	  freefont-bugs mailing list, Thu, May 6, 2010 "FreeMono affected
	  by OS X 10.6 nested character references bug" reduced reference
	  nesting in multiple quote characters, as a courtesy to Mac OS
	  10.6 users, who suffer from a bug.

2010-05-03 22:52 +0000 [r1439]  Stevan_White:

	* FreeSerif.sfd: Latin: kerning tweeks

2010-05-03 20:51 +0000 [r1438]  Stevan_White:

	* FreeSerifBold.sfd: Latin: spacing of round
	  small letters, kerning tweeks, fiddled with ligatures

2010-04-17 19:38 +0000 [r1437]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Daniel Johnson improved
	  Cherokee Da

2010-04-10 12:03 +0000 [r1436]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMonoBold.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Emboldened underscore
	  and related glyphs. Resolves issue 22999
	  https://savannah.gnu.org/bugs/index.php?22999 Low line (005F) in
	  bold fonts do not have heavier weight

2010-04-09 18:55 +0000 [r1435]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Serif: Added Thai TTF names

2010-04-08 20:44 +0000 [r1434]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Corrected some
	  language names A bit o kerning

2010-04-08 20:20 +0000 [r1433]  Stevan_White:

	* FreeSerifBold.sfd: Fixed problem with name.
	  This should be the same as before the revision except for the
	  fix. The KDE font installer was saying this face was incorrectly
	  named Free Serif Bold Italic, but this wasn't quite correct. One
	  of the other language names said it was named Free Serif Halvfett
	  Kursiv, which conflicted with that language name for the real
	  Free Serif Bold Italic.

2010-04-08 17:26 +0000 [r1432]  Stevan_White:

	* FreeSerifBold.sfd: regression: for some
	  unknown reason, previous release gets built so that my system
	  thinks it is Free Serif Bold Italic. This is a mystery. There is
	  nothing about Italic in the file.

2010-04-06 19:33 +0000 [r1431]  Stevan_White:

	* sfd/Makefile: Made graceful in absence of
	  Fontforge

2010-03-29 20:47 +0000 [r1430]  Stevan_White:

	* FreeSerifBold.sfd: Adjusted some accent marks,
	  named a few characters

2010-03-25 21:05 +0000 [r1429]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: added Ukrainian TTF
	  names

2010-03-24 23:40 +0000 [r1428]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: added bahasa
	  indonesian TTF names

2010-03-21 12:27 +0000 [r1427]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Added several TTF
	  Names. (Need to check with native speakers.)

2010-03-20 11:39 +0000 [r1426]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Fill out TTF Names tables
	  regarding font names

2010-03-19 08:37 +0000 [r1425]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Removed TTF Names for
	  'sample text' and all 'license URL' except English, 1) in
	  response to FontForge complain that large names tables conflict
	  with recent Windows patches 2) because they are redundant and
	  inappropriate (Hope to post the list of sample texts elsewhere,
	  because I like the idea.)

2010-03-02 08:13 +0000 [r1424]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Latin standard
	  ligatures: in non-bold, promoted dlig to liga. Put standard Dutch
	  IJ in all; Adjusted spacing a bit

2010-02-28 13:04 +0000 [r1423]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBold.sfd: Cyrillic: further revision
	  of spacing, in direction of loosening. bold ze was strangely
	  thin; made bolder.

2010-02-27 11:45 +0000 [r1422]  Stevan_White:

	* FreeSerif.sfd: Cyrillic: reviewed spacing of
	  whole range, generally loosened slightly tidied a couple of ugly
	  glyphs

2010-02-09 19:34 +0000 [r1421]  Stevan_White:

	* FreeSerifBoldItalic.sfd: tweeks to kerning in
	  Latin

2010-02-09 18:40 +0000 [r1420]  Stevan_White:

	* FreeSerifBold.sfd: Tweeks to kerning in Latin

2010-01-28 08:20 +0000 [r1419]  Stevan_White:

	* tools/isMonoMono.py: Implement FontForge handling
	  of magic characters .notdef, .null, nonmarkingreturn at 0x0000
	  0x0001 0x000D

2010-01-27 22:48 +0000 [r1418]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Improved magic
	  characters for FreeMono, inserted them for other faces
	  https://savannah.gnu.org/bugs/index.php?28742

2010-01-27 21:24 +0000 [r1417]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Added at 0x0000
	  0x0001 0x000D characters .notdef .null nonmarkingreturn These are
	  NOT UNICODE they are to satisfy an essentially undocumented
	  "feature" of FontForge, ( in source tottf.c, AssignNotdefNull() )
	  And a funny strong suggestion of TrueType fonts, that they begin
	  with three special characters (evidently different from encoding
	  slots) http://www.microsoft.com/typography/otspec/recom.htm
	  FontForge looks for these glyphs in certain places and copies
	  them. Otherwise, apparently, it will find a glyph elsewhere
	  (unclear how) and copy it there, possibly resulting in something
	  quite wrong (such as a non-monospace font). The three are copied
	  to the end of TTF fonts (only one to the end of OTF fonts)

2009-12-30 23:25 +0000 [r1416]  Stevan_White:

	* FreeSans.sfd: Tamil: deletd Unicode range due
	  to absence of necessary ligatures

2009-12-30 23:01 +0000 [r1415]  Stevan_White:

	* FreeSerifBold.sfd: Cyrillic tweek to spacing

2009-12-29 21:01 +0000 [r1414]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd: Gurmukhi: added 2 characters
	  to complete range

2009-12-29 20:40 +0000 [r1413]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Devanagari: filled out sans,
	  some corrections in serif

2009-12-29 01:42 +0000 [r1412]  Stevan_White:

	* FreeSerif.sfd: Devanagari: Drew and
	  constructed glyphs to complete range.

2009-12-28 19:00 +0000 [r1411]  Stevan_White:

	* AUTHORS, CREDITS: added credits for
	  recent additions

2009-12-28 18:36 +0000 [r1410]  Stevan_White:

	* tools/ranges/OpenType.py: Added/corrected some
	  ranges

2009-12-27 16:25 +0000 [r1405-1409]  Stevan_White:

	* tools/MES-Conformance/WGL4.lst (added),
	  tools/MES-Conformance (added),
	  tools/MES-Conformance/MES-1.txt (added),
	  tools/MES-Conformance/MES-2.txt (added),
	  tools/MES-Conformance/CheckConformance.pl (added),
	  tools/MES-Conformance/MES-1.lst (added),
	  tools/MES-Conformance/MES-2.lst (added),
	  tools/MES-Conformance/MES-3B.txt (added),
	  tools/MES-Conformance/mes-list-expand.pl (added),
	  tools/MES-Conformance/MES-3B.lst (added): Moved
	  Primoz' conformance tools (dated 2002) to a more comfortalbe
	  directory.

	* tools/MES-3B.lst (removed),
	  tools/GenerateTrueType,
	  tools/WGL4.lst (removed),
	  tools/MES-1.txt (removed),
	  tools/MES-2.txt (removed),
	  tools/CheckConformance.pl (removed),
	  tools/MES-1.lst (removed),
	  tools/MES-2.lst (removed),
	  tools/MES-3B.txt (removed),
	  tools/mes-list-expand.pl (removed): disabled build
	  of Mac TT

	* tools/ligatureLookups.py: better sanity check

	* tools/range_report.py: more generic title

	* tools/ranges/OpenType.py: correction to Tamil

2009-12-27 13:56 +0000 [r1404]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Resolves bug #23049
	  Replacement character (U+FFFD) not properly styled
	  https://savannah.gnu.org/bugs/index.php?23049

2009-12-27 01:47 +0000 [r1403]  Stevan_White:

	* FreeSerif.sfd: Devanagari: added letters,
	  completing Unicode range

2009-12-27 01:06 +0000 [r1402]  Stevan_White:

	* FreeSerif.sfd: Tamil: added 2 monster glyphs,
	  completing range (0bc1-2 still questionable)

2009-12-26 18:14 +0000 [r1401]  Stevan_White:

	* FreeSerif.sfd: Tamil: a few more symbols. Only
	  a couple of monster signs still missing, and a couple of marks
	  seem just wrong...

2009-12-26 17:03 +0000 [r1400]  Stevan_White:

	* FreeSerif.sfd: Tamil: added digits

2009-12-26 15:40 +0000 [r1399]  Stevan_White:

	* FreeSans.sfd: Tamil: completed digits, added
	  some other chars

2009-12-26 14:12 +0000 [r1398]  Stevan_White:

	* FreeSerif.sfd: Malayalam: completed Unicode
	  range, also moving some Chillu letters from Private Use area to
	  Unicode, and re-establishing ligatures

2009-12-26 00:38 +0000 [r1397]  Stevan_White:

	* FreeSerif.sfd: Indic ranges: named lots of
	  characters Tamil: corrected and added a few letters

2009-12-24 23:56 +0000 [r1396]  Stevan_White:

	* FreeSans.sfd: Indic ranges, esp. Devanagari:
	  added several new Unicode (not yet complete) tidied letters
	  Bengali: tidying, made some refs

2009-12-24 18:29 +0000 [r1395]  Stevan_White:

	* FreeSans.sfd: Indic ranges: named most of the
	  Unicode characters Tried to improve a few bad names for ligatures
	  in Private Use Area Made a few references: not clearly an
	  improvement. This stuff is still a mess...

2009-12-24 15:19 +0000 [r1394]  Stevan_White:

	* FreeSans.sfd: Devanagari: added an obviously
	  missing nukta ligature

2009-12-24 14:36 +0000 [r1393]  Stevan_White:

	* FreeSans.sfd: Gujarati: added some subvocalic
	  letters for Sanskrit. Quality is low.

2009-12-24 13:26 +0000 [r1392]  Stevan_White:

	* FreeSans.sfd: Filled out Geometric Shapes.

2009-12-24 10:23 +0000 [r1391]  Stevan_White:

	* tools/ligatureLookups.py: fixed syntax error

2009-12-23 23:06 +0000 [r1390]  Stevan_White:

	* tools/LigatureList.pl (removed): no longer
	  relevant.

2009-12-23 22:58 +0000 [r1389]  Stevan_White:

	* tools/ligatureLookups.py (added): Replacement for
	  the LigatureList.pl script, which relied on an external utility
	  to produce a list of replacement lookups.

2009-12-22 23:56 +0000 [r1388]  Stevan_White:

	* tools/ranges/OpenType.py: removed links to
	  disappeared web pages

2009-12-22 23:45 +0000 [r1387]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: cosmetic re-name and
	  re-order of lookups

2009-12-22 22:51 +0000 [r1386]  Stevan_White:

	* FreeMonoOblique.sfd: Diagonal Fractions lookup
	  didn't make much sense in mono anyway, much less in just one
	  style.

2009-12-20 13:33 +0000 [r1385]  Stevan_White:

	* FreeSerif.sfd: split Diagonal Fractions lookup
	  into two tables

2009-12-19 23:20 +0000 [r1382-1384]  Stevan_White:

	* tools/range_report.py: corrected script name

	* tools/ranges/OpenType.py: numerous changes, some
	  reflecting changes to Unicode standard

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: General Punctuation:
	  Got rid of deprecated subrange 0x206A-0x206F

2009-12-19 23:13 +0000 [r1381]  Stevan_White:

	* FreeSerif.sfd: re-encoded some characters
	  adjusted some marks corrected two wrongly-placed glyphs in
	  Phonetic Extensions Supplement

2009-12-14 08:22 +0000 [r1380]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Unicode 5.2
	  corrections of Abkhasian letters from Daniel Johnson. Got rid of
	  some old glyph names.

2009-12-06 15:07 +0000 [r1379]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansOblique.sfd: Cherokee: fixed previous
	  commit of a partial, and old range in medium weight

2009-12-06 14:57 +0000 [r1378]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Cherokee, courtesy of
	  Daniel Johnson.

2009-11-20 13:28 +0000 [r1377]  Stevan_White:

	* FreeSans.sfd: New glyphs for Unified Canadian
	  Aboriginal Syllabics as defined in Unicode 5.2 by Daniel Johnson

2009-11-14 22:47 +0000 [r1376]  Stevan_White:

	* FreeSerif.sfd: Resolves bug #27842: FreeSerif
	  U+1F016 (Mahjong Tile Seven of Bamboos) Wrong
	  https://savannah.gnu.org/bugs/?27842 has one extra bamboo in it
	  for a total of eight. The bamboo on the top left needs to be
	  removed

2009-11-14 22:31 +0000 [r1375]  Stevan_White:

	* FreeSerif.sfd: Resolves bug #27579: Orthodox
	  cross has wrong glyph http://savannah.gnu.org/bugs/?27579 U+2626
	  Orthodox Cross should slope downwards from left to right.

2009-11-13 02:33 +0000 [r1374]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBold.sfd: Cyrillic: corrections to
	  Abkhazian letters by Daniel Johnson

2009-11-05 15:33 +0000 [r1373]  Stevan_White:

	* FreeSerif.sfd: Cyrillic: corrections to
	  Abkhazian letters by Daniel Johnson

2009-10-16 21:12 +0000 [r1372]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Cyrillic: tweeks to brev,
	  based on Unicode 5.2 samples

2009-10-08 07:38 +0000 [r1371]  Stevan_White:

	* FreeSerif.sfd: Currency Symbols: tidied

2009-10-07 23:48 +0000 [r1370]  Stevan_White:

	* FreeSerif.sfd: Number Forms: three new vulgar
	  fractions

2009-10-07 23:32 +0000 [r1369]  Stevan_White:

	* FreeSerif.sfd: Currency Symbols: added "Tenga"
	  of Kazakhstan

2009-10-07 23:19 +0000 [r1368]  Stevan_White:

	* FreeSerif.sfd: Latin Extended: corrected a
	  couple of wrong bearings some fiddling with Vienamese letters
	  with hook

2009-09-30 23:31 +0000 [r1367]  Stevan_White:

	* FreeSerif.sfd: added missing accent

2009-09-17 21:20 +0000 [r1366]  Stevan_White:

	* FreeSerif.sfd: Latin kerning: gave qj positive
	  kerning (it must happen sometimes)

2009-09-17 21:09 +0000 [r1365]  Stevan_White:

	* FreeSerif.sfd: Latin kerning: differentiated
	  grave and dieresis vowels in some combos. (also, adieresis hadn't
	  been kerned at all...)

2009-09-17 20:12 +0000 [r1364]  Stevan_White:

	* AUTHORS: Credited Yannis & Wellcome Institute for
	  Sinhala glyphs

2009-09-17 20:07 +0000 [r1363]  Stevan_White:

	* FreeSerif.sfd: made ref of one sinhals letter

2009-09-15 10:30 +0000 [r1362]  Stevan_White:

	* CREDITS: Credit Wellcome Library for Sinhala, and
	  the Sinhala LKLUG font

2009-07-30 14:03 +0000 [r1361]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSerif.sfd: kerning

2009-07-26 10:41 +0000 [r1360]  Stevan_White:

	* FreeSansBold.sfd: kerning

2009-07-26 07:54 +0000 [r1359]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: added some space in
	  diaeresis did some kerning

2009-07-26 07:42 +0000 [r1358]  Stevan_White:

	* FreeSansBold.sfd: revert serious mistake with
	  previous commit: had somehow set splies quadratic. Here, the
	  GaspTable from that commit is preserved, However there were
	  evidently numerous other changes that weren't documented, and
	  these are lost.

2009-07-12 16:21 +0000 [r1357]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Added GASP tables for
	  control of hinting/smoothing (anti-alising) of TrueType. Now the
	  values are better suited to the EM size of the fonts. Further
	  adjustment (based say on stem width) might still be profitable.

2009-07-11 23:32 +0000 [r1356]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: More tweeks on
	  accents, especially Maltese letters.

2009-07-11 19:53 +0000 [r1355]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Maltese Hbar, hbar,
	  Improved, made more consistent.

2009-07-11 17:08 +0000 [r1354]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Latin: various
	  corrections and tweeks for consistency in accented letters.

2009-07-11 08:23 +0000 [r1353]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: more space to accent on
	  lcaron tightened DZ etc ligatures in SerifItalic faces made
	  references etc in Spacing Modifiers and Combining further kerning

2009-07-10 22:21 +0000 [r1352]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: kerning Sans: decided dots
	  and some other marks needed more room.

2009-07-10 19:09 +0000 [r1351]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: IPA. Phonetics: tweeks

2009-07-10 08:02 +0000 [r1349-1350]  Stevan_White:

	* FreeSerif.sfd: Tweeked some math symbols

	* FreeSans.sfd,
	  FreeSansOblique.sfd: Letterlike: additions

2009-07-10 07:21 +0000 [r1348]  Stevan_White:

	* FreeMonoOblique.sfd: IPA

2009-07-09 22:53 +0000 [r1347]  Stevan_White:

	* FreeSerifBoldItalic.sfd: Another IPA symbol

2009-07-09 22:32 +0000 [r1346]  Stevan_White:

	* FreeSerifBoldItalic.sfd: IPA and Spacing
	  Modifier Letters: added a few

2009-07-08 22:38 +0000 [r1345]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin: various horizontal
	  spacing and positioning issues Replaced Tz and tz ligatures with
	  the originals of Danial Johnson. (range is "Mayanist" and despite
	  Unicode samples, current usage uses normal z in glyph) Put
	  stylized Tz tz in a discretionary ligature lookup.

2009-07-08 18:39 +0000 [r1344]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Cyrillic yi_yi
	  Ukrainian ligature Latin various pointwise cleanup, made some
	  references, horizontal adjustments

2009-07-08 00:16 +0000 [r1343]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Cyrillic: Special ligature
	  for Ukrainian yi_yi, also some kerning

2009-07-07 08:09 +0000 [r1342]  Stevan_White:

	* FreeSerif.sfd: Latin: kerning; made accents on
	  i tighter to avoid collisions

2009-07-05 13:08 +0000 [r1341]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd: Made lookup tables
	  for Dutch IJ

2009-07-05 13:01 +0000 [r1340]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd: Re-did Dutch digraph
	  IJ in bold faces as "broken-U", because I thought it was cool and
	  nobody was there to stop me.

2009-07-05 12:35 +0000 [r1339]  Stevan_White:

	* FreeSerif.sfd: Corrected ligatures Tz tz; put
	  them in special lookup Latin Extended Additional: added
	  medevalist letters, completing range

2009-07-05 11:07 +0000 [r1338]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Cyrillic: added yi_yi
	  ligature for Ukrainian in roman. Not sure if it's right. Latin:
	  kerning. Added longs_t ligature, and historic ligature tables
	  IPA: in bolditalic, added a letter or two

2009-07-04 23:22 +0000 [r1337]  Stevan_White:

	* FreeSerif.sfd: Kerning for hbar

2009-07-04 23:10 +0000 [r1336]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Kerning for Western Slavic
	  languages Added a couple of IPA to BI

2009-07-04 13:29 +0000 [r1335]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: combined letters ae,
	  oe, ij had incorrect horizontal spacing

2009-07-04 13:14 +0000 [r1334]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: combined letters ae, oe, ij
	  had wrong horizontal spacing.

2009-07-04 12:27 +0000 [r1333]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Combining Diacritical Marks
	  for Symbols: additions and fiddling. Also related circles,
	  arrows.

2009-07-04 09:22 +0000 [r1332]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Letterlike Symbols:
	  various additions and adjustments Number Forms: Completed range
	  in serif roman. Adjusted spacing of Latin numerals in serif
	  faces. Diacritics for Symbols: various additions Geometric
	  shapes: thought it best for circle to be round even in oblique
	  faces. It's a choice of course. Added to several faces. Made all
	  to pass tests (except sinhals)

2009-07-02 20:37 +0000 [r1331]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Letterlike Symbols: more
	  additions, adjustments Also fiddled with some math symbols in
	  Sans faces

2009-07-02 09:14 +0000 [r1330]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansOblique.sfd: Letterlike Symbols;
	  additions and adjustments Note: Pango is rendering very
	  strangely: many glyphs in the font are being stolen from some
	  other fonts instead: All the script an blackletter, and some
	  others as well. Is there a cache in the system somewhere? Or
	  what?

2009-06-28 19:48 +0000 [r1329]  Stevan_White:

	* FreeSerif.sfd: Modified glyph. From patch by
	  Pavel Skrylev.

2009-06-28 19:11 +0000 [r1328]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Superscritps and
	  Subscripts: made glyphs to complete range in all faces

2009-06-28 17:43 +0000 [r1327]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansOblique.sfd: Added some combining
	  marks for symbols and Blackboard Bold letters to letterlike
	  symbols

2009-06-28 08:59 +0000 [r1326]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerifBold.sfd: Various adjustments
	  regarding superscript and subscript characters in several ranges.

2009-06-27 15:45 +0000 [r1325]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: More fiddling with
	  super/subscripts Tidied points in Serif to make fontforge "wrong
	  direction" complaint in scaled reference go away. Made some other
	  references in basic latin ranges.

2009-06-27 14:54 +0000 [r1324]  Stevan_White:

	* FreeSerif.sfd: Superscripts and subscripts (in
	  various ranges): tried to systematize sizing and vertical
	  placement. Note: most letters are still simply scaled. This is
	  practical maintenance-wise, but isn't typographically optimal.

2009-06-27 09:04 +0000 [r1323]  Stevan_White:

	* FreeSerif.sfd: Cyrillic Extended A: altered
	  mark positions

2009-06-26 23:31 +0000 [r1322]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Phonetic Extensions:
	  various corrections and fiddling Changed Blues values in
	  FreeSerifBoldItalic

2009-06-25 19:27 +0000 [r1321]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd: Combining Diacritical
	  Marks for Symbols: added to bold oblique added placement mark to
	  many

2009-06-25 07:37 +0000 [r1320]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Deleted TrueType
	  tables 'cvt', 'fpgm', 'prep' because of FontForge complaints.

2009-06-25 07:24 +0000 [r1319]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: IPA Extensions:
	  completed in bold and boldoblique, completing range for face. All
	  faces: deleted 'cvt', 'prep', 'fpgm' TrueType tables, because
	  FontForge was complaining about them (legacy hinting that could
	  interfere, possibly lower quality code, etc)

2009-06-24 23:22 +0000 [r1318]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd: IPA Extensions: made
	  several characters

2009-06-24 22:25 +0000 [r1317]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: IPA Extensions: completed
	  range in bold

2009-06-23 21:48 +0000 [r1316]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Spacing Modifiers: added a
	  couple Combining Diacriticals: brought range to par with FreeSans

2009-06-23 21:11 +0000 [r1315]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Same correction as
	  for FreeSerif

2009-06-23 20:58 +0000 [r1314]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Corrected descender of 027f

2009-06-23 20:45 +0000 [r1313]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: IPA and related: made lots
	  of additional glyphs

2009-06-22 08:43 +0000 [r1312]  Stevan_White:

	* FreeSerif.sfd: Latin: further clean-up

2009-06-21 10:53 +0000 [r1311]  Stevan_White:

	* FreeSerif.sfd: General pointwise clean-up of
	  Latin and Latin Extended. No visible changes were intended
	  besides changed policy on horizontal placement of some horned
	  letters

2009-06-21 09:21 +0000 [r1310]  Stevan_White:

	* FreeSerif.sfd: Latin Extended-C: constructed
	  glyphs to complete range. Latin Extended-D: constructed a bunch
	  of glyphs; some related changes elsewhere.

2009-06-20 17:43 +0000 [r1309]  Stevan_White:

	* FreeSerif.sfd: Latin Extended-C: added most of
	  range

2009-06-20 12:51 +0000 [r1308]  Stevan_White:

	* FreeSerif.sfd: Modifier Tone Letters: added
	  glyphs to complete range. This should also complete all the
	  IPA/Phonetic extenseions ranges.

2009-06-20 11:25 +0000 [r1307]  Stevan_White:

	* FreeSerif.sfd: Phonetic Extensions Supplement:
	  added letters completing range Note the superscript ones are
	  simply scaled, so stem width is not quite right...

2009-06-19 08:19 +0000 [r1306]  Stevan_White:

	* FreeSerif.sfd: Phonetic Extensions: made
	  letters to complete range

2009-06-18 06:40 +0000 [r1305]  Stevan_White:

	* FreeSans.sfd: IPA Extensions: clean-up, fix
	  some horizontal spacing issues

2009-06-18 06:16 +0000 [r1304]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Some glyph problems
	  clean-up, made to pass validate (BlueValues)

2009-06-17 21:13 +0000 [r1303]  Stevan_White:

	* FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: IPA Extensions:
	  Improved spacing and horizontal positioning

2009-06-17 08:42 +0000 [r1302]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Georgian: added
	  missing letter

2009-06-17 08:22 +0000 [r1301]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd: IPA: made curls on
	  some glyphs more distinct. note horizontal placement in BO is
	  chaotic--needs work

2009-06-14 13:51 +0000 [r1300]  Stevan_White:

	* FreeSerif.sfd: Musical Symbols: various
	  experiments with lookups. calt: should have allowed isolatef
	  forms of combining marks, (but can't see application support...)
	  aalt, hist: for alterntative characters Added some historical
	  glyphs Math Alphanumeric: some clean-up.

2009-06-11 21:57 +0000 [r1299]  Stevan_White:

	* FreeSerif.sfd: Musical Symbols: various
	  tweeks. Failed attempt to use 'isol' lookup to put isolated
	  combining marks above the baseline.

2009-06-11 09:04 +0000 [r1298]  Stevan_White:

	* FreeSerif.sfd: Musical Symbols: more fiddling
	  with mark placement, strengthened ornamental endings

2009-06-10 22:53 +0000 [r1297]  Stevan_White:

	* FreeSerif.sfd: Musical Symbols: mkmk
	  positioning of augmentation dot

2009-06-10 09:09 +0000 [r1296]  Stevan_White:

	* FreeSerif.sfd: Musical Symbols: combining
	  marks above and below notes now working somewhat

2009-06-08 21:46 +0000 [r1295]  Stevan_White:

	* FreeSerif.sfd: Musical Symbols: more
	  adjustments of size and placement

2009-06-06 23:28 +0000 [r1294]  Stevan_White:

	* FreeSerif.sfd: Musical Symbols: mostly work on
	  lookups. Made tablature symbols thinner.

2009-06-06 19:24 +0000 [r1293]  Stevan_White:

	* FreeSerif.sfd: Musical Symbols: more fiddling
	  with sizes and positions Byzantine Musical Symbols: minor
	  clean-ups

2009-06-06 12:31 +0000 [r1292]  Stevan_White:

	* FreeSerif.sfd: Fixed some ATT problems with
	  'kern' lookups for Cyrillic

2009-06-06 12:25 +0000 [r1291]  Stevan_White:

	* FreeSerif.sfd: Musical Symbols: more changes
	  to size and position

2009-06-05 21:28 +0000 [r1290]  Stevan_White:

	* FreeSerif.sfd: Musical Symbols: many changes
	  to glyph positions and shapes

2009-05-27 22:36 +0000 [r1289]  Stevan_White:

	* FreeSerif.sfd: Cyrillic, additions and
	  re-naming by Pavel Skrylev. * Added Cyrillic-B Ext glyphs, and
	  Cyrillic-A Ext glyphs. * Added cyr_above anchor to all cyrillic
	  glyphs, some glyphs was changed * To all Cyrillic letters I've
	  given proper names based on old Slavic language which is source
	  of all modern slavic languages. (the naming is contentious, and
	  there are a couple of technical problems...under consideration)

2009-05-26 23:09 +0000 [r1288]  Stevan_White:

	* FreeSerif.sfd: Musical Symbols: scaled up
	  accidentals a bit. reduced width of repeated figure marks reduced
	  width of caesura even more tried to implement mark
	  positioning...not working everywhere made references

2009-05-25 23:48 +0000 [r1287]  Stevan_White:

	* FreeSerif.sfd: Made all letterlike glyph parts
	  more like those from Serif and Serif Italic. (Except for Ped.
	  U+1d1ae)

2009-05-20 08:25 +0000 [r1286]  Stevan_White:

	* FreeSerif.sfd: Corrected DPOS lookup for Music
	  range to be in script 'musi'

2009-05-20 07:35 +0000 [r1285]  Stevan_White:

	* FreeSerif.sfd: Western Musical Symbols:
	  fiddling Miscellaneous Symbols: re-design of musical flat symbol

2009-05-19 07:37 +0000 [r1284]  Stevan_White:

	* FreeSerif.sfd: Western Musical Symbols:
	  revised horizontal placement of notes

2009-05-19 01:38 +0000 [r1283]  Stevan_White:

	* FreeSerif.sfd: Western Musical Symbols: raised
	  some more glyphs

2009-05-19 01:27 +0000 [r1282]  Stevan_White:

	* FreeSerif.sfd: Western Musical Symbols:
	  altered many combining symbols to be more like Unicode sample
	  corrected many symbols that should be spacing but had 0 width
	  raised many symbols that were far lower than in Unicode samples

2009-05-18 09:47 +0000 [r1281]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: U+0354 and U+0355
	  (combining left/right arrowhead below) were switched

2009-05-11 21:27 +0000 [r1280]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Hebrew: Moved vowel below mark
	  for esh (05e8) and dalet (05d3) as recommended in bug report
	  #25887 "FreeSans Hebrew most nonspacing marks are spacing"
	  https://savannah.gnu.org/bugs/?25887

2009-05-11 07:25 +0000 [r1279]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Put PS Private
	  BlueScale back to default. (Was really experiment to alleviate
	  excessive overshoot. Didn't work and FontForge complained.)

2009-05-11 07:21 +0000 [r1278]  Stevan_White:

	* FreeSerif.sfd: Geometric Shapes: made two
	  'bullet' shapes to be consistent with 'bullet' Blues values: put
	  Std VH W into Stem Snap values

2009-05-11 07:09 +0000 [r1277]  Stevan_White:

	* FreeSans.sfd: More Geometric Shapes. also
	  worked on character widths.

2009-05-09 19:47 +0000 [r1276]  Stevan_White:

	* FreeSans.sfd: More Geometric Shapes

2009-05-02 12:44 +0000 [r1274-1275]  Stevan_White:

	* FreeSansOblique.sfd: Had misplaced Tifinagh
	  range.

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: The rest of Tifinagh,
	  courtesy of Daniel Johnson. + a few adjustments.

2009-05-01 09:06 +0000 [r1273]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Explicitly disabled
	  PS Private dictionary entry BlueFuzz as recommended in Adobe Type
	  1 Font Format, sect. 5.8.

2009-05-01 07:50 +0000 [r1272]  Stevan_White:

	* FreeSans.sfd: Tifinagh range, courtesy of
	  Daniel Johnson.

2009-05-01 07:47 +0000 [r1271]  Stevan_White:

	* tools/ranges.py: Corrections for Georgian and
	  Tifinagh ranges

2009-05-01 07:17 +0000 [r1270]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Adjusted PS Private
	  values according to understanding of Adobe Type 1 Font Format
	  (Ch. 5, Private Dictionary) Not completely clear on BlueScale,
	  and StemSnap values are still a mystery. (FontForge 'guess' seems
	  to do little or nothing, and I don't know where these values came
	  from) Original hope was to improve appearance of overshoot in
	  Sans, but it seems that my renderers ignore this information.

2009-04-28 07:41 +0000 [r1269]  Stevan_White:

	* FreeSans.sfd: More Geometric Shapes

2009-04-27 23:14 +0000 [r1268]  Stevan_White:

	* FreeSans.sfd: More Geometrical Shapes

2009-04-27 08:11 +0000 [r1267]  Stevan_White:

	* FreeSans.sfd: Added U+25D0-3 (half-black
	  circles) Note the Geometric Shapes range is still in poor shape
	  in Sans.

2009-04-25 11:55 +0000 [r1266]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Serif: fiddled with mark
	  positioning in Cyrillic and Glagolitic Non-roman faces: Added
	  'mark' positioning for Cyrillic

2009-04-14 09:16 +0000 [r1265]  Stevan_White:

	* FreeSerif.sfd: Cyrillic Extended-B more
	  additions. Now has all except payerok punctuation and Old
	  Abkhasian letters.

2009-04-13 18:10 +0000 [r1264]  Stevan_White:

	* FreeSerif.sfd: Cyrillic Extended-B a few more
	  letters

2009-04-13 17:35 +0000 [r1263]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: kerning: tweeks

2009-04-11 15:35 +0000 [r1262]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd, FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Georgian: additions and
	  improvements. Serif now has full compliment. Note uppercase is
	  questionable, being a simple scaling...

2009-04-11 13:20 +0000 [r1261]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Latin Extended-D
	  added u+a788-c

2009-04-11 12:08 +0000 [r1260]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin Extended-D corrected
	  slots for Mayanist letters in roman added modifier letters
	  u+a788-c

2009-04-11 11:42 +0000 [r1259]  Stevan_White:

	* tools/ranges.py: Corrected Vai range

2009-04-11 10:50 +0000 [r1258]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin Extended-D Mayanist
	  letters by Daniel Johnson

2009-04-10 09:00 +0000 [r1257]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: Letterlike Symbols: additions
	  and improvements

2009-04-10 08:38 +0000 [r1256]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Letterlike Symbols:
	  additions, improvements

2009-04-10 07:13 +0000 [r1255]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Reconsidered numeral
	  1. Made angle more like other angles in font.

2009-04-09 06:45 +0000 [r1254]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Increased angle of
	  hook on numeral 'one', to improve distinction from letter 'l' at
	  small point sized. (Noticed that on some systems, even at 12
	  point the distinction had not been obvious.)

2009-03-29 13:56 +0000 [r1253]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: General Punctuation:
	  Adjust position, shape of quotes, fiddle with others.

2009-03-29 12:12 +0000 [r1252]  Stevan_White:

	* FreeSans.sfd: Kerning: Had grouped letters
	  with round bowls on left (e,c,0,d,q) together, but now see the
	  original Nimbus fonts had left bounds that vary by 10 EM or more,
	  which is quite visible. I am correcting a flaw in the original
	  font in a very difficult and complex way. It would be much better
	  to make the bounds of these similar letters to be equal. To do:
	  same for punctuation, copy to other styles of Sans

2009-03-29 03:48 +0000 [r1251]  Stevan_White:

	* FreeSerif.sfd: Kerning: tweeks

2009-03-29 03:34 +0000 [r1250]  Stevan_White:

	* FreeSerif.sfd: Kerning tweek

2009-03-22 18:54 +0000 [r1249]  Stevan_White:

	* FreeMono.sfd: clean-up of some of the basic
	  Nimbus glyphs

2009-03-22 14:26 +0000 [r1248]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Hebrew: more work on lookups
	  regarding vowel marks

2009-03-22 11:03 +0000 [r1247]  Stevan_White:

	* FreeSans.sfd: Hebrew: vowel mark positioning

2009-03-16 19:26 +0000 [r1246]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: re-named some lookup tables
	  (Sinhala, Hebrew)

2009-03-15 11:58 +0000 [r1245]  Stevan_White:

	* FreeSerif.sfd: Georgian: point-wise cleanup
	  note: doubts about spacing and glyph bounds in capitals

2009-03-15 09:58 +0000 [r1244]  Stevan_White:

	* FreeSerif.sfd: Greek Extended: corrected
	  u+1fd3

2009-03-13 08:15 +0000 [r1243]  Stevan_White:

	* FreeSerif.sfd: Cyrillic (and extensions)
	  various tweeks to old Cyrillic letters, spacing.

2009-03-07 12:30 +0000 [r1242]  Stevan_White:

	* FreeMonoBoldOblique.sfd: correct direction
	  braceleft

2009-03-07 12:16 +0000 [r1240-1241]  Stevan_White:

	* FreeSerif.sfd: Glagolitic: tweeks

	* FreeMonoBold.sfd: correct direction of
	  braceleft

2009-03-05 09:03 +0000 [r1239]  Stevan_White:

	* FreeSerif.sfd: Glagolitic: more fiddling.
	  Raised small letters so their tops are even with the tops of the
	  big letters, which is more in line with their "hanging" nature,
	  and with most manuscripts.

2009-03-04 07:03 +0000 [r1238]  Stevan_White:

	* tools/ranges.py: ranges for Sinhala

2009-03-03 09:54 +0000 [r1237]  Stevan_White:

	* FreeSerif.sfd: Glagolitic: more tweeks

2009-03-02 23:03 +0000 [r1236]  Stevan_White:

	* FreeSerif.sfd: Glagolitic: added Djervi (I
	  think)

2009-03-02 21:49 +0000 [r1235]  Stevan_White:

	* tools/GenerateMacTT (added): Special script for
	  generating fonts that don't give errors on the Mac. I hope
	  somebody will fix something, either in Font Book or FontForge,
	  that will make this unnecessary

2009-03-01 23:24 +0000 [r1234]  Stevan_White:

	* FreeSerif.sfd: Glagolitic: tweeks

2009-02-28 19:38 +0000 [r1233]  Stevan_White:

	* FreeSerif.sfd: Sinhala: replaced U+0DD0-1 with
	  ones from sinha font

2009-02-28 12:25 +0000 [r1232]  Stevan_White:

	* FreeSerif.sfd: Glagolitid: Additions,
	  improvements (Note: the characters still missing I simply haven't
	  yet recognized in a manuscript or found in an on-line HTML
	  encoded text. I would like to see real ones rather than copying
	  out of the Unicode samples.) Cyrillic Extended B: Assembled
	  Monograph Uk, uk. See discussion
	  http://en.wikipedia.org/wiki/Uk_(Cyrillic)

2009-02-28 00:14 +0000 [r1231]  Stevan_White:

	* FreeSerif.sfd: More work on Glagolitic

2009-02-27 15:35 +0000 [r1230]  Stevan_White:

	* FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Made braces more
	  distinctive.

2009-02-26 15:12 +0000 [r1229]  Stevan_White:

	* FreeMono.sfd: ASCII: Accentuated braces (for
	  better readability in terminals)

2009-02-24 22:27 +0000 [r1228]  Stevan_White:

	* FreeSerif.sfd: Cherokee: Daniel improved
	  U+13d2

2009-02-24 07:41 +0000 [r1227]  Stevan_White:

	* FreeSerif.sfd: Sinhala: Copied most Unicode
	  glyphs directly from Yannis Haralambous sinhala MetaFont files.
	  These are: all but 0dd0 0dd1 0df4. Gave Unicode characters names,
	  based on a transliteration scheme from Harshula. The auxiliary
	  Sinhala are all from the LKLUG font.

2009-02-22 23:41 +0000 [r1226]  Stevan_White:

	* FreeSerif.sfd: Copied glyphs from LKLUG font,
	  as discussed with Harshula Jayasuriya.
	  http://sinhala.cvs.sourceforge.net/viewvc/sinhala/sinhala/fonts/
	  This font is derived from a Metafont font: This package was
	  originally developed by Yannis Haralambous (Yannis.Haralambous at
	  univ-lille1 fr), with funding from the Wellcome Institute for the
	  History of Medicine, 183 Euston Road, London NW1 2BE, England
	  (Contact person: Dominik Wujastyk (D.Wujastyk at ucl ac uk)),
	  which is using the font in its Catalogue of Sinhala Manuscript.
	  The original package was available by ftp at ftp.bcc.ac.uk in:
	  /pub/users/ucgadkw/indology/software/sinhala1-TeX.zip The hope is
	  to eventually pull glyphs directly from the original MF files.

2009-02-21 19:48 +0000 [r1225]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: tidying previous commits

2009-02-21 14:11 +0000 [r1224]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Serif: Revised letter
	  spacing in Armenian. Serif roman: Added initial version of
	  Glagolitic (by me)

2009-02-18 20:38 +0000 [r1223]  Stevan_White:

	* FreeSerif.sfd: Armenian: spacing changes
	  Sinhala: deleted range, for usual reasons: It doesn't even
	  contain the full set of Unicode letters, and Sinhala requires
	  auxiliary characters and lookup tables for proper representation.
	  (Found what I think are the originals at
	  http://www.mettanet.org/fonts/ )

2009-02-12 20:08 +0000 [r1222]  Stevan_White:

	* tools/ranges.py,
	  tools/findBackLayers.py,
	  tools/GenerateTrueType,
	  tools/GenerateOpenType,
	  tools/range_report.py,
	  tools/validate.py,
	  tools/checkGlyphNumbers.py,
	  tools/isMonoMono.py: Changed
	  /usr/local/bin/fontforge to /usr/bin/fontforge (to work with the
	  update-alternatives mechanism, whereby a link is made
	  /usr/bin/fontforge -> /etc/alternatives/fontforge -> preferred
	  binary )

2009-02-12 19:33 +0000 [r1221]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: SerifBoldItalic - Armenian
	  oblique from Daniel Johnson's bold version Added ligatures (by
	  Daniel) and lookup tables.

2009-02-12 08:39 +0000 [r1220]  Stevan_White:

	* FreeSerifItalic.sfd: Armenian: Made oblique
	  version of Daniel Johnson's range

2009-02-12 08:28 +0000 [r1219]  Stevan_White:

	* FreeSerifBold.sfd: Armenian by Daniel Johnson

2009-02-12 08:00 +0000 [r1218]  Stevan_White:

	* FreeSerif.sfd: Improvements of a couple of
	  Armenian characters, from Daniel Johnson.

2009-02-07 20:28 +0000 [r1217]  Stevan_White:

	* FreeSerifItalic.sfd: Latin: kerning fixed some
	  missing extrema

2009-02-07 19:47 +0000 [r1216]  Stevan_White:

	* FreeSerifItalic.sfd: Latin: kerning made some
	  more delicate italic IPA letters

2009-02-06 20:14 +0000 [r1215]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Latin: kerning

2009-02-06 10:19 +0000 [r1214]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Latin: kerning Adjusted ff
	  etc ligatures in Italic

2009-02-05 23:20 +0000 [r1213]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Latin: kerning

2009-02-05 19:57 +0000 [r1212]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Thai sample text (means
	  something like Mr Sangkapan who sells bottles was picked up by
	  the police because he stole the wash of ms. .....)

2009-02-04 20:19 +0000 [r1211]  Stevan_White:

	* FreeSans.sfd: Vai range, by Daniel Johnson

2009-02-04 20:08 +0000 [r1210]  Stevan_White:

	* FreeSerifBoldItalic.sfd: Latin: kerning

2009-02-03 20:53 +0000 [r1209]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Latin: kerning

2009-02-02 23:44 +0000 [r1208]  Stevan_White:

	* FreeSerifBoldItalic.sfd: Latin: kerning

2009-02-02 23:32 +0000 [r1207]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Latin: kerning

2009-01-26 21:00 +0000 [r1206]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Made nu distinct from
	  v. Remedies bug #25376: https://savannah.gnu.org/bugs/?25376
	  greek letter 'nu' [U+03bd] same as latin 'v' [U+0076] in FreeMono

2009-01-22 21:37 +0000 [r1205]  Stevan_White:

	* FreeSans.sfd: Kerning. Need to copy to other
	  faces

2009-01-22 01:44 +0000 [r1204]  Stevan_White:

	* FreeSerif.sfd: Arabic ligatures: turned on
	  "ignore combining marks" on advice of Khaled Hosny.

2009-01-20 17:09 +0000 [r1203]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Drew U+1e9C-F in Latin
	  Extended Additional (including capital ess-zet)

2009-01-19 20:48 +0000 [r1202]  Stevan_White:

	* FreeMono.sfd: Update ot Kayah Li from Daniel
	  Johnson

2009-01-18 16:49 +0000 [r1201]  Stevan_White:

	* AUTHORS, CREDITS: Daniel Johnson's
	  latest contributions

2009-01-18 14:33 +0000 [r1200]  Stevan_White:

	* FreeSerif.sfd: Coptic: Laula to be same height
	  as Phi Psi (higher than other letters). Dei and dei also to be
	  higher letters, adjusted widths too.

2009-01-18 14:25 +0000 [r1199]  Stevan_White:

	* FreeSans.sfd: Unified Canadian Aboriginal
	  Syllabics: moved the non-Unicode letter part glyphs ucas.ring
	  ucas.horizline ucas.shortvert ucas.righttack ucas.lefttack to the
	  Private Use area 59664-59668 Technique was to edit the SFD file
	  directly: It seems the encoding slot for the glyph is the first
	  field of the "Encoding:" line for the character.

2009-01-18 13:28 +0000 [r1198]  Stevan_White:

	* FreeMono.sfd: Cherokee: improvements from D.
	  Johnsaon

2009-01-18 12:34 +0000 [r1197]  Stevan_White:

	* FreeSerif.sfd: Latin: kerning

2009-01-17 18:58 +0000 [r1196]  Stevan_White:

	* FreeSerif.sfd: Cyrillic: kerning

2009-01-17 15:26 +0000 [r1195]  Stevan_White:

	* FreeSerif.sfd: Re-linked Ezh/Yogh and re-made
	  reversed Ezh

2009-01-16 21:28 +0000 [r1194]  Stevan_White:

	* FreeSerif.sfd: Fiddled with Coptic glyphs:
	  Longer tail on Shei, less droopy Alfa. Made Armenian Aiw to be a
	  link to Phi (on Daniel Johnson's advice )

2009-01-16 08:01 +0000 [r1193]  Stevan_White:

	* tools/ranges.py: corrected Unified Canadian
	  Aboriginal Syllabics

2009-01-16 07:44 +0000 [r1192]  Stevan_White:

	* FreeMono.sfd: Kayah Li by Daniel Johnson

2009-01-16 07:36 +0000 [r1190-1191]  Stevan_White:

	* FreeMono.sfd: removed hints

	* FreeMono.sfd: Cherokee, by Daniel Johnson

2009-01-16 07:27 +0000 [r1189]  Stevan_White:

	* FreeSerif.sfd: Made references of some
	  Armenian punctuation, Made primereversed to be really the reverse
	  of prime.

2009-01-13 21:43 +0000 [r1187-1188]  Stevan_White:

	* FreeSans.sfd: named some Armenian letters

	* FreeSerif.sfd: Armenian range by Daniel
	  Johnson, with ligatures.

2009-01-13 08:43 +0000 [r1186]  Stevan_White:

	* README: GPL 2 -> GPL 3

2009-01-12 20:35 +0000 [r1185]  Stevan_White:

	* FreeSerif.sfd: Coptic: more kerning

2009-01-12 09:06 +0000 [r1184]  Stevan_White:

	* FreeSerif.sfd: Coptic: Raised top of laula
	  above common top, as it appears in most other fonts.Makes it
	  easier to distinguish from alfa. A little more space around Iauda
	  and iauda. Kerned Dei.

2009-01-11 22:30 +0000 [r1183]  Stevan_White:

	* FreeSerif.sfd: Coptic: Made capital Alpha is
	  distinct in printed form. Made more angular vida with flattened
	  top and bottom. Made small hori more curvy.

2009-01-11 14:06 +0000 [r1182]  Stevan_White:

	* FreeSerif.sfd: Greek: made archaic letters
	  u+0370-3 (understand are used for old numbering) Coptic: more
	  fiddling with Hori, made Kapa much wider reduced width of small
	  letter straight stems to match small letters in Latin kerning for
	  capitals

2009-01-11 00:39 +0000 [r1181]  Stevan_White:

	* FreeSerif.sfd: Revised Coptic. Added a few
	  dialectic letters and kai symbol Removed many "ear" serifs,
	  preferring a slashing continuation of long decenders and
	  diagonals as in some manuscripts Made Kai and kai not to go below
	  baseline Made Psi a little broader Made small khei distinct from
	  cap, Revised Hori and hori. Some of this toward
	  https://savannah.gnu.org/bugs/?25289

2009-01-08 22:23 +0000 [r1180]  Stevan_White:

	* FreeSerif.sfd: Various alterations to Coptic:
	  Re-worked K and M, added kern table.

2009-01-07 10:03 +0000 [r1179]  Stevan_White:

	* FreeSans.sfd: Daniel Johnson's fix of TT
	  reference transformation bug in Unified Canadian Aboriginal
	  Syllabics,

2009-01-06 22:27 +0000 [r1178]  Stevan_White:

	* FreeSans.sfd: Unified Canadian Aboriginal
	  Syllabics range, courtesy of Daniel Johnson.

2009-01-04 16:12 +0000 [r1176]  Stevan_White:

	* ChangeLog: copyright dates 2009

2009-01-04 16:08 +0000 [r1175]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Added 2009 to
	  copyright dates

2009-01-04 15:59 +0000 [r1173-1174]  Stevan_White:

	* ChangeLog: changes since last release

	* AUTHORS, CREDITS: Removed
	  Glagolitic range author

2009-01-04 15:27 +0000 [r1172]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSerifBold.sfd: Added some TrueType names

2009-01-01 20:47 +0000 [r1171]  Stevan_White:

	* FreeSerif.sfd: Removde Glagolitic range, since
	  have not (yet) received OK from author. Added some TrueType Names

2008-12-31 13:06 +0000 [r1170]  Stevan_White:

	* COPYING: Updated license to GPL v3

2008-12-30 21:34 +0000 [r1169]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd: Oblique versions of
	  Daniel Johnson's Cherokee.

2008-12-30 20:42 +0000 [r1168]  Stevan_White:

	* FreeSerifBold.sfd: Cherokee Bold range from
	  Daniel Johnson.

2008-12-27 16:43 +0000 [r1165-1167]  Stevan_White:

	* tools/isMonoMono.py: 900 EM -> 800

	* FreeMonoBold.sfd,
	  FreeMonoBoldOblique.sfd: Made glyphs to lie
	  between -200 and 800 EM

	* tools/isMonoMono.py: check that glyphs lie in
	  vertical bounding boxes

2008-12-27 14:46 +0000 [r1164]  Stevan_White:

	* FreeMono.sfd,
	  FreeSerif.sfd: Extensible bracket characters
	  didn't exactly line up. Fixed. Mono: a couple of glyphs had
	  gotten out of their bounding boxes again.

2008-12-26 22:41 +0000 [r1162]  Stevan_White:

	* ChangeLog: last two day's changes

2008-12-26 22:36 +0000 [r1160-1161]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Final pre-release
	  cleanup

	* notes/maintenance.txt: nongnu -> gnu

2008-12-26 20:48 +0000 [r1159]  Stevan_White:

	* FreeSerif.sfd: Buginese vowel u was misnamed

2008-12-26 20:36 +0000 [r1158]  Stevan_White:

	* FreeMono.sfd: Yatcyrillic somehow was a mark
	  character ... fixed

2008-12-26 20:27 +0000 [r1157]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansOblique.sfd: Had to un-link references
	  in Sans: uni02B2, uni02B5 SansOblique: uni0363 because validation
	  of the TTF file said the glyph "is drawn in wrong direction" I
	  would have preferred to have understand this...

2008-12-26 20:23 +0000 [r1156]  Stevan_White:

	* sfd/Makefile: Added quick test for FontForge
	  version.

2008-12-26 19:52 +0000 [r1155]  Stevan_White:

	* FreeMonoBold.sfd,
	  FreeMonoBoldOblique.sfd: Removed kerning
	  tables (?? what were they doing here anyway??)

2008-12-26 19:43 +0000 [r1154]  Stevan_White:

	* tools/ranges.py: some changes to explanatory text

2008-12-26 18:54 +0000 [r1153]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Kerning tables for Thai.
	  Handles one common case: short letter followed by a tall one with
	  an overhang to the left.

2008-12-26 12:33 +0000 [r1152]  Stevan_White:

	* INSTALL: small corrections: better detail about
	  Windows install

2008-12-26 12:29 +0000 [r1151]  Stevan_White:

	* AUTHORS, CREDITS: added most recent
	  authors, synched up

2008-12-25 20:47 +0000 [r1150]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd: More putzing with kerning
	  tables

2008-12-25 14:43 +0000 [r1149]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Copied kerning classes
	  Serif -> SerifBold SerifItalic -> SerifBoldItalic Sans ->
	  SansOblique SansBold SansBoldOblique Some associated naming of
	  characters, etc

2008-12-25 12:51 +0000 [r1146-1148]  Stevan_White:

	* AUTHORS, CREDITS,
	  README: formatting

	* ChangeLog: brought up to date

	* notes/maintenance.txt: added new release
	  proceedures

2008-12-25 11:54 +0000 [r1145]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Additions and correction in
	  Spacing Modifier letters and IPA Extensions

2008-12-23 21:35 +0000 [r1144]  Stevan_White:

	* FreeSerif.sfd: Applied patch to Cherokee range

2008-12-20 15:57 +0000 [r1143]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: Fixed kern classes that end in
	  space (crashes FontForge)

2008-12-20 15:38 +0000 [r1142]  Stevan_White:

	* FreeSerifItalic.sfd: kerning

2008-12-20 12:17 +0000 [r1141]  Stevan_White:

	* FreeSerif.sfd: kerning

2008-12-19 00:55 +0000 [r1140]  Stevan_White:

	* FreeSerifItalic.sfd: kerning

2008-12-19 00:06 +0000 [r1139]  Stevan_White:

	* FreeSerif.sfd: kerning Some adjustments to
	  Glagolitc spacing, mark positioning

2008-12-17 23:08 +0000 [r1138]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: kerning

2008-12-16 23:36 +0000 [r1137]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: kerning

2008-12-11 22:27 +0000 [r1136]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: kerning

2008-12-10 11:03 +0000 [r1135]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd: kerning

2008-12-10 09:34 +0000 [r1134]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: kerning

2008-12-08 23:01 +0000 [r1133]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: kerning

2008-12-08 09:52 +0000 [r1132]  Stevan_White:

	* FreeSansOblique.sfd: Slanted small final
	  sigma. remedies bug #24993: U+03C2 "Greek small letter final
	  sigma" not slanted in Free Sans Oblique
	  https://savannah.gnu.org/bugs/index.php?24993

2008-12-07 19:30 +0000 [r1131]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: kerning, etc

2008-12-06 23:42 +0000 [r1130]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: kerning

2008-12-06 13:48 +0000 [r1129]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSansOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: kerning Tweek in Sans
	  having to do with addition of Latin Extended

2008-12-05 20:52 +0000 [r1128]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd: Tweeks to Latin
	  Extended Additional

2008-12-05 20:34 +0000 [r1127]  Stevan_White:

	* FreeSansBoldOblique.sfd: Added Latin Extended
	  Additional range

2008-12-05 10:35 +0000 [r1126]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Extra space at end of
	  kern class names has bad effect on FornForge script that try to
	  run through kern classes. Some FontForge call corrupts memory.
	  Got rid of extra space.

2008-12-02 21:12 +0000 [r1125]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Replaced U+0387 GREEK
	  ANO TELEIA with top dot of colon. See bug #24987: U+0387 GREEK
	  ANO TELEIA too low https://savannah.gnu.org/bugs/index.php?24987

2008-12-02 09:25 +0000 [r1124]  Stevan_White:

	* FreeSerif.sfd: more kerning in Cyrillic (broke
	  into two tables of classes)

2008-12-01 20:22 +0000 [r1122-1123]  Stevan_White:

	* FreeSerif.sfd: tweeks to kernin

	* FreeSerifBoldItalic.sfd: kerning

2008-11-30 21:17 +0000 [r1121]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Kerning for Latin and
	  Cyrillic fairly complete in Serif faces. Complete in sense that
	  it looks pretty good under Pango for English French German
	  Spanish Polish Czech Latvian But have not done Vietnamese (will
	  require many more entries). I adjust roman and italic, then copy
	  tables by hand to bold and bolditalic. Misgiving: bolditalic is
	  much too crammed Overall, I may have over-kerned. (A difficult
	  temptation to master.)

2008-11-30 15:06 +0000 [r1120]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: kerning

2008-11-30 13:43 +0000 [r1119]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd,
	  FreeSerif.sfd: kerning In Serif, modified
	  widths of some extended latin glyphs

2008-11-29 13:44 +0000 [r1118]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: kerning

2008-11-29 11:56 +0000 [r1117]  Stevan_White:

	* FreeSerif.sfd: Broke Latin kerning subtable
	  into four, hoping it will be easier to understand and maintain.

2008-11-29 11:26 +0000 [r1116]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: kerning

2008-11-29 00:38 +0000 [r1115]  Stevan_White:

	* FreeSansOblique.sfd: kerning

2008-11-28 23:43 +0000 [r1114]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSerif.sfd: kerninig

2008-11-28 21:54 +0000 [r1112-1113]  Stevan_White:

	* FreeSans.sfd: kerning

	* FreeSans.sfd,
	  FreeSerif.sfd: more kerning; made guillemot
	  narrower

2008-11-28 10:26 +0000 [r1111]  Stevan_White:

	* FreeSans.sfd: kerning

2008-11-28 00:11 +0000 [r1109-1110]  Stevan_White:

	* FreeSansOblique.sfd,
	  FreeSerif.sfd: previous commit was incomplete

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Adjusted width of single
	  quotes (and apostrophe) to be "punctuation width" More fiddling
	  with kerning.

2008-11-27 22:37 +0000 [r1108]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: much fiddling with kerning

2008-11-26 19:44 +0000 [r1107]  Stevan_White:

	* FreeSerifBold.sfd: Basic kerning, named main
	  Cyrillic letters

2008-11-26 18:41 +0000 [r1106]  Stevan_White:

	* FreeSerifItalic.sfd: Basic Cyrillic kerning

2008-11-26 07:03 +0000 [r1105]  Stevan_White:

	* FreeSerif.sfd: further tweeks to Cyrillic
	  kerning

2008-11-26 06:58 +0000 [r1104]  Stevan_White:

	* FreeSerif.sfd: Tweeks to Cyrillic kerning

2008-11-26 00:32 +0000 [r1103]  Stevan_White:

	* FreeSerifItalic.sfd,
	  sfd/Makefile, FreeSerif.sfd,
	  FreeSerifBold.sfd: Much fiddling with kerning,
	  tables, and generating fonts whose kerning tables work with
	  OpenOffice.

2008-11-25 23:59 +0000 [r1102]  Stevan_White:

	* tools/GenerateTrueType: Made to include old-style
	  kerning Converted to Python

2008-11-24 08:11 +0000 [r1100-1101]  Stevan_White:

	* FreeSerif.sfd: Prev commit to validate

	* FreeSerif.sfd: regularized padding in
	  Miscellaneous symbols. At least within related ranges tried to
	  make similar.

2008-11-23 20:21 +0000 [r1097-1099]  Stevan_White:

	* tools/ranges/__init__.py (added),
	  tools/ranges/OpenType.py (added),
	  tools/ranges (added): meant to add these before
	  moving the ranges.py file

	* tools/range_report (removed),
	  tools/range_report.py (added): preferred to keep
	  the file ending

	* tools/range_report (added): Starting to break out
	  different functionalities of this large script

2008-11-23 15:38 +0000 [r1096]  Stevan_White:

	* FreeSerif.sfd: Completed Miscellaneous
	  Symbols, with some drawings from George Douros' Unicode Symbols,
	  and some of mine.

2008-11-23 14:21 +0000 [r1095]  Stevan_White:

	* FreeSerif.sfd: Filled out Miscellaneous
	  Symbols. Used George Douros' Unicode font.

2008-11-23 12:57 +0000 [r1094]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoOblique.sfd: Replaced Greek Exteded
	  psili and dasia with scaled versions of the "bent quote" mark. I
	  think it's distinctive enough, but not so silly. Remedies bug
	  #22997: Mono: Greek Extended psili is ugly
	  https://savannah.gnu.org/bugs/?22997

2008-11-23 00:47 +0000 [r1093]  Stevan_White:

	* FreeSerif.sfd: Made some recycling symbols

2008-11-22 23:07 +0000 [r1092]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Finished with Hebrew
	  Pointed letters in all faces.

2008-11-22 16:36 +0000 [r1091]  Stevan_White:

	* FreeSans.sfd: Fiddled with Hebrew Pointed
	  letters

2008-11-22 15:45 +0000 [r1090]  Stevan_White:

	* FreeSerifItalic.sfd: Marks for Vietnamese

2008-11-22 10:32 +0000 [r1089]  Stevan_White:

	* CREDITS: fixed some links having to do with Omega,
	  intlfonts

2008-11-22 09:32 +0000 [r1088]  Stevan_White:

	* CREDITS: Fixed/removed broken links

2008-11-21 23:53 +0000 [r1087]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: Letter pe had strange thick
	  middle ear that looked awful. lamed had ben bumped at some point.
	  Fixed. Adjusted some of the points.

2008-11-21 22:54 +0000 [r1086]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: repairs to Pointed Hebrew

2008-11-21 21:29 +0000 [r1085]  Stevan_White:

	* FreeSerif.sfd: Numeral line positioning marks
	  for Gothic

2008-11-21 10:17 +0000 [r1084]  Stevan_White:

	* FreeSerifItalic.sfd: Added Combining Marks for
	  Symbols (some question about obliqueness of some symbols) Cleaned
	  up some empty glyphs in Pointed Hebrew.

2008-11-20 21:00 +0000 [r1083]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Provided Hebrew pointed
	  letters, with lookups, for all Serif faces.

2008-11-19 22:30 +0000 [r1082]  Stevan_White:

	* FreeSerifBoldItalic.sfd: renamed Hebrew
	  lookups

2008-11-19 22:12 +0000 [r1081]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Completed Hebrew in Bold
	  faces.

2008-11-19 00:04 +0000 [r1080]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeSerif.sfd: More tweeks to Hebrew points

2008-11-18 23:44 +0000 [r1079]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd: Adjustments corrections and
	  additions to Hebrew points

2008-11-18 20:56 +0000 [r1078]  Stevan_White:

	* FreeSansBold.sfd: Cleaned out a lot of
	  ridiculous kernings

2008-11-16 23:39 +0000 [r1077]  Stevan_White:

	* FreeSansBoldOblique.sfd: fiddled with Armenian
	  ligatures

2008-11-16 23:26 +0000 [r1076]  Stevan_White:

	* FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Added Armenian (with
	  ligatures) to BoldOblique Fiddled with character spacing

2008-11-16 22:56 +0000 [r1075]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Added U+01f9 and
	  paragraph end marker to Georgian Fiddled with Armenian ligatures

2008-11-15 13:36 +0000 [r1074]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Added Georgian turned
	  gan 10F9 (because it was easy)

2008-11-15 13:28 +0000 [r1073]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Another pass at
	  letter spacing in Cyrillic. Also went through ancient letters.
	  Added Georgian paragraph separator 10FB

2008-11-15 10:11 +0000 [r1072]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Re-worked letter
	  spacing through modern Cyrillic range.

2008-11-15 00:05 +0000 [r1071]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSerif.sfd: Letter spacing

2008-11-14 22:48 +0000 [r1070]  Stevan_White:

	* FreeSerif.sfd: Added several characters to
	  Cyrillic Extended-B

2008-11-14 09:56 +0000 [r1069]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd: Made Cyrillic hooked
	  e U+04BC-F to look less goofy.

2008-11-14 09:36 +0000 [r1068]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Based on assertion on
	  Pechatnyj Dvor's web site, Cyrillic Fita U+0472-3 and "Barred O"
	  U+04E8-9 are different styles the same letter, and the fact that
	  the tilde in the O never looked good in Sans, I made them all
	  barred O's.

2008-11-14 09:32 +0000 [r1067]  Stevan_White:

	* FreeSerif.sfd: Added Cyrillic Yn, yn
	  (U+a65e-f)

2008-11-14 08:30 +0000 [r1066]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerifBold.sfd: Synced up Cyrillic and
	  Combining Diacritics ranges, Couple of tweeks in Gujarati to make
	  TT validate

2008-11-13 23:44 +0000 [r1065]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Changes to older Cyrillic
	  letters, in response to further information. Made omegas, omegas
	  with titlo, and OT to all be of the same size and shape in Serif.
	  Un-linked Cyrillic Psi and psi from Greek, made squarer versions.
	  Some more Cyrillic diacritical marks in Sans. Re-worked U+04bc-f
	  . Experimenting with mark positioning for Cyrillic

2008-11-12 22:53 +0000 [r1064]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Strove to make Euro
	  look more like the EC logo design, while making glyph fit better
	  with the design of its face. See bug #3576: Euro design
	  https://savannah.gnu.org/bugs/?23576

2008-11-12 21:33 +0000 [r1063]  Stevan_White:

	* FreeSans.sfd: Tweeks to previous commit

2008-11-12 21:10 +0000 [r1062]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Adjustments mostly to GPOS
	  tables having to do with Vietnamese marks. The WAZU Vietnamese
	  test page looks pretty good in Sans now. Still not thrilled with
	  below-dot when it appears with a mark over e.g. U+0102. Pango
	  positions one or the other but not both.

2008-11-12 00:04 +0000 [r1061]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Extensive
	  modification of GPOS lookup tables for mark positioning. I think
	  they're now all functional (except styled Mono faces have none).
	  Also added lots of marks to faces that didn't have them, and also
	  fiddled with Combining Diacritical Marks.

2008-11-10 19:53 +0000 [r1060]  Stevan_White:

	* FreeSerif.sfd: Made one combining mark really
	  combining

2008-11-10 19:49 +0000 [r1059]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Made a few combining
	  characters to be zero-width in Mono, Added them to other styles.

2008-11-09 23:49 +0000 [r1058]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Figured out why below marks
	  in Thai weren't working in the lowest letters. I think Pango and
	  other font renderers ignore 'blwm'. 'mark' works though.

2008-11-09 23:21 +0000 [r1057]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Tweeks to Thai marks

2008-11-09 22:56 +0000 [r1056]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Several bugfixes in Thai,
	  mostly having to do with mark placement and ligatures.
	  Implemented ru-saraaa and lu-saraaa with ligatures.

2008-11-09 21:21 +0000 [r1055]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: added and named
	  dottedcircle (used by Pango to render combining mark base)

2008-11-09 12:42 +0000 [r1054]  Stevan_White:

	* FreeSerif.sfd: More tweeks to Coptic

2008-11-09 12:22 +0000 [r1053]  Stevan_White:

	* FreeSerif.sfd: Tweeks to Coptic, after viewing
	  more papyrus samples and web pages.

2008-11-09 10:41 +0000 [r1052]  Stevan_White:

	* FreeSerif.sfd: Weight of Coptic small letters
	  made to match that of Latin and Greek ones.

2008-11-08 20:05 +0000 [r1051]  Stevan_White:

	* FreeSerif.sfd: Coptic tweek spacing

2008-11-08 19:22 +0000 [r1050]  Stevan_White:

	* FreeSerif.sfd: Made Coptic to comply better
	  with http://www.wazu.jp/gallery/Test_Coptic.html Made a flourish
	  at foot of letters with long diagonal.

2008-11-08 15:51 +0000 [r1049]  Stevan_White:

	* FreeSerif.sfd: Coptic: punctuation marks were
	  in the wrong row.

2008-11-08 15:38 +0000 [r1048]  Stevan_White:

	* FreeSerif.sfd: More tweeks to Coptic; put in a
	  mark lookup table. Note: for small letters I made scaled
	  references to captials. Results in those letters looking quite
	  light next to the capitals and next to small Latin letters. Also,
	  there are a few variant forms for capitals (Unicode samples don't
	  show this). It would be good to re-work

2008-11-08 13:27 +0000 [r1047]  Stevan_White:

	* FreeSerif.sfd: Added Coptic alphabet in
	  u+2C80-2CB1 and u+03E2-u+03EF, drawn/built by me, based on
	  Unicode samples, TeX font copte, and scans at WikiPedia.

2008-11-07 08:12 +0000 [r1046]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Further tweeks to dieresis
	  in Greek and Cyrillic

2008-11-07 07:41 +0000 [r1045]  Stevan_White:

	* FreeSerif.sfd: replaced Greek I dieresis with
	  references, tweeked height of dieresis.

2008-11-04 08:42 +0000 [r1044]  Stevan_White:

	* FreeSerif.sfd: Added a few Cyrillic Extended-B
	  letters seen in web pages while looking for Glagolitic text.

2008-11-04 00:29 +0000 [r1042-1043]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoOblique.sfd: Added a few old Cyrillic
	  characters.

	* FreeSerif.sfd: Several corrections and tweeks
	  to Glagolitic. Still missing six slots from Unicode, but don't
	  see them in the TeX fonts. On the other hand, several on-line
	  Glagolitic pages (bibles etc) don't seem to use these. Maybe it's
	  OK as-is.

2008-11-03 09:53 +0000 [r1041]  Stevan_White:

	* FreeSerif.sfd: Added lowercase range to
	  Glagolitic, as a facile scaling of the uppercase.

2008-11-03 09:50 +0000 [r1040]  Stevan_White:

	* FreeSerif.sfd: Added letter to Glagolitic,
	  scaled range.

2008-11-02 22:59 +0000 [r1039]  Stevan_White:

	* FreeSerif.sfd: Replaced fraktur bold from
	  Mathematical Alphanumeric Symbols with that from TX Fonts by
	  Young Ryu. One concern: letter k is damaged (in both medium and
	  bold). I just hacked something up. Added Glagolitic "round type"
	  font (Croation capitols only) from the collection of Croatian
	  fonts for LaTeX by Darko ZubriniÄ
	  ftp://ftp.dante.de/tex-archive/languages/croatian/
	  http://www.tug.org/TUGboat/Articles/tb17-1/tb50zubr.pdf Several
	  letters are missing besides the small letters.

2008-11-02 09:06 +0000 [r1038]  Stevan_White:

	* FreeSerifBoldItalic.sfd: A couple of Thai
	  references got obliqued twice.

2008-11-02 08:57 +0000 [r1037]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: By popular demand, removed
	  'ears' from Greek Upsilon and Psi. Copied resulting glyphs to
	  Serif Mathematical Alphanumeric Symbols.

2008-11-02 00:49 +0000 [r1036]  Stevan_White:

	* FreeSerif.sfd: Some pointwise cleanup of main
	  Tamil range

2008-11-01 23:20 +0000 [r1035]  Stevan_White:

	* FreeSerif.sfd: Tried some things with lookups.
	  Didn't make much headway.

2008-11-01 22:34 +0000 [r1033-1034]  Stevan_White:

	* FreeMono.sfd: somehow made a letter with wrong
	  width

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Added similar lookups and
	  ligatures to Thai ranges.

2008-11-01 18:34 +0000 [r1032]  Stevan_White:

	* FreeSerif.sfd: Lookups now work no worse than
	  those for other Thai fonts, at least in Pango. Still perplexed by
	  behaviour of "Required" lookups.

2008-11-01 15:44 +0000 [r1031]  Stevan_White:

	* FreeSerif.sfd: For Thai, made ligatures and
	  lookups for yoying and thothan combined with a lower vowel. These
	  work well. Attempted looksups for saraaa with ru and lu, and for
	  saraam. Not working.

2008-10-31 23:54 +0000 [r1030]  Stevan_White:

	* FreeSans.sfd: Cleaned up a few of the Bengali
	  ligatures

2008-10-31 23:46 +0000 [r1029]  Stevan_White:

	* FreeSerifBold.sfd: Tweek Thai

2008-10-31 22:48 +0000 [r1028]  Stevan_White:

	* FreeSerif.sfd: Fixed ligatures and mark
	  positioning for HanunÃ³o. Problem with ligatures: Gnome pango
	  doesn't do 'rlig', only 'liga'

2008-10-31 21:27 +0000 [r1027]  Stevan_White:

	* FreeSerifItalic.sfd: Changed lookup table
	  scripts for Devanagari and Bengali. Find Problems -> ATT found
	  several problems showing lookups acting on glyphs that weren't
	  listed in the script ranges, including dev2, bng2 (why not deva
	  and beng, I don't know). danda and doubledanda of Devanagari I
	  understand are to be shared among Indic scripts. So included bng2
	  and dev2 in the 'aalt' table for those. The 'init' and 'half'
	  tables for Bengali made active for bng2. The 'locl' table for
	  Bengali didn't do anything I could see: It mapped the Devanagari
	  danda to itself, and the doubledanda to itself. Deleted.

2008-10-31 20:10 +0000 [r1026]  Stevan_White:

	* FreeSerifItalic.sfd: Cleaned up some kern
	  tables. adjustments of under 5 EM are invisible. Some others I
	  just didn't like. Some were putting a letter beneath another,
	  with is wrong.

2008-10-31 19:39 +0000 [r1025]  Stevan_White:

	* FreeSerifBoldItalic.sfd: Added Thai

2008-10-31 19:02 +0000 [r1024]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBold.sfd: Changes to mark positioning
	  lookups, esp. in Italic. Widened numerals in Bold

2008-10-27 19:00 +0000 [r1023]  Stevan_White:

	* FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Tweeks regarding
	  Armenian and lookups

2008-10-27 18:48 +0000 [r1022]  Stevan_White:

	* FreeSansBold.sfd: Added Armenian ligatures
	  FB13-FB17 with lookups Also made a historical ligature ('hlig')
	  table for u+0587. Toward bug #15183: missing characters from
	  Armenian range (FreeSans, Bold)
	  https://savannah.gnu.org/bugs/index.php?15183

2008-10-27 09:17 +0000 [r1021]  Stevan_White:

	* FreeSansOblique.sfd: Added Armenian ligatures,
	  lookups. Cleaned up contours.

2008-10-26 23:45 +0000 [r1020]  Stevan_White:

	* FreeSans.sfd: Added 5 Armenian ligatures to
	  U+FB13 â FB17, and made corresponding 'liga' lookup. Found
	  there one ligature u+0587 that according to
	  http://en.wikipedia.org/wiki/Armenian_alphabet "in new
	  orthography the Ö character is not a typographical ligature
	  anymore, and must never be treated as such. It is a distinct
	  letter and has its place in the new alphabetic sequence." So
	  moved this out of the 'liga' lookup and into a new 'hlig' lookup.

2008-10-26 22:26 +0000 [r1019]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Lots of improvements to
	  Thai. Completely revised letter spacing in Italic, and fiddled
	  with combining marks in all. Still aren't working quite right,
	  especially in Italic. Still need to work over digits (in Bold
	  they aren't even bold yet)

2008-10-26 14:21 +0000 [r1018]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Bold Thai : added
	  (painstakingly) constructed glyphs, lookups roman Thai: tweeks
	  Italic Thai: tweeks (Note this still has multiple problems)

2008-10-25 08:26 +0000 [r1017]  Stevan_White:

	* FreeSerif.sfd: WAZU says
	  http://www.wazu.jp/gallery/Fonts_Hanunoo.html MPH 2B Damase
	  doesn't support the consonant-vowel ligatures necessary to render
	  Buhid writing. OK, so I made 'mark' lookups for combining marks
	  and a bunch of ligatures in an 'rlig' lookup. The latter still
	  not working: don't know why. Made page to match the example of
	  the combining forms at
	  http://www.omniglot.com/writing/hanunoo.htm

2008-10-24 21:56 +0000 [r1016]  Stevan_White:

	* FreeSerif.sfd: Removed some marks from
	  Mathematical Alphanumeric Symbols

2008-10-24 08:13 +0000 [r1015]  Stevan_White:

	* FreeSerif.sfd: Tweeked combining marks for
	  Vietnamese. Made to satisfy WAZU JAPAN Comprehensive Unicode Test
	  Page for Vietnamese / Quá»c Ngá»¯
	  http://www.wazu.jp/gallery/Test_Vietnamese.html Could still use
	  some tweeking...

2008-10-24 07:25 +0000 [r1014]  Stevan_White:

	* FreeSerif.sfd: Added marks for composition of
	  Vietnamese

2008-10-23 22:03 +0000 [r1013]  Stevan_White:

	* FreeMono.sfd,
	  FreeSerif.sfd: Put "below" combining mark on
	  lots of vowels and derivatives, for Vietnamese. Named a bunch of
	  composit Latin, expecting to make substitutions.

2008-10-23 20:14 +0000 [r1012]  Stevan_White:

	* FreeSerif.sfd: Thai spacing alterations based
	  on advice of a native speaker.

2008-10-22 06:30 +0000 [r1011]  Stevan_White:

	* FreeSerif.sfd: re-named Thai lookups according
	  to order

2008-10-21 21:28 +0000 [r1010]  Stevan_White:

	* FreeSans.sfd: Cleanup of glyphs in Gujarati,
	  Devanagari. Note: Serious problem with Sans GPOS abvm in
	  Devanagari "'abvm' Above Base Mark in Devanagari subtable"
	  "gujr-0" But all the characters that list gujr-0 are in Gujarati.
	  Not sure how this got broken or how to fix it.

2008-10-21 19:32 +0000 [r1009]  Stevan_White:

	* FreeSerif.sfd: Fiddled with Thai mark
	  positioning: passes my tests now OK. Made a few more references
	  in Math Symbols; more regularization of stroke.

2008-10-21 09:01 +0000 [r1008]  Stevan_White:

	* FreeSerif.sfd: Added mark class for Vietnamese
	  "horn" Several references made in General Punctuation, Arrows

2008-10-20 22:47 +0000 [r1007]  Stevan_White:

	* FreeMono.sfd: added some Combining Diacritical
	  Marks

2008-10-20 18:41 +0000 [r1006]  Stevan_White:

	* FreeSerif.sfd: Made some references from
	  serifed Latin capitals to Greek counterparts.

2008-10-20 18:24 +0000 [r1005]  Stevan_White:

	* FreeSerif.sfd: Made a few repeated glyphs into
	  references in Musical Symbols

2008-10-20 17:33 +0000 [r1004]  Stevan_White:

	* tools/ranges.py, tools/validate.py:
	  tweeked some ranges, format of output

2008-10-19 19:36 +0000 [r1003]  Stevan_White:

	* FreeSerif.sfd: Moved several glypns from
	  Mathematical Alphanumeric Symbols to Letterlike Symbols. Couple
	  tweeks in Mathematical Symbols.

2008-10-19 18:30 +0000 [r1002]  Stevan_White:

	* FreeMono.sfd,
	  FreeSerif.sfd: Fiddling with Mathematical
	  Symbols. In Serif, trying to make stroke width more consistent.

2008-10-19 16:47 +0000 [r1001]  Stevan_White:

	* FreeMono.sfd: Tweek

2008-10-19 16:44 +0000 [r1000]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Added some Greek
	  symbols in Mono and Sans to make a little more regular and
	  correspond better with TeX. Tweek of serif.

2008-10-19 14:47 +0000 [r999]  Stevan_White:

	* FreeSansBold.sfd: a few more improvements.
	  (One problem with the Mathematical Alphanumeric area is, one must
	  remember to change it any time another face is altered...)

2008-10-19 14:41 +0000 [r998]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerifBold.sfd: Several improvements and
	  additions to Sans faces (mostly in Greek) from experience of
	  pasting into FreeSerif Mathematical Alphanumeric Symbols.

2008-10-19 14:37 +0000 [r997]  Stevan_White:

	* FreeSerif.sfd: Replaced most of Mathematical
	  Alphanumeric Symbols roman italic bold (latin and greek) gothic
	  italic bold (latin and greek) typewriter and numerals with glyphs
	  from FreeFont. These were scaled to uniform height. Remains:
	  Blackboard Bold, Fraktur, Calligraphic, Script

2008-10-19 12:03 +0000 [r996]  Stevan_White:

	* FreeSerif.sfd: Tidied lookup table names for
	  Malayalam

2008-10-19 11:49 +0000 [r995]  Stevan_White:

	* FreeSerif.sfd: Applied Malayalam patch from
	  Hiran Venugopalan

2008-10-19 10:27 +0000 [r993-994]  Stevan_White:

	* FreeMono.sfd: Added/corrected many
	  Mathematical Symbols

	* FreeSansOblique.sfd: more IPA

2008-10-18 11:20 +0000 [r992]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Made lots more IPA
	  and Phonetic Extensions Note: fontforge is reporting an error in
	  a few glyphs made by scaling another, that the glyphs are drawn
	  in the wrong direction--only in TrueType though. Suspect a
	  FontForge bug.

2008-10-17 23:10 +0000 [r991]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Added several
	  Combining Diacritical Marks

2008-10-17 20:33 +0000 [r990]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Made several Spacing
	  Modifier Letters, Combining Diacritical Marks, and IPA and
	  Phonetic Extensions

2008-10-17 06:40 +0000 [r989]  Stevan_White:

	* CREDITS: Hiran Venugopalan of Swathanthra
	  Malayalam Computing George Douros Daniel Johnson

2008-10-16 20:53 +0000 [r988]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Added some Superscripts and
	  Subscripts

2008-10-16 20:01 +0000 [r987]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Finished off
	  Superscripts and subscripts

2008-10-16 19:06 +0000 [r986]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Completed General
	  Punctuation for Mono faces

2008-10-16 08:08 +0000 [r985]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Added some General
	  Punctuation

2008-10-16 06:48 +0000 [r983-984]  Stevan_White:

	* tools/ranges.py: un-grayed totals rows

	* tools/ranges.py: Fiddled Cyrillic made output
	  prettier

2008-10-15 18:28 +0000 [r982]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: more Letterlike
	  Symbols, Currency Symbols

2008-10-15 08:49 +0000 [r981]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Made some Combining
	  Diacritical Marks for Symbols, Letterlike Symbols

2008-10-14 22:18 +0000 [r980]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Added some General
	  Punctuation

2008-10-14 21:24 +0000 [r979]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Added double slanted
	  hyphen, made General Punctuaton Supplement like Serif's

2008-10-14 21:03 +0000 [r977-978]  Stevan_White:

	* FreeSansBoldOblique.sfd: Filled out Greek
	  Extended

	* FreeSerifItalic.sfd,
	  FreeMono.sfd: fixes to last 2 commits

2008-10-14 19:51 +0000 [r976]  Stevan_White:

	* FreeSerifItalic.sfd: Last character to General
	  Punctuation

2008-10-14 19:43 +0000 [r975]  Stevan_White:

	* FreeMono.sfd: Built some Enclosed
	  Alphanumerics (1-10)

2008-10-14 07:26 +0000 [r974]  Stevan_White:

	* FreeSerif.sfd: Copied in Daniel Johnson's
	  changes to Cherokee.

2008-10-12 15:42 +0000 [r973]  Stevan_White:

	* FreeSerif.sfd: Included Daniel Johnson's
	  Cherokee glyphs.

2008-10-05 11:20 +0000 [r972]  Stevan_White:

	* FreeMono.sfd: Further corrections to diaresis
	  in Cyrillic -- legibility in small sizes

2008-10-05 10:43 +0000 [r971]  Stevan_White:

	* FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd: Regularized placement of
	  diaresis in Cyrillic

2008-10-05 09:07 +0000 [r970]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Added same set of
	  glyphs to Cyrillic Supplement

2008-10-04 22:26 +0000 [r969]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Added some of the
	  easier letters from Cyrillic Supplement

2008-10-04 20:27 +0000 [r968]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoBoldOblique.sfd: Finished high
	  Cyrillic range for MonoBold and MonoBoldOblique. (Remaining:
	  historic ranges, Cyrillic extensions) Tweeked others.

2008-10-04 13:26 +0000 [r967]  Stevan_White:

	* INSTALL: Added info about Adobe ATM

2008-10-03 22:40 +0000 [r966]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeMonoBold.sfd: Serif*Italic: Added last
	  Abkhazian letters to Cyrillic MonoBold: tweek

2008-10-03 16:23 +0000 [r965]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoOblique.sfd: Mono: Some additions to
	  historic letters

2008-10-03 14:55 +0000 [r964]  Stevan_White:

	* FreeSerif.sfd: Added some punctuation and
	  combining numeric marks from Cyrillic Extended B

2008-10-03 13:21 +0000 [r963]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeSerifBold.sfd: Various technical tweeks,
	  mostly concerning recent additions. Also did a bit more "Points
	  too close" and "irrelevant control points". Cyrillic millions
	  redesign meant could not maintain use of references for it.

2008-10-03 11:03 +0000 [r962]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: More high Cyrillic Included
	  old Cyrillic millions combining mark in Sans, changed design in
	  Serif

2008-10-02 21:44 +0000 [r961]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: More high Cyrillic

2008-10-02 17:45 +0000 [r960]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMonoOblique.sfd: More high Cyrillic glyphs

2008-10-02 08:55 +0000 [r959]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd: More glyphs in high
	  Cyrillic. Remains only some whose form I'm unsure of in italic.

2008-10-02 07:44 +0000 [r958]  Stevan_White:

	* FreeSerifBoldItalic.sfd: More glyphs in higher
	  Cyrillic range

2008-10-01 23:02 +0000 [r957]  Stevan_White:

	* FreeSerifItalic.sfd: Same process of
	  tightening el, em, ge (but a P.S. to previous commit: also did
	  ya, ze for SerifBold.

2008-10-01 22:54 +0000 [r956]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: BoldItalic: Tightened up
	  spacing on left of el, em, ge (could go farther, but it is partly
	  a problem with glyph design...

2008-10-01 22:10 +0000 [r955]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: More additions to Cyrillic.
	  Finished SerifBold except for Nivkh additions. Used references on
	  number combining forms.

2008-09-29 22:44 +0000 [r954]  Stevan_White:

	* FreeSerif.sfd: Added four (obsolete) Chuvash
	  letters to Cyrillic Supplement - completing it.

2008-09-29 21:32 +0000 [r953]  Stevan_White:

	* FreeSerif.sfd: Greek adjustments Adjusted
	  spacing of kappa slightly Got rid of ears on Psi, following
	  similar request for Upsilon.

2008-09-28 17:02 +0000 [r952]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Adding and fiddling with
	  Spacing Modifiers and Combining Diacriticals

2008-09-28 10:30 +0000 [r951]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Revisions of several
	  Combining Diacritical marks

2008-09-28 10:25 +0000 [r950]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: More Combining
	  Diacriticals

2008-09-28 08:18 +0000 [r949]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Still more Spacing
	  Modifiers and Combining Diacriticals

2008-09-28 00:10 +0000 [r948]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: A few Combining
	  Diacriticals and Spacing Modifiers

2008-09-27 23:45 +0000 [r947]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: MonoBoldOblique:
	  Primarily filling out Spacing Modifier Letters others: little
	  fixes found along the way

2008-09-27 13:04 +0000 [r946]  Stevan_White:

	* FreeSerif.sfd: Replaced Malayalam range with
	  that from Rachana_04 found on Swathanthra Malayalam Computing
	  project page http://savannah.nongnu.org/projects/smc/ Besides
	  scaling and converting to cubic, performed much clean-up of
	  glyphs, added an r2 character, and re-named a bunch of
	  characters.

2008-09-22 22:06 +0000 [r945]  Stevan_White:

	* tools/ranges.py: Corrections and additions to
	  several ranges, put extra row in table with character totals

2008-09-22 21:41 +0000 [r944]  Stevan_White:

	* FreeSerif.sfd: Filled in as much of Phonetic
	  Extensions as I could without artistic abilities. Note 1D48-9 are
	  not references due to apparent FontForge bug, that says scaled
	  references go in wrong direction.

2008-09-22 19:20 +0000 [r943]  Stevan_White:

	* FreeSerif.sfd: Cleaup of some Bengali glyphs.
	  Note many of the ligatures remain very very messy.

2008-09-22 14:29 +0000 [r940-942]  Stevan_White:

	* Makefile: made some lines quiet

	* sfd/Makefile: added more validations made to work
	  with GenerateOpenType

	* tools/GenerateOpenType: Replaced bash version with
	  Python

2008-09-22 09:23 +0000 [r939]  Stevan_White:

	* FreeSerif.sfd: Built two more easy Phonetic
	  Extensions

2008-09-22 09:14 +0000 [r938]  Stevan_White:

	* FreeSerif.sfd: Built some Phonetic Extensions
	  letters, those with middle tilde

2008-09-21 20:46 +0000 [r937]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Added lots of Spacing
	  Modifier Letters and Combining Diacritical Marks.

2008-09-21 15:34 +0000 [r936]  Stevan_White:

	* FreeSans.sfd, FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Wrote script to check
	  if glyph encodings were in stated ranges, fixed most
	  discrepancies. There were a bunch of incompletely deleted
	  characters in several faces. Sans: found several other problems
	  in the process Tamil Four slots labelled 0BDA-D have glyphs, not
	  in Unicode. also 0BE1 I think they are misplaced; added 0010 to
	  each of them Devanagari Slot labelled U+093B is not in
	  Unicode--can't find glyph: deleted likewise 094F (may have been
	  meant to be 0954) 0955, 0973-0976 0954 should be a combining
	  mark, but it appears on the wrong side of 0. 0971 was just
	  wrong--made into simple dot. 0972 is also wrong--made my own
	  Candra A. Gujarati Slots labelled 0AE4-5 are not in Unicode; seem
	  not to belong at all. Deleted. 2800 is a dup of 2790. Deleted
	  Serif: phillipine_double u1736 was misplaced A bunch of the Math
	  Alphanumeric symbols are empty in the standard, because they're
	  represented elsewhere. These should be deleted (TO DO) First need
	  to make style consistent with existing symbols.

2008-09-21 11:41 +0000 [r935]  Stevan_White:

	* FreeSerif.sfd: Applied patch from Daniel J
	  Remedies bug FreeSerif: Missing glyphs with palatal hook
	  https://savannah.gnu.org/bugs/index.php?24298 Adding several
	  letters to Phoenetic Extensions range U+1D80-BF

2008-09-21 11:00 +0000 [r934]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Made four characters
	  U+200C-F to be zero-width Remedy to bug #23593: Mono 0-width
	  chars: zero-width or space?
	  https://savannah.gnu.org/bugs/index.php?23593

2008-09-20 23:17 +0000 [r933]  Stevan_White:

	* FreeSerif.sfd: Made Mahjong tiles to take up
	  less space using references Cleaned up several validation
	  problems

2008-09-19 22:54 +0000 [r932]  Stevan_White:

	* tools/checkGlyphNumbers.py: included other Private
	  Use Ranges

2008-09-19 20:48 +0000 [r931]  Stevan_White:

	* tools/checkGlyphNumbers.py (added): Added script
	  to check that glyph numbers match Unicode (with complication)

2008-09-19 20:36 +0000 [r930]  Stevan_White:

	* tools/ranges.py: corrected and simplified
	  calculation of whether OS/2 bit is set. Now works mostly
	  perfectly, except for some high Unicode ranges...

2008-09-19 20:25 +0000 [r928-929]  Stevan_White:

	* FreeSerif.sfd: Added several Hebrew Alphabetic
	  Presentation Forms (some easy ones), to make its coverage the
	  same as Serif Bold.

	* FreeSerifBold.sfd: Re-encoded. Deleted several
	  glyphs in Hebrew Alphabetic Presentation Forms that didn't
	  correspond valid Unicode

2008-09-19 20:19 +0000 [r927]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd,
	  FreeMonoBold.sfd: Ran script to find
	  mis-numbered glyphs. Several were simply typos, some offset by
	  one.

2008-09-19 20:13 +0000 [r926]  Stevan_White:

	* FreeSansOblique.sfd: Numerous cases of glyphs
	  in Private Use area incorrectly assigned Unicode numbers and
	  names. Gave all -1 for Unicode and named like "slot.XXXX".

2008-09-19 09:51 +0000 [r921-925]  Stevan_White:

	* tools/GenerateOpenType: Made to output old-style
	  kerning tables for Windows (FontForge complains though)

	* tools/GenerateTrueType: Made to hint before
	  instructing: FontForge bases TT instructions on hints although
	  hints are not put into TrueType fonts.

	* tools/hex_range.py: cosmetic tweek

	* tools/isMonoMono.py: re-worked to take a list of
	  files as input

	* tools/ranges.py: re-structured code and corrected
	  a few ranges

2008-09-19 09:17 +0000 [r920]  Stevan_White:

	* FreeSerif.sfd: Adapted Mahjong Tiles from
	  George Douros' Unicode Symbols font.

2008-09-18 23:27 +0000 [r919]  Stevan_White:

	* FreeSerif.sfd: Added Domino Tiles. Domino
	  outline is copied from George Douros' Unicode Symbols, but the
	  rest I preferred to do with references.

2008-09-18 21:30 +0000 [r918]  Stevan_White:

	* FreeSerif.sfd: Adapted Mathematical
	  Alphanumeric Symbols from George Douros' Unicode Symbols font.

2008-09-18 21:02 +0000 [r917]  Stevan_White:

	* FreeMonoBoldOblique.sfd: This one got away
	  from me--I don't know what I did. Looks like some small contour
	  edits.

2008-09-18 20:58 +0000 [r915-916]  Stevan_White:

	* FreeSansBoldOblique.sfd: Fixed one mis-numberd
	  character in Latin Extended-B

	* FreeSerifItalic.sfd,
	  FreeSerifBold.sfd: Fixed several mis-numbered
	  characters.

2008-09-18 20:13 +0000 [r914]  Stevan_White:

	* FreeSansBold.sfd: SansBold: one Georgian
	  letter with no name, one Zapf Dingbat was unnumbered ATT test
	  shows a bunch of problems with Gurmukhi and 'blwf' table indeed
	  shows those letters at 0x10000+ Sans names them like
	  uni0A30_uni0A4D.blwf: they are in range ECC6 to ED06 I meant to
	  move this range into Private Use in last release, and missed it.
	  So now it is moved, into same range as Sans. Both Sans and
	  SansBold in nukt table for Gurmukhi have duplicate entries for
	  uni0A15 uni0A3C. Deleted dups.

2008-09-18 19:05 +0000 [r913]  Stevan_White:

	* FreeMonoOblique.sfd: fixed a number of
	  Unassigned Code Points in Greek Extended

2008-09-18 18:31 +0000 [r912]  Stevan_White:

	* FreeSansOblique.sfd: mis-numbered Combining
	  Diacritics

2008-09-18 18:24 +0000 [r911]  Stevan_White:

	* FreeSansOblique.sfd: Several chars in Latin
	  Extended hadn't been named. One spurious letter in Letterlike
	  Symbols

2008-09-16 19:37 +0000 [r910]  Stevan_White:

	* FreeSans.sfd, FreeMono.sfd,
	  FreeSerif.sfd: Lots of additions: unless
	  otherwise noted, they are from George Duros' fonts Analecta,
	  Music, and Unicode (haven't got final confirmation of the
	  eligibility of these glyphs, so this is just for testing.) Added
	  some combining marks, fiddled a bit. In both Serif & Mono, tried
	  to get a key symbol characters to fit inside the key combining
	  mark Serif Got rid of ears on Upsilon Added: Gothic Western &
	  Byzantine Musical Symbols Misc Symbols, Misc Technical Symbols
	  (also drew many myself) Supplemental Symbols and Arrows Mono
	  Added: lotsa Misc Technical Symbols OCR Symbols drew many
	  Supplemental Symbols and Arrows, Misc Technical Sans Added
	  Phoenecian Made a few Letterlike Symbols; Made Re and Im to be
	  sans-serif.

2008-09-11 20:49 +0000 [r908]  Stevan_White:

	* ChangeLog: changes since June

2008-09-11 20:42 +0000 [r907]  Stevan_White:

	* FreeSerif.sfd: Removed pointless entries from
	  Latin kern table Tidied points in Sinhala

2008-09-07 21:47 +0000 [r906]  Stevan_White:

	* FreeSerif.sfd: Tidied up Tamil ligatures
	  EEA8-EEAB to fix TT build warning "MonotonicFindAlong: Never
	  found our spline."

2008-09-07 21:07 +0000 [r905]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  sfd/Makefile, FreeSans.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Added APL characters
	  to FreeMono (why?...) Fixed several last-minute problems,
	  including Serif: Tweeked GPOS mark table for Cyrillic Sans: Added
	  a GPOS table for Cyrillic (but several diacritics are missing)
	  Serif, Mono: tweeked some bugs in extensible brackets & integrals
	  Serif: Vietnamese o circumflex: accent was a bit high. fixed.
	  MonoBoldOblique OTF build uni213b intersects self Generation of
	  TT fonts complains about several things to stderr, including
	  SerifBold: "There exists a 'fpgm' code that seems incompatible
	  with FontForge's. Instructions generated will be of lower
	  quality. If legacy hinting is to be scrapped, it is suggested to
	  clear the `fpgm` and repeat autoinstructing. It will be then
	  possible to append user's code to FontForge's 'fpgm', but due to
	  possible future updates, it is extremely advised to use high
	  numbers for user's functions." (probably has been there since I
	  first copied the TT instructions in. ) Just repeated the copying
	  process carefully, and the warning went away. Serif:
	  "FindMatchingHVEdge fell into an impossible position" fixed a
	  bunch of point too close REMAINING PROBLEM in Serif TT build
	  "MonotonicFindAlong: Never found our spline." fixed several bad
	  TT matrices-- there are several more fixed many "control points
	  too close" no luck

2008-09-03 08:43 +0000 [r904]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansOblique.sfd: Added/corrected some
	  Misc. Symbols by copying from Serif. Note this is only a stopgap
	  solution. Want real sans-serif symbols.

2008-09-02 22:41 +0000 [r903]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Put in minimal
	  Miscellaneous Symbols: Card suites and some musical notes. Note
	  not very happy with shapes...some I just drew. Sans isn't really
	  sans. Fixed one APL symbol in Mono so it verified in OTF version

2008-08-31 11:39 +0000 [r902]  Stevan_White:

	* FreeMono.sfd: Built set of APL symbols.

2008-08-30 18:37 +0000 [r901]  Stevan_White:

	* FreeSans.sfd: Un-linked references in uni02B2
	  and uni02B5, because when validating the TrueType version,
	  fontforge gave an error "is drawn in wrong direction". (I suspect
	  a bug in fontforge. Other similar glyphs make no errors.)

2008-08-30 16:23 +0000 [r900]  Stevan_White:

	* FreeSerif.sfd: Fixed missing extrema in
	  TrueType. These were the last cases being reported by validate in
	  all the faces.

2008-08-30 13:46 +0000 [r899]  Stevan_White:

	* FreeSerifItalic.sfd: fixed last missing
	  extrema in TrueType

2008-08-30 11:02 +0000 [r898]  Stevan_White:

	* sfd/Makefile, Makefile,
	  tools/validate.py,
	  tools/isMonoMono.py: restructured validation to
	  look in a directory

2008-08-30 10:57 +0000 [r897]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeSerif.sfd: Fixed missing extrema in
	  TrueType versions

2008-08-30 09:31 +0000 [r894-896]  Stevan_White:

	* AUTHORS, CREDITS: unified style

	* README: lots of re-wording, concerning goals and
	  purposes

	* INSTALL: Got rid of outline style, tried to make a
	  little more appealing.

2008-08-14 22:01 +0000 [r893]  Stevan_White:

	* FreeSans.sfd: Same problem with uni0A83 as
	  with bn_llikaar. Just made zero-width.

2008-08-14 20:29 +0000 [r892]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansOblique.sfd: Glyph bn_llikaar, U+09E3
	  BENGALI VOWEL SIGN VOCALIC LL, has right bound positioned far
	  into the negative. Causes a warning in fontforge when opening OTF
	  version. Comparing with other fonts supporting Bengali, found no
	  others that do this. FreeSerif makes glyph width 0 (which sounds
	  right according to Unicode) and puts glyph wholly to left of 0.
	  However, I haven't found this letter in text anywhere. I wonder
	  if it is really used in writing.

2008-08-14 07:41 +0000 [r891]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeSerif.sfd: Further TrueType validation
	  fixes. Sans still has two glyps in wrong direction.

2008-08-13 23:09 +0000 [r890]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Fixed more TrueType
	  problems...all with missing extrema in TTF validation

2008-08-13 21:35 +0000 [r889]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: fixed all the TrueType
	  validation problems of type "intersects itself" and all but two
	  of the "wrong directions", as well as a lot of "missing extrema".
	  But there remain hundreds of missing extrema in the TrueType
	  version. Also, bn_llikaar in Sans and Oblique still has a problem
	  in OTF version.

2008-08-12 22:21 +0000 [r888]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Made .ttf files to
	  validate. Other faces have many more problems still.

2008-08-12 20:41 +0000 [r887]  Stevan_White:

	* tools/GenerateTrueType: For TrueType, one puts in
	  Instructions, not Hints. This vastly improves look of TrueType
	  fonts in Windows.

2008-08-11 23:07 +0000 [r886]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd,
	  FreeMonoOblique.sfd: Continuing to make OTF
	  versions validate. * FreeMonoOblique.sfd: lots of missing points
	  at extrema * FreeSerif.sfd 12 wrong directions, 1 missing extrema
	  * FreeSerifItalic.sfd many missing points at extrema, 1
	  self-intersecting What was wrong: in several oblique cases, an
	  already-italic glyph was made more italic, thereby fouling up
	  extrema (although why it passed validation in the SFD I don't
	  know). Some glyphs were overly-complicated with many near points.
	  Cleaned up, rounded to int. Remaining problem: OTF
	  FreeSansOblique FreeSans. one Bengali glyph in each whose advance
	  width and htmx don't match. Moral of story: validate the OTF and
	  TTF versions too before a release.

2008-08-11 22:59 +0000 [r885]  Stevan_White:

	* FreeSansOblique.sfd: Reverse a mistake from
	  last commit: somehow this file was converted to quadratic, or
	  something.

2008-08-10 22:25 +0000 [r884]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Found that SFD files
	  that validated produced OpenType files that don't. These
	  represent the easy fixes. Some were result of conversion to
	  quadratic; some shouldn't have validated in the SFD... *
	  MonoBoldOblique: uni0250 missing pts at extrema [reference glyph
	  rotated...] * Sans: uni0AC4 wrong direction [simplified, rounded
	  to int] * SansOblique: uni01EA wrong direction [rounded to int] *
	  SerifBold: uni023f wrong direction [round to int] *
	  SerifBoldItalic: uni0245 missing pts at extrema [ungrouped ref,
	  added extrema]

2008-08-06 18:08 +0000 [r883]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd: Re-set font metrics,
	  which were somehow making uneven vertical spacing.

2008-08-06 07:05 +0000 [r882]  Stevan_White:

	* ChangeLog: Added changes since last release

2008-06-22 09:14 +0000 [r881]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: Made to validate

2008-06-22 09:08 +0000 [r879-880]  Stevan_White:

	* tools/ranges.py: Brought more into line with
	  OpenType added some ranges fixed bug with ranges outside of font

	* CREDITS: 3 new ranges

2008-06-22 07:53 +0000 [r878]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: Cyrillic: tweeked accents for
	  consistency, and for readability in small sizes.

2008-06-21 23:31 +0000 [r877]  Stevan_White:

	* FreeSerif.sfd: Thanna range: tweeking

2008-06-21 23:15 +0000 [r876]  Stevan_White:

	* FreeSerif.sfd: Thaana range: Scaled up by
	  about 15%, raised by 100EM, tightened some of the diacritics to
	  get inside 900 to -300 EM limits.

2008-06-21 22:21 +0000 [r875]  Stevan_White:

	* FreeSans.sfd: Added Old Persian and Ugaritic
	  from MPH2BDamase font.

2008-06-21 21:50 +0000 [r874]  Stevan_White:

	* FreeSerif.sfd: Added Tai Le range adapted from
	  MPH2BDamase font.

2008-06-21 18:39 +0000 [r873]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Added some ancient Greek
	  numerals from Tempora to high Unicode area, (partly just to show
	  it can now be done.)

2008-06-21 15:51 +0000 [r871-872]  Stevan_White:

	* FreeSerifItalic.sfd: Couple of tweeks putting
	  glyphs above -300EM.

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: Surgery to Thai letter 'tho
	  than', u+0e10, to push it above -300 EM. This makes Thai range
	  completely between 900 and -300 EM.

2008-06-21 11:20 +0000 [r870]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeSerif.sfd: Many auxilary characters (esp.
	  for Malayalam, Bengla, and Tamil) representing ligatures and
	  alternative forms without their own Unicode, were moved from
	  ranges above 0xFFFF (which ought to have been slots for other
	  defined Unicode ranges) into the Private Use area. In Serif, I
	  segregated the scripts, in Sans it was hard to see where one
	  began and another ended, so I moved them en masse. Note several
	  problems with wrongly-named characters: I already re-named
	  glyph570 and glyph582. But there are others with names starting
	  with A...

2008-06-21 11:08 +0000 [r869]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansOblique.sfd: Fixed (I hope the last)
	  problem with scripts in lookups Find Problems -> ATT (all
	  selected) finds multiple issues, SansBold In addition to script
	  'guru', added 'gur2' to the scripts for these lookups 'nukt'
	  Nukta forms in Gurmukhi 'blwf' Below Base Forms in Gurmukhi
	  'pstf' Post Base Forms in Gurmukhi 'blws' Below Base
	  Substitutions in Gurmukhi 'abvs' Above Base Substitutions in
	  Gurmukhi 'psts' Post Base Substitutions in Gurmukhi SansOblique
	  In addition to script 'beng', added 'bng2' to the scripts for the
	  lookup 'half' Half Forms in Bengali Moreover, the lookup 'aalt'
	  Access All Alternates in Latin contains only Bengali letters.
	  Re-named as Bengali, made to work on beng, bng2 scripts

2008-06-20 21:52 +0000 [r868]  Stevan_White:

	* FreeSerif.sfd: Scaled Sinhala range. Remedies
	  bug #23656: Sinhala letters over-sized

2008-06-20 18:03 +0000 [r867]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Mostly messing with
	  Greek Extended accents again. re-positioned ypogegrammani on
	  advice of Alexey Kryukov Put prosgegrammani beneath main letters
	  in Mono, to make narrower glyphs Implemented more distinction
	  between tonos and acute.

2008-06-19 08:25 +0000 [r866]  Stevan_White:

	* FreeMonoBoldOblique.sfd: Completed fit of Mono
	  to 800 to -200 EM.

2008-06-19 07:47 +0000 [r865]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd: Set Metrics to
	  recommended values

2008-06-19 07:40 +0000 [r864]  Stevan_White:

	* FreeMonoOblique.sfd: Now Mono Oblique, as well
	  as roman and Bold, are within 800 to -200 EM. Just BoldOblique to
	  go.

2008-06-19 07:25 +0000 [r863]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd: More toward fitting to 800
	  to -200 EM. Basically, reduced Georgian by 92%. Also made an
	  over-all offset, so Georgian is somehow centered (Bold...I guess
	  I already did this in roman). Want to also do an emboldening to
	  make stroke like rest of font, but current FontForge has a nasty
	  crash that loses data on this function.

2008-06-19 06:19 +0000 [r862]  Stevan_White:

	* FreeMono.sfd: In effort to make fit in 800 to
	  -200 EM, Scaled Georgian by 92%, centered on 600 wide box. Next:
	  Embolden a bit.

2008-06-18 19:46 +0000 [r861]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Toward making all
	  glyphs lie between -200 and 800 EM. Numerous small changes,
	  especially raising descenders of some Hebrew letters. Georgian
	  remains a problem

2008-06-13 07:39 +0000 [r860]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Moved prosgegrammeni up to
	  baseline, (and then moved all references down to baseline)

2008-06-13 06:17 +0000 [r859]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Added Control Picture
	  "blank" to all faces. Switched U+0222-3 from TemporaLGCUni

2008-06-11 17:57 +0000 [r858]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: More fiddling with Greek
	  Extended accents

2008-06-11 07:43 +0000 [r857]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Tweeks to accents etc in
	  Greek Extended and Cyrillic

2008-06-10 08:00 +0000 [r856]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBold.sfd: Fixed a few big horizontal
	  spacing problems

2008-06-09 23:12 +0000 [r855]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Implemented TemporaLCGUni
	  glyphs in Cyrillic ranges. Added a breve_cyrillic for the
	  moustache breve mark.

2008-06-08 08:45 +0000 [r854]  Stevan_White:

	* FreeSerif.sfd: Replaced most of Cyrillic range
	  with TemporaLGCUni. Remodelled many of the derived Cyrillic
	  characters after these. Fiddled globally with spacing of small
	  letters. Unclear on diacritics 485-6, unhappy with breve.

2008-06-07 23:12 +0000 [r853]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Started implementing
	  TemporaLCGUni in Greek ranges. Replaced 3DC-3E1 from Tempora,
	  because I thought they looked nicer and more like the other
	  existing FreeFont glyphs. Replaced 3DA-B from Tempora, because
	  they look more like Unicode samples, and nicer. Added 03f3-4,
	  03F7-F. Prefer my own lunate epsilon. Replaced Phi and Omega from
	  Tempora. These plainly fit the other FreeFont glyphs better than
	  the origninals. (How did this happen?) In bold, replaced U+03D7
	  Copied lbbar u+2114 Small italic greek: replaced most except phi,
	  psi, omega Based on new information, broke the identification of
	  oxia with Latin acute.

2008-06-07 13:15 +0000 [r852]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Efforts to control
	  heights of characters

2008-06-05 22:04 +0000 [r851]  Stevan_White:

	* FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Made to validate

2008-06-04 23:14 +0000 [r850]  Stevan_White:

	* FreeSans.sfd: Fixed undefined character in
	  kerning classes

2008-06-03 22:26 +0000 [r849]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: tweeks and additions
	  to General Punctuation

2008-06-03 07:12 +0000 [r848]  Stevan_White:

	* FreeSansOblique.sfd,
	  FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Completed/tweeked
	  Number Forms

2008-06-02 22:28 +0000 [r847]  Stevan_White:

	* FreeMono.sfd,
	  FreeSerif.sfd: Added some Miscellaneous
	  Technical symbols

2008-06-02 21:25 +0000 [r846]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Tweeks...mostly Letterlike

2008-06-01 21:52 +0000 [r845]  Stevan_White:

	* FreeMono.sfd,
	  FreeSerif.sfd: Added Box Drawing characters to
	  Serif. Tweeked a glyph in Mono

2008-06-01 12:30 +0000 [r844]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Added several glyphs
	  to Letterlike Characters

2008-05-31 07:30 +0000 [r843]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoBoldOblique.sfd: Validation pass
	  SansOblique and SansBoldOblique had validation problem with
	  BlueValues Private Dictionary Elements in BlueValues/OtherBlues
	  array are disordered Elements in BlueValues/OtherBlues array are
	  too close (Change BlueFuzz) StemSnapV does not contain StdVW
	  value. So I ordered the array, and based on other slanted fonts,
	  removed StemSnapV. Note however, I still think the two top Blues
	  lines are too close But I don't even know what the second-to-top
	  line is meant to do.

2008-05-31 07:23 +0000 [r842]  Stevan_White:

	* FreeSerif.sfd: Added to Block Elements,
	  Geometric Shapes Made to validate

2008-05-29 08:49 +0000 [r841]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Looking at special symbols.
	  Drew several Miscellaneous Symbols in Mono and Serif
	  (Completed/corrected planetary symbols, added Dice, some other
	  easy ones) Completed Dingbats in Serif (using URW Dingbats) Added
	  some Block Elements to Serif

2008-05-25 23:47 +0000 [r840]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: More changes stemming from
	  J. Poon's report.

2008-05-25 12:56 +0000 [r839]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd: Height surgery on
	  SerifBoldItalic. More fiddling with accents in others.

2008-05-25 11:01 +0000 [r838]  Stevan_White:

	* FreeSerifItalic.sfd: More height surgery. Only
	  a few left in Benglai and Thai

2008-05-25 10:54 +0000 [r837]  Stevan_White:

	* FreeSerifBold.sfd: Re-applied surgery to make
	  glyphs between 900 and -300EM

2008-05-25 09:58 +0000 [r836]  Stevan_White:

	* FreeSerifBold.sfd: *** Regression
	  Inadvertently un-linked all references in SerifBold in r1.83.
	  This reverses that error (but also un-does the surgery mentioned
	  there)

2008-05-24 22:23 +0000 [r835]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBold.sfd: Applied surgery to make
	  Latin letters go under 900EM. One exception yet...

2008-05-24 20:59 +0000 [r834]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Serif: much fiddling with
	  accents in Latin ranges. Re-thought some glyphs (there are still
	  a few messy ones, especially in bold) Checked horizontal
	  spacing...fixed a number of problems.

2008-05-23 07:37 +0000 [r833]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSansBold.sfd,
	  FreeSerifBold.sfd: Made Latin Extended-B
	  coverage consistent across Serif; cleaned up some glyphs

2008-05-22 23:07 +0000 [r832]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd: Filled more of General
	  Punctuation in Sans and Serif Made all agree on coverage of Latin
	  Extended Additional

2008-05-22 21:34 +0000 [r831]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSerifBold.sfd: Added Latin Extended
	  Additional to SansOblique. Made Latin Extended Additional
	  coverage consistent across Sans, B, I Made Latin Extended-B
	  coverage same in SerifBold.

2008-05-22 19:05 +0000 [r830]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoBoldOblique.sfd: Mono* made Latin-B
	  coverage consistent across faces

2008-05-22 07:50 +0000 [r829]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Made set of Latin
	  Extended-B consistent across Sans faces

2008-05-21 23:14 +0000 [r828]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd: More filling in General
	  Punctuation

2008-05-21 22:26 +0000 [r827]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: another bunch of J. Poon's
	  reports also, filling in some Combining Diacriticals, Spacing
	  Modifiers, and General Punctuation in bold faces

2008-05-20 23:16 +0000 [r826]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd, FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeSerifBold.sfd: Mucking about with mark
	  tables in Thai (Serif) Other faces: Making changes from J. Poon's
	  report

2008-05-20 06:41 +0000 [r825]  Stevan_White:

	* CREDITS: Mark Williamson Jacob Poon

2008-05-20 06:37 +0000 [r823-824]  Stevan_White:

	* Makefile: added tests target

	* tools/findBackLayers.py (added),
	  tools/validate.py (added),
	  tools/isMonoMono.py (added),
	  tools/nameBySlot.py (added): isMonoMono.py checks
	  that monospace is really that findBackLayers.py finds glyphs in
	  back layers nameBySlot.py Sets name/unicode values of a range to
	  encoding value verify.py runs FontForge "verify" on all faces

2008-05-18 19:17 +0000 [r822]  Stevan_White:

	* tools/ranges.py: put table explanation back in

2008-05-18 19:11 +0000 [r821]  Stevan_White:

	* tools/ranges.py: Improved behaviour for high
	  Unicode

2008-05-18 13:32 +0000 [r820]  Stevan_White:

	* FreeSans.sfd: Revision of kerning

2008-05-18 12:16 +0000 [r819]  Stevan_White:

	* FreeSerif.sfd: Made Latin kerning a little
	  more reasonable: reduced many excessive kerns (some had letters
	  apparently overlapping, which shouldn't happen) made kerns
	  increment by 5EM for ease of reading got rid of kerns too small
	  to be seen

2008-05-18 12:11 +0000 [r818]  Stevan_White:

	* FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: made to verify

2008-05-12 22:52 +0000 [r817]  Stevan_White:

	* FreeSerif.sfd: made to validate

2008-05-12 22:46 +0000 [r816]  Stevan_White:

	* FreeSerif.sfd: Gurmukhi: filled range in
	  Serif, taking glyphs from the original Punjabi font by Hardip
	  Singh Pannu http://members.aol.com/hspannu/punjabi.html (file
	  pb_win95.exe)

2008-05-12 19:43 +0000 [r815]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSerifBold.sfd: Hebrew, basic. Some faces
	  missing punctuation marks, added.

2008-05-12 19:03 +0000 [r814]  Stevan_White:

	* FreeSans.sfd, FreeMono.sfd:
	  Armenian: Sans tried to make verticals and horizontals of more
	  uniform width both, finddled with punctuation

2008-05-12 12:56 +0000 [r813]  Stevan_White:

	* FreeMonoOblique.sfd: made to validate

2008-05-12 12:23 +0000 [r812]  Stevan_White:

	* FreeMonoBold.sfd: made to validate

2008-05-12 12:04 +0000 [r811]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd: Armenian in Sans:
	  regularized letter spacing

2008-05-12 10:19 +0000 [r810]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeMonoOblique.sfd: Armenian: fill out ranges
	  and clean up SansBold especially had a lot of incorrect
	  references. Now all the ranges with Armenian at least share the
	  same set of characters.

2008-05-12 07:58 +0000 [r809]  Stevan_White:

	* FreeMono.sfd: Fixed glyph with wrong width.

2008-05-11 21:29 +0000 [r808]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: 1) made to validate
	  2) Mono: copied in Spacing Modifier Letters (glyphs not yet
	  named) 3) SerifItalic: Filled in General Punctuation

2008-05-11 17:56 +0000 [r807]  Stevan_White:

	* FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Made to validate, and
	  pass all other FontForge tests. Expedient: rounded everything to
	  int

2008-05-11 14:00 +0000 [r806]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Made to have the same Greek
	  Symbols, Made to validate

2008-05-11 12:59 +0000 [r805]  Stevan_White:

	* FreeSansOblique.sfd: Made Greek Symbols as
	  full as rest of Sans

2008-05-11 12:40 +0000 [r804]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd: Made Greek Symbols as full
	  as rest of Sans. Changed a name in Sans.

2008-05-11 12:25 +0000 [r803]  Stevan_White:

	* FreeSansBold.sfd: Deleted orphaned Arabic
	  glyph from Arabic Presentation forms-B

2008-05-11 12:17 +0000 [r802]  Stevan_White:

	* FreeMonoOblique.sfd: Made Greek as full as
	  other faces Made to validate

2008-05-11 11:08 +0000 [r801]  Stevan_White:

	* FreeSansBold.sfd: Deleted seven orphaned
	  Arabic characters; looks like somebody started, didn't get very
	  far, putting Arabic in bold.

2008-05-11 11:05 +0000 [r800]  Stevan_White:

	* FreeSerifBold.sfd: Deleted the single Arabic
	  character: it was clearly there by mistake.

2008-05-11 11:01 +0000 [r799]  Stevan_White:

	* FreeSansOblique.sfd: Tweeks to Armenian

2008-05-11 10:56 +0000 [r796-798]  Stevan_White:

	* FreeSansOblique.sfd: Comment from previous
	  commit of FreeSans was meant for FreeSansOblique. In FreeSans,
	  only tweeked a few letters during putting more characters in this
	  face. Filled in Spacing Modifier Letters, increased General
	  Punctuation.

	* FreeSans.sfd: Filled in Spacing Modifier
	  Letters, increased General Punctuation

	* FreeMono.sfd: Made Armenian as full as other
	  roman faces.

2008-05-11 08:22 +0000 [r795]  Stevan_White:

	* FreeMono.sfd: Completed Spacing Modifier
	  Letters Added a couple of Greek Punctuation

2008-05-10 22:39 +0000 [r794]  Stevan_White:

	* FreeMono.sfd: added more Spacing Modifier
	  Letters

2008-05-10 21:57 +0000 [r792-793]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: Did same process of scaling and
	  sizing for Thai in Sans as in Serif. Added mark tables to Sans.
	  Improvement, but there are questions...

	* FreeSans.sfd: Tidied some Gurmukhi glyphs,
	  validated.

2008-05-10 17:50 +0000 [r791]  Stevan_White:

	* FreeMonoBoldOblique.sfd: Made metrics like
	  rest of Mono

2008-05-10 13:44 +0000 [r790]  Stevan_White:

	* FreeSans.sfd: Deleted ranges for Oriya,
	  Kannada, on account of 1) they only contained a subset of the
	  consonant glyphs of the scripts, few if any vowels, and had no
	  ligature lookups as required 2) Kannada was based on the Akurti
	  fonts, which have copyright issues. See bug #23225: Oriya range
	  only partial bug #23224: Kannada range only partial

2008-05-09 07:49 +0000 [r789]  Stevan_White:

	* tools/ranges.py: more info on range intervals

2008-05-09 05:36 +0000 [r788]  Stevan_White:

	* FreeSerif.sfd: Deleted Telugu range. It didn't
	  represent a complete writing system for the language. See notes
	  at https://savannah.gnu.org/bugs/index.php?23202 Serif: Telugu
	  range missing many characters; many wrong

2008-05-09 05:22 +0000 [r787]  Stevan_White:

	* FreeSerif.sfd: Got a copy of the original
	  Tikkana font, Copied in remaining consonants and vowels that I
	  could find there. I think one vowel 0C55 is missing according to
	  unicode). Strangely, the Telugu digits are alo missing. In
	  Tikkana, the default "checkmark" structural mark is missing from
	  many consonants, according to Unicode, but is a separate glyph. I
	  put the checkmark on. This, and scaled up by 150% and cleaned up
	  intersecting glyphs and many unnecessary points.

2008-05-07 22:38 +0000 [r786]  Stevan_White:

	* FreeSerif.sfd: Filled out Telugu consonants.
	  Vowels still need to be done

2008-05-07 19:35 +0000 [r785]  Stevan_White:

	* FreeSerif.sfd: Operated on Latin glyphs with
	  stacked accents to make them fit under 900EM. Scaled Telugu bu
	  150%.

2008-05-05 22:17 +0000 [r784]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd: Corrected further fontforge
	  "find problems" added some math characters to FreeSerif

2008-05-04 23:52 +0000 [r783]  Stevan_White:

	* FreeSansBold.sfd: Made to validate, and fixed
	  bad TT transformations

2008-05-04 19:52 +0000 [r782]  Stevan_White:

	* FreeMono.sfd,
	  FreeSerif.sfd: Mainly TeX additions trying to
	  satisfy Markus Kuhn's TeX-as-Unicode page

2008-05-04 14:25 +0000 [r781]  Stevan_White:

	* FreeMono.sfd: Adjusted heights of extensible
	  brackets

2008-05-04 13:42 +0000 [r780]  Stevan_White:

	* FreeMono.sfd: Fixed problems with extensible
	  brackets, thanks to Markus Kuhn's page
	  http://www.cl.cam.ac.uk/~mgk25/ucs/examples/UTF-8-demo.txt

2008-05-04 11:43 +0000 [r779]  Stevan_White:

	* tools/ranges.py: fiddled with ranges, doc

2008-05-04 11:25 +0000 [r778]  Stevan_White:

	* tools/ranges.py: made some ranges more correct?

2008-05-04 11:02 +0000 [r777]  Stevan_White:

	* tools/ranges.py: fixed some bugs in ranges better
	  error reporting

2008-05-04 10:34 +0000 [r775-776]  Stevan_White:

	* tools/ranges.py: Got rid of Unicode 1.1 references

	* tools/ranges.py: made to use OpenType table

2008-05-04 03:11 +0000 [r774]  Stevan_White:

	* FreeSans.sfd, FreeMono.sfd,
	  FreeSerif.sfd: made to validate

2008-05-03 20:44 +0000 [r773]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Removed digits from Private
	  Use Area. See bug 23050.

2008-05-03 19:54 +0000 [r772]  Stevan_White:

	* tools/ranges.py: restructure text output

2008-05-03 17:05 +0000 [r771]  Stevan_White:

	* FreeSans.sfd, FreeMono.sfd:
	  Completed General Punctuation

2008-05-03 15:00 +0000 [r770]  Stevan_White:

	* tools/ranges.py: More docs, date

2008-05-03 14:53 +0000 [r769]  Stevan_White:

	* FreeSans.sfd: Completed IPA Extensions

2008-05-03 13:46 +0000 [r768]  Stevan_White:

	* FreeSans.sfd, FreeMono.sfd,
	  FreeSerif.sfd: More work on Superscripts and
	  Subscripts, Spacing Modifiers. Sans is now complete in both.
	  Added Pfennig to Sans and Mono.

2008-05-03 11:55 +0000 [r767]  Stevan_White:

	* tools/ranges.py: rearrangement and cosmetic

2008-05-03 11:46 +0000 [r765-766]  Stevan_White:

	* tools/ranges.py: ...except I had broken it. now
	  fixed

	* tools/ranges.py: Seems to be in a useful form at
	  this point.

2008-05-03 10:29 +0000 [r764]  Stevan_White:

	* FreeSerif.sfd: Added a hand-drawn old German
	  Pfennig to Currency Symbols

2008-05-02 23:46 +0000 [r763]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd, FreeMono.sfd,
	  FreeSerif.sfd: Further additions to General
	  Punctuation, Super and Sub Scripts, Spacing Modifiers, etc.

2008-05-02 20:29 +0000 [r762]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd: Sans: additions to Spacing
	  Modifiers, IPA SerifItalic: shortening stacked accents to
	  maintain readability when clipped

2008-05-01 19:32 +0000 [r761]  Stevan_White:

	* FreeSans.sfd: Additions to Spacing Modifiers
	  and changes to Combining Diacritics

2008-05-01 18:26 +0000 [r760]  Stevan_White:

	* FreeSerif.sfd: Made sure all the half rings in
	  Combining Diacriticals and Spacing Modifiers were really half
	  rings (J. Poon had complained about this)

2008-05-01 18:11 +0000 [r759]  Stevan_White:

	* FreeSerif.sfd: Filled out General Punctuation
	  Some work on Spacing Modifiers

2008-05-01 14:10 +0000 [r758]  Stevan_White:

	* FreeSerif.sfd: Filled out Mathematical
	  Operators still needs lots of work Made to validate

2008-05-01 09:14 +0000 [r757]  Stevan_White:

	* FreeSerif.sfd: Filled out Latin Extended B

2008-04-30 23:00 +0000 [r756]  Stevan_White:

	* FreeSerif.sfd: added some letters with curls
	  to Latin Extended B

2008-04-30 22:38 +0000 [r755]  Stevan_White:

	* FreeSerif.sfd: more fiddling with Latin
	  Extended B accents

2008-04-30 20:28 +0000 [r754]  Stevan_White:

	* FreeSerif.sfd: Added HanunÃ³o script, with
	  characters based on those in font MPH2BDamase, on request from
	  the maintainer of that font,
	  http://packages.debian.org/sid/ttf-mph-2b-damase Glyphs are
	  simple vector strokes. Could be a little more uniform.

2008-04-29 23:54 +0000 [r753]  Stevan_White:

	* FreeSerif.sfd: Added Buginese script
	  "Lontara", with characters based on those in font MPH2BDamase, on
	  request from the maintainer of that font,
	  http://packages.debian.org/sid/ttf-mph-2b-damase Note the glyphs
	  are pretty rough, clearly a digitization of handwriting. I just
	  cleaned them up, and corrected discrepancies with Unicode, and
	  compared with some pictorial samples of the script I could find.

2008-04-28 22:15 +0000 [r752]  Stevan_White:

	* tools/ranges.py: improved look a lot still unhappy
	  with some ranges OS/2 seems sometimes bang-on, sometimes
	  unrelated to anything ( including fontforge's OS/2 listing)

2008-04-28 22:00 +0000 [r751]  Stevan_White:

	* FreeSerif.sfd: Much fiddling with Tamil range.
	  First scaled to 78% (avoiding the references) This gets it in the
	  ballpark height-wise. [A bit taller than the Latin letters, but
	  the stroke is narrower, but then the glyphs are busier.] Then had
	  to re-align combined references, the trickiest being the halants.
	  Checked with other fonts with Tamil text.

2008-04-28 20:05 +0000 [r750]  Stevan_White:

	* FreeSerif.sfd: Cleanum of missing extrema in
	  Arabic and Thaana

2008-04-28 19:55 +0000 [r749]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: cleanup of control points in
	  Arabic and Thaana

2008-04-28 17:04 +0000 [r748]  Stevan_White:

	* tools/ranges.py (added): a script to print out how
	  many characters in a range are implemented in each font

2008-04-28 08:06 +0000 [r747]  Stevan_White:

	* FreeSerif.sfd: * Many changes to Thai, trying
	  to make the script fit between some lines, so accents won't get
	  clipped, etc. Also, stroke weight was heavier than that of Latin.
	  Scaled whole thing by 93%. Shrank the tallest letters 0E42-4 to
	  get them under 900EM. Shaved off top of maiek. Fiddled with
	  positioning of all accents. Made positioning tables for accents.
	  [note, unclear these are working correctly] * Fixed a bug having
	  to do with character replacements for characters named 'ng' and
	  'nj'; these names had been taken on by other characters. * Made
	  to validate * unicode positions of two Cyrillic Extended
	  characters were switched. * fiddled with a couple of Cyrillic
	  combining diacritics

2008-04-26 22:14 +0000 [r746]  Stevan_White:

	* FreeSans.sfd: bugfix: a left harpoon
	  mysteriously appeared to the left of letter p!

2008-04-26 19:46 +0000 [r745]  Stevan_White:

	* FreeMono.sfd: Made to validate

2008-04-26 18:40 +0000 [r744]  Stevan_White:

	* FreeSans.sfd: made to validate

2008-04-26 15:04 +0000 [r743]  Stevan_White:

	* FreeSans.sfd: Toward J. Poons report Made 032B
	  more like proper double-arches (and distinct fro 033C seagull)

2008-04-26 14:22 +0000 [r742]  Stevan_White:

	* FreeSans.sfd: Toward J. Poon's report Made
	  032b more like a seagull

2008-04-26 14:05 +0000 [r741]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd: Sans: fiddling with widths
	  and terminators of math symbols, toward J. Poon's report R & B:
	  removed u+2741 because it didn't match the Unicode description

2008-04-26 12:08 +0000 [r740]  Stevan_White:

	* FreeMono.sfd: extensible parenthesis symbols
	  weight/terminators Toward bug # 23064
	  https://savannah.gnu.org/bugs/index.php?23064 Rounded a bunch of
	  terminators

2008-04-22 21:45 +0000 [r739]  Stevan_White:

	* FreeSans.sfd: Fiddled with math--consequences
	  of changing the "similar" operator

2008-04-22 20:06 +0000 [r738]  Stevan_White:

	* FreeSerif.sfd: Small alignment problem in
	  Greek Extended

2008-04-22 19:52 +0000 [r737]  Stevan_White:

	* FreeSerif.sfd: One more tweek to spacing in
	  Cyrillic Extended

2008-04-22 19:48 +0000 [r736]  Stevan_White:

	* FreeSerif.sfd: Corrected spacing in Cyrillic
	  Supplement

2008-04-22 19:35 +0000 [r735]  Stevan_White:

	* FreeSerif.sfd: Added Cyrillic Supplement
	  letters for Enets, Khanty, Chukchi, Itelmen, Mordvin, Kurdish,
	  Aleut

2008-04-22 18:46 +0000 [r734]  Stevan_White:

	* FreeSans.sfd: More tightening of accents

2008-04-22 18:41 +0000 [r733]  Stevan_White:

	* FreeSerif.sfd: Added Cyrillic letters for
	  Nivkh (completing Cyrillic range) More tightening of accents in
	  Latin Extended.

2008-04-22 08:49 +0000 [r732]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Adjustments to h and k with
	  caron and cedilla in Latin A and B

2008-04-21 22:23 +0000 [r731]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: FreeSerifBold: deleted 3
	  dotted Hebrew letters in Private/Corporate use (E801-3). They
	  weren't ligatures or in any other lookup, and they weren't
	  present in FreeSerif. FreeSansBold: unlinked and deleted F6C3,
	  which called itself commaaccent. Made some new spacing and
	  non-spacing accents to make up for it. FreeSansBoldOblique: Made
	  references of many Latin Extended. Also corrected several wrong
	  ones. Freeserif: re-named commaaccent

2008-04-21 18:30 +0000 [r730]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Deleted Hiragana and
	  Katakana ranges, as discussed on bugs list. Cleaned up some
	  encoding issues, unnamed glyphs

2008-04-21 07:28 +0000 [r728-729]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Roman: added 'sine' -- not
	  beautiful, but I liked drawing it All: Made special lookup for
	  Dutch ligatures 'IJ' and 'ij'

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Roman: ffi etc Latin
	  ligatures from 'liga' to 'dlig' (these weren't really ligatures
	  anyway, and only looked very bad when used. Retain for condensed
	  type. Others: deleted Latin 'liga' table altogether BoldOblique :
	  added j to ij ligature

2008-04-20 22:31 +0000 [r727]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Toward J. Poon's
	  Report: Except for issues of terminators not always vertical or
	  horizontal, and a few things that were too hard or I was unsure
	  of.

2008-04-20 15:31 +0000 [r726]  Stevan_White:

	* FreeSerif.sfd: More fiddling with accents

2008-04-20 15:11 +0000 [r725]  Stevan_White:

	* FreeSerif.sfd: futzing with accents in Latin
	  Extended Additional

2008-04-20 14:28 +0000 [r724]  Stevan_White:

	* FreeSerif.sfd: Fiddled with accents in Latin
	  Extended-B

2008-04-20 12:56 +0000 [r723]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: added primemod
	  character, referenced by Greek number sign

2008-04-20 09:51 +0000 [r722]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoOblique.sfd: Following J. Poon's
	  report, disconnected NJ (01CA)

2008-04-19 21:20 +0000 [r721]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: First pass throught
	  J. Poon's bug list. See bug reports for details.

2008-04-19 15:44 +0000 [r720]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Made underscore
	  slanted in Oblique faces, made all to be width of character.
	  Towards J. Poon's report. Disturbed that xterm and some other
	  apps put small space between characters when none was called for.

2008-04-19 15:20 +0000 [r719]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeMono.sfd,
	  FreeMonoBold.sfd: corrections on Currency
	  Symbols

2008-04-19 11:59 +0000 [r718]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd, FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: More corrections, additions
	  to Currency Symbols

2008-04-19 01:41 +0000 [r717]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Filled out and corrected
	  Currency Symbols

2008-04-18 21:46 +0000 [r716]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Adjustments to Combining Marks
	  for Symbols Additions to range in Sans, and re-structured its
	  marks table so that "middle" can apply to any range

2008-04-18 08:31 +0000 [r715]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: Made reference between
	  combining koronis and lenis of Greek Extended In Serif, re-worked
	  combining marks lookup tables, added anchors in Latin, moved so
	  without marks they work in kedit (but now I'm doubting kedit does
	  a reasonable thing...what is a better application for testing
	  this?)

2008-04-16 21:46 +0000 [r714]  Stevan_White:

	* FreeSerifItalic.sfd: adjusting of spacing and
	  accents in Greek

2008-04-16 20:30 +0000 [r713]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoOblique.sfd: Much futzing with Greek
	  letter spacing and accents. Added lenis to FreeMono.

2008-04-16 06:45 +0000 [r712]  Stevan_White:

	* FreeMono.sfd,
	  FreeSerif.sfd: Adjusted spacing of dots of
	  Greek dieresistonons in Serif Whippted up something for Greek
	  kappascript in Mono (could use revision)

2008-04-16 06:18 +0000 [r711]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Raised dots on
	  double-dotted Cyrillic i, to match that of i and j.

2008-04-14 09:02 +0000 [r710]  Stevan_White:

	* FreeMono.sfd: Corrected 27e6-7 "white bracket"
	  Note it is probably a FontForge bug these symbols aren't showing
	  up. FontForge thinks they are in Supplemental Arrows, but they
	  should be in Supplemental Math-A

2008-04-13 22:18 +0000 [r709]  Stevan_White:

	* FreeMono.sfd: named some Greek characters

2008-04-13 22:00 +0000 [r708]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Spacing of some Cyrillic
	  characters

2008-04-13 21:10 +0000 [r707]  Stevan_White:

	* FreeSerif.sfd: some fiddling with accents,
	  yogh was too wide

2008-04-13 14:28 +0000 [r706]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansOblique.sfd: Character spacing was
	  chaos--tried to improve. BoldOblique also needs it.

2008-04-13 12:17 +0000 [r705]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Completed the fix of
	  bug #12798, Greek glyphs with accents to side Much mucking with
	  accents here, and fixed a few things that were just wrong.

2008-04-12 19:56 +0000 [r704]  Stevan_White:

	* FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Made Mono curly
	  quotes "bent"

2008-04-12 19:39 +0000 [r703]  Stevan_White:

	* FreeMono.sfd: More fiddling with Greek accents
	  Made quotes "bent"

2008-04-12 18:57 +0000 [r702]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Adjustments on Greek
	  diaresistonos etc. Adjustments in Serif on combining marks for
	  symbols

2008-04-12 11:58 +0000 [r701]  Stevan_White:

	* FreeSerif.sfd: More additions to Combining
	  marks for Symbols

2008-04-12 11:44 +0000 [r700]  Stevan_White:

	* FreeSerif.sfd: Additions to Combining marks
	  for Symbols -- now mostly full. Lots of adjustments to middle
	  anchor point in Latin to make big circle (nearly) encircle
	  preceding latter

2008-04-11 07:14 +0000 [r699]  Stevan_White:

	* FreeMono.sfd: Bugfix: Had indroduce a glyph of
	  width other than 600, making kterminal not recognize it as a
	  monospace font.

2008-04-10 19:44 +0000 [r698]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: More messing with accents.
	  Further to bug #12798, Greek glyphs with accents to side Much
	  messing with glyphs in Greek Extended range

2008-04-09 11:35 +0000 [r697]  Stevan_White:

	* FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Revisited Latin-1 and
	  Latin-A accents. Glyph B7 was called "periodcentered", but
	  Unicode callse it Mid Dot, and the description doesn't refer to
	  the period. I made it like the dot accent. throughout, and
	  referred L-dot to it. Also double-checked "commaaccent"
	  characters (some in Unicode called cedilla, but the Unicode
	  example shows a comma...mystery) also the funny IPA upside-down f
	  often had two bars, incorrectly. To do: go through rest of Serif,
	  and Sans

2008-04-08 23:25 +0000 [r696]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Completed
	  re-structuring of stacked Latin accents in Mono. Also: lots of
	  associated adjustments of Greek Extended accents. (Trying to at
	  least center extremely wide characters on their box) Repaired
	  some victems of "find overlaps" sweeps Worked on glyphs with
	  apostrope/comma parts Corrected a few wrong glyphs. Trying out a
	  "bent quotes" solution to making primes distinct from quotes.

2008-04-08 09:56 +0000 [r695]  Stevan_White:

	* FreeMonoOblique.sfd: Toward reducing overall
	  height Did similar process as for Mono, fixing a few errors along
	  the way. Also the Greek Extended range was very messed up
	  vertical and horizontally. Horizonal spacing of the heavily
	  accented Greek is a real problem in Mono... To do: revisit
	  "commaaccent" characters in all faces: do some have cedillas?
	  some Hebrew glyphs are a little low Georgian generally is way out
	  of bounds

2008-04-07 07:18 +0000 [r694]  Stevan_White:

	* INSTALL: various updates and corrections, tweeked
	  formatting

2008-04-06 22:48 +0000 [r693]  Stevan_White:

	* FreeMonoBold.sfd: Tweeking of accents

2008-04-06 21:21 +0000 [r692]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd: Re-worked accents in
	  FreeMonoBold.sfd to make Latin ranges lie between 800 and -200
	  EM, as with FreeMono.

2008-04-06 16:20 +0000 [r691]  Stevan_White:

	* tools/hex_range.py (added): a tool for printing
	  out ranges of formatted hex numbers

2008-04-06 12:33 +0000 [r690]  Stevan_White:

	* FreeMono.sfd: Latin Extended ranges:
	  Implemented new policy of shortening the letters of the
	  characters with the highest-stacked accents. At this point all
	  the Latin glyphs lie betweeen 800 and -200 EM. Also checked for
	  readability of all the Latin extended letters in xterm. (Issue:
	  it chops letters outside their bounding boxes; many accents had
	  been a bit outside. Made sure that if they were chopped, they
	  were at least still recognizable.)

2008-04-05 19:41 +0000 [r689]  Stevan_White:

	* FreeMono.sfd: Following exchange about Mono on
	  freefont-bugs with Joe Wells, who * doesn't like the curly quote
	  marks * wants combining diacritics to work * wants tight line
	  spacing Trying to reduce font height: * exclamdown was below -200
	  * Throughout Extended Greek, ypogegrammeni were too low.
	  Shortened glyph, and raised all references. * Lots of messing
	  with Latin Extended ranges to make glyphs mostly fit into 800
	  height. Mostly succeeded. A couple will get chopped. * Messed
	  with "commaaccent" glyphs, which were very low * Cyrillic 04B1
	  had a tail that was incorrectly low * Much mucking with Georgian
	  range. Moved up by 95 (read Georgian is written as though
	  centered between two horizontal lines, rather than as sitting on
	  a baseline) There are still a few very high glyphs. FontForge
	  U+0122 called Gcommaaccent, glyph looks like that, but Unicode
	  says it's Gcedilla. Made the ones called cedilla by Unicode to be
	  cedillas Note bug in Unicode: standard for 0122, 0123, 0136,
	  0137, 013B, 013C, 0145, 0146, 0156, 0157 all talk about cedilla,
	  say to make it with cedilla, but example shows comma. By the way:
	  * Got rid of commaaccent and dotlessj in Corporate Use * Replaced
	  shadedark, with little squares now not overlapping. * Corrected
	  IPA symbol 'ts' 02A6, added 02a8, 02a9, 02aa, 02ab, 02ac, 02ad,
	  02ae, 02af (so many changes...the CVS server was down...)

2008-04-05 18:18 +0000 [r688]  Stevan_White:

	* FreeSerif.sfd: e-named arabic and hebrew
	  characters Big adjustment to comma-accents. Mostly effects Greek
	  Extended. Made such accents to be like comma, rather than like
	  Russian apostrophe (and de-referenced that symbol)

2008-04-04 07:17 +0000 [r687]  Stevan_White:

	* FreeMono.sfd,
	  FreeSerif.sfd: raised dot on superscript i
	  (2071) to make it more distinct at small sizes

2008-04-03 23:33 +0000 [r686]  Stevan_White:

	* FreeMono.sfd: added two IPA symbols

2008-04-02 20:15 +0000 [r685]  Stevan_White:

	* FreeSerif.sfd: fixed a few more control points
	  too close

2008-04-02 18:59 +0000 [r684]  Stevan_White:

	* FreeSerif.sfd: Fixed names of languages in
	  ligature table for latn "w/i". This fixes a crash when FontForge
	  opened the ttf table

2008-04-02 18:47 +0000 [r683]  Stevan_White:

	* FreeSerif.sfd: Motivated by bug crashing
	  FontForge when opening ttf file, started cleanup of useless
	  control points. Not finished. Got partway through Sinhala

2008-03-31 21:51 +0000 [r682]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd: Fixed various "Find
	  Problems", including glyphs with mixed-up names, and bad TT
	  matrices. (lots more bad TT matrices remain)

2008-03-31 06:50 +0000 [r681]  Stevan_White:

	* FreeSerif.sfd: re-named a bunch of Cyrillic
	  letters

2008-03-30 22:32 +0000 [r680]  Stevan_White:

	* FreeSerif.sfd: put above mark on Cyrillic i
	  and double-dot i for Slavonic number forms

2008-03-30 21:06 +0000 [r679]  Stevan_White:

	* FreeSans.sfd: tightened spacing on glyphs of
	  last commit

2008-03-30 21:03 +0000 [r678]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Concerning bug #16120, Include
	  upper case Wynn and upper case Yogh Adapted Herman Miller's
	  Thyromanes letters 01F7 021C 021D for Serif Drew my own versions
	  for Sans.

2008-03-30 16:00 +0000 [r677]  Stevan_White:

	* FreeSerif.sfd: Added 04F6,7

2008-03-30 15:34 +0000 [r676]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: Made more Cyrillic diacritics
	  really combine. Made a mark lookup just for Cyrillic diacritics,
	  Marked most of the unadorned Cyrillic alphabet. Still not clear
	  on correct shapes for some of the marks.

2008-03-29 23:25 +0000 [r675]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoOblique.sfd: Tweeks to accents

2008-03-29 20:32 +0000 [r674]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd: small adjustments in Cyrillic

2008-03-29 20:14 +0000 [r673]  Stevan_White:

	* FreeSerif.sfd: corrected small palochka made
	  Cyrillic combining hundred-thousands and millions really combine
	  named some combining diacriticals

2008-03-29 14:18 +0000 [r672]  Stevan_White:

	* FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: mostly adjusted
	  horizontal spacing of mono oblique faces

2008-03-29 13:18 +0000 [r671]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: More cleanup of
	  Cyrillic ranges Completely re-did horizontal spacing of
	  SerifItalic and SerifBoldItalic. See bug #17912, poor kerning in
	  Cyrillic oblique... https://savannah.gnu.org/bugs/index.php?17912
	  It looked like chaos to me. Only so much can be done: the font is
	  flawed. But I think the changes make text readable in these
	  faces. There were many dozens of incorrect glyphs in
	  higher-numbered characters. It looked like someone started
	  copying letters that were like the correct ones, meaning to edit
	  them later, but never got around to it. (Or else, a facile
	  copying of glyphs that kinda looked right, but really kinda
	  weren't.) I deleted all those I could find. No glyph is better
	  than a wrong glyph. Futzt with accents, shooting for consistency
	  and readability. A maintenance thing: making correct references
	  (acyrillic vs a, although they may be the same glyph) I made a
	  lot of headway, but it isn't finished. Likewise, a large fraction
	  of these are compound characters, which can be made with
	  references, resulting in easier maintenance, reduced likelihood
	  of errors, and smaller files. I replaced many.

2008-03-29 00:34 +0000 [r670]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd: Cyrillic italic Added italic,
	  bolditalic 0493, 04a7, 04AD because their form clearly varies in
	  italic. But was just guessing...

2008-03-29 00:14 +0000 [r669]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd: Cyrillic italic Added
	  italic, bolditalic 0493, 04AD because their form clearly varies
	  in italic. But was just guessing as to exact form.

2008-03-28 23:16 +0000 [r668]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Overhaul of Cyrillic
	  Italic, BoldItalic added small yat for bug #22588 (note Times New
	  Roman doesn't use alternate form in Italic) All forms of Serif
	  have big problems in Cyrillic. The ugliest is in roman. The
	  letters, even of the Russian alphabet, are of inconsistent height
	  (awfully, small 0438 (ii) 0446 (tse)) and they vary from the
	  height of Latin and they vary from the height of italic and bold.
	  They are a mish-mash of letters from several fonts, of similar
	  (but not quite identical) weight, and similar, (but not quite
	  identical) size. I think the best solution would be to identify
	  the face that best matches Latin, and fill the range with that. I
	  think this is possible because the rarer letters seem to be
	  better: the common letters are the ones that are wrong. For now,
	  I just increased the sized of 0438 and 0446, and 048a, 048b, also
	  0459 (lje) 045A (nje) 0464 (dje) Other issues ------------ Were
	  outline, with home-made accents - replaced with references 0400
	  (was referencing E) 0401, 0402, 040c, 040d, 040e 0450, 0451, 0453
	  more in the later ranges. Deleted dozens of characters in Bold,
	  Italic, and BoldItalic, that appeared half-made (a part of the
	  letter was copied, but the letter was never completed, and was
	  basically wrong) Lots of letters with hooks and lines in italic
	  were represented as just the unhooked version in italic. 048a,
	  048b (short i with tail, had big-eared breve common in Cyrillic,
	  but unlike other letters) Re-implemented many compound letters
	  with references. Questionable ------------ In all versions,
	  0478-9 Uk, uk. SerifItalic 0495 Small ge with acute, in italic:
	  is it like italic ge, or oblique? Well, peeked at Times New
	  Roman, which has it oblique.

2008-03-27 20:33 +0000 [r667]  Stevan_White:

	* FreeSerifBoldItalic.sfd: Moving all Greek
	  capitals with accent so they don't cover previous letter.
	  Remedies bug #12798

2008-03-27 20:27 +0000 [r666]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Various tweeks to accented
	  Latin letters. Connected O-ogonek correctly

2008-03-27 19:55 +0000 [r665]  Stevan_White:

	* FreeSerifItalic.sfd: Accents of numerous
	  accented Latin letters got shifted in a previous commit. This
	  fixes it.

2008-03-27 19:28 +0000 [r664]  Stevan_White:

	* FreeSerif.sfd: Adjusted combining tack left
	  and right (0318-0319) to be above -300 EM.

2008-03-26 23:37 +0000 [r663]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Added some "middle" marks for
	  positioning of diacritics

2008-03-26 23:21 +0000 [r662]  Stevan_White:

	* FreeSans.sfd: copied 4 enclosing combining
	  diacriticals from Serif 20DD - 20E0

2008-03-26 23:13 +0000 [r661]  Stevan_White:

	* FreeSerif.sfd: adjusted and added some
	  enclosing diacritics 20DD - 20E0 could use more work. Other faces
	  need 'em too. In response to Debian bug #472566 ttf-freefont:
	  U+20DD COMBINING ENCOLSING CIRCLE doesn't combine
	  http://bugs.debian.org/cgi-bin/bugreport.cgi?bug=472566

2008-03-26 20:00 +0000 [r660]  Stevan_White:

	* FreeSerif.sfd: Lowered a few over-high Latin
	  accents

2008-03-25 23:27 +0000 [r659]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd: FreeSansBold Devangari only
	  digits 1 and 2, and nothing else. Deleted FreeMonoBold
	  FreeMonoOblique FreeSerifBold FreeSeriftalic FreeSerifBoldItalic
	  FreeSansOblique FreeSansBold FreeSansBoldOblique got rid of
	  dotlessj, comma in Corporate Use Single Substitution lookup, ccmp
	  table made proper dotlessj, re-linked j-circumflex Note:
	  FreeSansBold has a commaaccent in Corporate Use, used by several
	  other characers. Haven't done anything about this.

2008-03-25 07:30 +0000 [r658]  Stevan_White:

	* FreeSerif.sfd: Added/corrected glyphs for yeh
	  hamza in Arabic, Added init and medi lookups for yeh hamza.

2008-03-24 21:54 +0000 [r657]  Stevan_White:

	* FreeSerif.sfd: Added isolated and final forms
	  for 0629 teh marbuta 0624 waw hamza 0626 yeh hamza 0649 alef
	  maksura A previous commit had added lookups that referred to
	  these,

2008-03-24 18:51 +0000 [r656]  Stevan_White:

	* FreeSerif.sfd: more fiddling with
	  super/subscripts

2008-03-24 12:17 +0000 [r655]  Stevan_White:

	* notes/README-downloads.txt (added): An explanatory
	  file for the downloads directory

2008-03-24 11:59 +0000 [r654]  Stevan_White:

	* sfd/Makefile,
	  tools/GenerateTrueType, Makefile,
	  tools/GenerateOpenType (added): Scripts and Make
	  targets to generate OpenType fonts and zip file

2008-03-24 11:56 +0000 [r653]  Stevan_White:

	* notes/maintenance.txt: Added gnupload and info
	  about tagging

2008-03-23 18:11 +0000 [r652]  Stevan_White:

	* Makefile: no longer needs VPATH on top level

2008-03-23 18:04 +0000 [r650]  Stevan_White:

	* ChangeLog: additions for last few days

2008-03-23 14:21 +0000 [r649]  Stevan_White:

	* FreeSerif.sfd: last problem with Find Problems
	  -> ATT 'mark' Latin lookup afii10026 is in 'cyrl', also afii10074
	  (These are upper and lower Cyrillic i) [Just removed mark from
	  both letters] 'half' Bengali lookup Khanda_Ta is in 'bng2' [added
	  bng2 to lookup]

2008-03-23 13:28 +0000 [r648]  Stevan_White:

	* FreeSerif.sfd: added TtTable etc

2008-03-23 13:15 +0000 [r647]  Stevan_White:

	* FreeSerif.sfd: clean-up of Points too Close
	  through to end of font. This episode completes the paths/points
	  clean-up of Serif. (But note: many ranges, esp. Ethiopic,
	  Japanese, and Indic, have way too many points, resulting in
	  lumpiness.)

2008-03-23 12:03 +0000 [r646]  Stevan_White:

	* FreeSerif.sfd: clean-up of Points too Close
	  though Arabic

2008-03-23 11:50 +0000 [r645]  Stevan_White:

	* FreeSerif.sfd: clean-up of Points too Close
	  through Hiragana and Katakana

2008-03-22 14:43 +0000 [r644]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Mostly additions to FreeSans,
	  mostly having to do with LaTeX ranges. This basically fills the
	  LaTeX range for FreeSans, except for extensible brackets etc.

2008-03-21 23:49 +0000 [r642-643]  Stevan_White:

	* Makefile: split Makefile into two levels

	* sfd/Makefile (added): tidier with a lower-level
	  makefile

2008-03-21 21:51 +0000 [r641]  Stevan_White:

	* Makefile: made quieter

2008-03-21 20:57 +0000 [r640]  Stevan_White:

	* FreeSerif.sfd: in previous commit, hadn't
	  relly saved the changes about the ligature tables. this does that
	  also, named the "ciel" and "floor" characters

2008-03-21 20:50 +0000 [r636-639]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd: (these changes were also
	  made in the previous few commits of other faces) changed name of
	  00B5 from 'mu' to 'micro' changed name of 2206 from 'Delta' to
	  'Delta.math' changed name of 0308 from 'diaerisis' to
	  'diaerisiscomb'

	* FreeSerif.sfd: split lookup for ligatures in
	  latin into two classes; ff, ffl, fl which are appropriate for all
	  languages, and fi, ffi, which are not appropriate in Turkish (due
	  to distinction between short and long i)

	* FreeMonoBoldOblique.sfd: moved dotlessj from
	  Corporate Use, deleted commaaccent there mis-named glyphs
	  tcommaaccent, Tcommaaccent changed name of 030A from 'dieresis'
	  to 'ringcomb'

	* FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd: removed bogus glyphs
	  for 200C 200D, ZWJ and ZWNJ (zero-width joiner, zero-width
	  non-joiner)

2008-03-21 20:20 +0000 [r633-635]  Stevan_White:

	* tools/ConvertFont (removed): this was apparently
	  an older version of GenerateTrueType

	* tools/GenerateTrueType: Took out scaling to 2048
	  (was there a good reason for that?) Made to first auto-hint whole
	  font

	* Makefile: made to work for me

2008-03-21 15:20 +0000 [r632]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd: Regularized stacking of
	  accents in Latin Extended Additional

2008-03-21 15:09 +0000 [r631]  Stevan_White:

	* FreeSerif.sfd: everal characters in U+F600
	  Corporate Use range dotlessj, onefitted (and other "fitted"
	  numerals), commaaccent registered, copyright, trademark According
	  to "Adobe Glyph List"
	  http://www.tug.org/texlive/Contents/live/texmf-dist/doc/generic/t2/glyphlist.txt
	  Adobe "fitted" numerals used to go in the corporate use section
	  More in U+F800 range more corporate symbols represented elsewhere
	  math braces, brackets move some to 2300 range Now Serif has full
	  set extensible brackets in Miscellaneous Technical Also, think
	  I've filled out all of the IPA

2008-03-21 15:06 +0000 [r629-630]  Stevan_White:

	* FreeMono.sfd: Several additions to IPA
	  Regularized stacking of accents in Latin Extended Additional

	* FreeSans.sfd: egularized stacking of accents
	  in Latin Extended Additional Added some arrows Some Blackboard
	  Bold Several characters in U+F600 Corporate Use range dotlessj,
	  onefitted, commaaccent dotlessj referred to by: jcircumflex,
	  uni01F0: how to move it? (renamed it to uFFFF, re-linked others
	  by hand) commaaccent http://diacritics.typo.cz/index.php?id=9
	  should be u+0326 but wasn't linked to anything anyway

2008-03-21 15:01 +0000 [r628]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Got rid of dotlessi
	  etc single substitution and related ccmp table Regularized
	  stacking of accents in Latin Extended Additional FreeSansBold
	  U+0617 etc, read glyphs "4GWglm" It should be arabic. Deleted

2008-03-19 08:21 +0000 [r627]  Stevan_White:

	* ChangeLog: added changes of the 18th

2008-03-19 00:14 +0000 [r626]  Stevan_White:

	* FreeSans.sfd: clean-up of all path issues and
	  points too close

2008-03-18 19:52 +0000 [r625]  Stevan_White:

	* FreeSans.sfd: Re-ordered the ShortTable,
	  because when I did autoinstruct on the quadratic version, it did
	  it this way.

2008-03-18 19:44 +0000 [r624]  Stevan_White:

	* FreeSans.sfd: A ligature referred to a name
	  with no entry: afii301. This is the Adobe name for zero-width
	  joiner, U+200D. With this, FontForge's "Find Problems" ATT "Check
	  for missing glyph names" finally passes.

2008-03-18 19:41 +0000 [r623]  Stevan_White:

	* FreeSans.sfd: zero-width joiner and zero-width
	  non-joiner both had glyphs, incorrectly. (u+200B 200C)

2008-03-18 19:33 +0000 [r622]  Stevan_White:

	* FreeSans.sfd: Seems one ot the 'pres' tables
	  contains mixed Devangari and Gujarati. This is probably a
	  mistake, but re-named the table accordingly, and added the
	  script. With this, FontForge "Find Problems" for ATT "Check for
	  missing scripts in features" finally passes.

2008-03-18 19:24 +0000 [r621]  Stevan_White:

	* FreeSans.sfd: removed another 'above' mark
	  form a greek letter

2008-03-18 19:14 +0000 [r619-620]  Stevan_White:

	* FreeSans.sfd: Likewise a Gugarati lookup had
	  script DFLT.

	* FreeSans.sfd: 'pres' Pre Base Substitution has
	  script DFLT. That cannot be right: this contains Devangari
	  characters. Changed script and name accordingly. This was
	  probably a mistake and a leftover.

2008-03-18 19:07 +0000 [r618]  Stevan_White:

	* FreeSans.sfd: got rid of "lookup \d\d" in
	  table names

2008-03-18 19:01 +0000 [r617]  Stevan_White:

	* FreeSans.sfd: The 'aalt' table for Latin
	  contains only bengali characters. [renamed accordingly, and put
	  in script 'beng' rather than 'latn'] The table "Ligature
	  Substitution for Devanagari lookup 46" is really a 'reph'.
	  Re-named accordingly. The table "'vatu' Vattu Variants lookup 24"
	  is in script 'DFLT', which is wrong. The characters are from
	  Gujarati. But there is already such a table for Gujarati. Changed
	  name and script accordingly. Got rid of Single Substitution
	  table.

2008-03-18 18:54 +0000 [r616]  Stevan_White:

	* FreeSans.sfd: Think I fixed the problem
	  causing the FontForge crashes. Related to the dotlessi thing.
	  There's a table Lookup: 6 0 0 "'ccmp' Glyph
	  Composition/Decomposition in Latin lookup 0" {"'ccmp' Glyph
	  Composition/Decomposition in Latin lookup 0 subtable" } ['ccmp'
	  ('latn' <'dflt' > ) ] It is related to another lookup ChainSub2:
	  class "'ccmp' Glyph Composition/Decomposition in Latin lookup 0
	  subtable" that has something to do with i and j. It ends with
	  SeqLookup: 0 "Single Substitution lookup 47" EndFPST When I
	  removed the lookup for i and j, the SeqLookup line would be badly
	  corrupted, and subsequent saves resulted in a segfault and a
	  truncated sfd file. So I doctored the whole thing with vi.

2008-03-18 08:13 +0000 [r615]  Stevan_White:

	* FreeSans.sfd: Again got rid of single
	  substitution lookup changing dotlessi and dotlessj to i and j,
	  because it's wrong.

2008-03-18 08:10 +0000 [r614]  Stevan_White:

	* FreeSans.sfd: Regress to 1.118. Somehow
	  introduced a crash. (I wasn't careful enough changing the names
	  of the lookups, I think--need to make sure the change results in
	  unique names).

2008-03-17 23:36 +0000 [r613]  Stevan_White:

	* FreeSans.sfd: got rid of single substiturions
	  of dotlessi dotlessj for i and j. these were just wrong. the
	  'aalt' table only contained Bengali, although it said 'latn' and
	  its name was Latin. Changed name and script to correct. Got rid
	  of extra Devanagari vatu table

2008-03-17 23:30 +0000 [r612]  Stevan_White:

	* FreeSans.sfd: got rid of 'lookup xx' in table
	  names

2008-03-17 23:26 +0000 [r611]  Stevan_White:

	* FreeSans.sfd: Removed 'mark' named 'above'
	  from some Greek and Cyrillic letters, and corrected it for a
	  couple of diacriticals.

2008-03-16 20:32 +0000 [r609-610]  Stevan_White:

	* CREDITS: clean-up of non-unicode characters

	* CREDITS: converted to UTF-8

2008-03-16 20:18 +0000 [r608]  Stevan_White:

	* AUTHORS: changed name of maintainer to mine

2008-03-16 20:09 +0000 [r607]  Stevan_White:

	* ChangeLog: compiled changes from CVS log. this is
	  a messy process. I'm sure I missed a lot. Meant to represent
	  changes since 02/10.

2008-03-16 17:29 +0000 [r606]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd, FreeMono.sfd,
	  FreeMonoOblique.sfd: clean-up named some
	  unnamed characters removed a duplicate

2008-03-16 13:34 +0000 [r605]  Stevan_White:

	* FreeMono.sfd: Added vowels to GPOS 'mark'
	  lookup table for 'above' added 'below' as well, but only for
	  c-cedilla

2008-03-16 12:11 +0000 [r604]  Stevan_White:

	* FreeMono.sfd: Much futzing with accents in
	  Latin extensions, in attempt to make them readable at small
	  sizes, and to make the glyph bounds reasonable. Note:
	  A-ring-acute is a silly character.

2008-03-15 23:16 +0000 [r603]  Stevan_White:

	* FreeMono.sfd: path clean-up up to points too
	  close passes Validation

2008-03-15 22:43 +0000 [r602]  Stevan_White:

	* FreeMonoBoldOblique.sfd: clean-up of path
	  problems up to points too close

2008-03-15 22:19 +0000 [r601]  Stevan_White:

	* FreeMonoBoldOblique.sfd: Made to Validate
	  Added points at extrema

2008-03-15 21:59 +0000 [r600]  Stevan_White:

	* FreeSans.sfd: Changed names of a bunch of
	  glyphs with invalid TrueType names, in response to failed
	  Validation, and in Windows the font not showing up in the font
	  display. These were all in the range 0x1025f+, which is an
	  attempt to take care of combining forms in Indic scripts (I don't
	  know if this work was ever completed, or if it is the best way to
	  do this). The names were like uni0916_uni094D.half_uni0928.pres
	  or longer uni0926_uni094D.half_uni0926_uni094D.half.pres and a
	  few much longer than this. By a process of removing redundant
	  information, I think I preserved what information was there.
	  There were a few more names that were never filled in, but
	  contained a minus, which isn't valid. These start with
	  "nonunicode", and I replaced the minus with an underscore.

2008-03-15 19:55 +0000 [r599]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: some futzing with Combining
	  Diacriticals

2008-03-15 19:50 +0000 [r597-598]  Stevan_White:

	* FreeSerifBold.sfd: changed WinInfo

	* FreeSerifBoldItalic.sfd: added TtTable

2008-03-15 16:12 +0000 [r596]  Stevan_White:

	* FreeSerif.sfd: Fixed several lookup issues,
	  based on "Find Problems". TamlCA_TamlSSA.half with ligature from
	  lookup subtable 'haln' Halant Forms in Tamil lookup 24 subtable
	  refers to a missing glyph glyph190 Similar complaint with a
	  'half' lookup. For this and the other glyph190 issue, I changed
	  glyph190 to TamlCA_TamlSSA, which looks right according to its
	  surroundings. The lookup 'aalt' Access All Alternatives i is
	  active for glyph bn_ekaar which has script 'bng2', yet this
	  script does not appear in any of the features which apply the
	  lookup. Similar complaint about bn_aikaar. For this I changed the
	  script of the lookup to bng2, and changed the name of the lookup
	  accordingly. The lookup 'liga' standard Ligatures look is active
	  for glyph uni0937091F which has script 'dev2', yet this script
	  does not appear in any of the features which apply the lookup. It
	  makes a ligature of uni0937 and uni091F, which are in Devangari.
	  So I changed the script of the lookup to 'dev2' and changed the
	  name of the lookup accordingly. Also shortened the names of many
	  of the lookup tables.

2008-03-15 14:31 +0000 [r595]  Stevan_White:

	* FreeSerifBoldItalic.sfd: cleanup of path
	  problems, including points too close

2008-03-15 14:04 +0000 [r594]  Stevan_White:

	* FreeSerifBoldItalic.sfd: clean-up of path
	  problems, including extrema

2008-03-15 13:22 +0000 [r593]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: CJK punctuation: made some of
	  the very high glyphs smaller (under 900EM) The brackets in Sans
	  were very ugly, and not even Sans-serif. Serif: added extensible
	  square brackets, diddled with integral corrected direction of
	  some added glyphs

2008-03-15 13:13 +0000 [r592]  Stevan_White:

	* FreeSansBoldOblique.sfd: added TtTable

2008-03-15 13:10 +0000 [r591]  Stevan_White:

	* FreeSansBoldOblique.sfd: clean-up of path
	  problems, including points too close

2008-03-15 09:41 +0000 [r590]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: OS/2 Metrics back to
	  900/300

2008-03-15 09:35 +0000 [r589]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: OS/2 Metrics back to
	  900/300 -- kedit doesn't like offset values

2008-03-14 23:17 +0000 [r588]  Stevan_White:

	* FreeSansBold.sfd,
	  FreeSansOblique.sfd: added TfTables

2008-03-14 23:08 +0000 [r587]  Stevan_White:

	* FreeSansBold.sfd: clean-up of path problems
	  extrema self-intersecting ordered PS blue values

2008-03-14 22:30 +0000 [r586]  Stevan_White:

	* FreeSansBold.sfd: got rid of mixed references
	  and contours

2008-03-14 22:22 +0000 [r585]  Stevan_White:

	* FreeSansOblique.sfd: much clean-up of "points
	  too close"

2008-03-14 21:18 +0000 [r584]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: unified OS/2 Metrics
	  added Grid Fit added slated-hyphen to Sans

2008-03-14 19:35 +0000 [r583]  Stevan_White:

	* FreeSerif.sfd: in response to Validate,
	  changed names of three glyphs in the Tamil ligatures range...all
	  clearly bugs.

2008-03-14 19:18 +0000 [r582]  Stevan_White:

	* FreeSerif.sfd: cleanup of many path problems
	  "points too close"

2008-03-14 07:26 +0000 [r581]  Stevan_White:

	* FreeSerif.sfd: in response to validate
	  results, fixed a few glyphs

2008-03-14 07:19 +0000 [r580]  Stevan_White:

	* FreeSerif.sfd: got rid of mixed references and
	  contours

2008-03-14 00:05 +0000 [r579]  Stevan_White:

	* FreeSerif.sfd: changed OS/2 metrics various
	  other clean-ups

2008-03-13 23:43 +0000 [r578]  Stevan_White:

	* FreeMonoBoldOblique.sfd: added TtTable

2008-03-13 23:39 +0000 [r577]  Stevan_White:

	* FreeMonoBoldOblique.sfd: got rid of mixed ref
	  and contour

2008-03-13 23:33 +0000 [r576]  Stevan_White:

	* FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: removed PS Private
	  StemSnapV, which was empty anyway.

2008-03-13 23:28 +0000 [r574-575]  Stevan_White:

	* FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: added Grid Fit tables

	* FreeMono.sfd: change OS/2 metrics to 900/300

2008-03-13 23:19 +0000 [r573]  Stevan_White:

	* FreeSerifItalic.sfd: added TtTable, ShortTable

2008-03-13 23:15 +0000 [r572]  Stevan_White:

	* FreeSerifItalic.sfd: Removed PS Private
	  StemSnapV -- wasn't right anyway added Grid Fitting made better
	  OS/2 metrics

2008-03-13 23:06 +0000 [r571]  Stevan_White:

	* FreeSerifItalic.sfd: further path cleanup:
	  points too close together. in Bengli range, much yet to do

2008-03-13 20:00 +0000 [r569-570]  Stevan_White:

	* FreeMonoOblique.sfd: cleaned up remaining path
	  problems, missing extrema fixed some math characters

	* FreeMonoBold.sfd: cleaned up path problems,
	  and mixed refs and contours added TfTable

2008-03-13 08:25 +0000 [r568]  Stevan_White:

	* FreeSerifItalic.sfd: path clean-up more
	  self-intersecting added many missing extrema fixed mixed
	  references and glyphs

2008-03-12 23:11 +0000 [r567]  Stevan_White:

	* FreeSans.sfd: Rearranged PS BluesValues so
	  they were in increasing order, made all 20 in width. In response
	  to FontForge Validate complaint, in hopes of making font work in
	  Windows.

2008-03-12 22:45 +0000 [r566]  Stevan_White:

	* FreeMono.sfd: Copied in by hand TrueType
	  tables from a version with quadratic splines. I hope this will
	  make the glyphs smooth properly in Windows.

2008-03-12 22:41 +0000 [r565]  Stevan_White:

	* FreeSans.sfd: Added TrueType tables (copied in
	  by hand from version with quadratic splines)

2008-03-12 22:34 +0000 [r563-564]  Stevan_White:

	* FreeSans.sfd: clean-up tweek

	* FreeSans.sfd: Got rid of mixed contours and
	  refs

2008-03-12 22:18 +0000 [r562]  Stevan_White:

	* FreeMono.sfd: two more glyphs with quadratic
	  problems

2008-03-12 22:07 +0000 [r561]  Stevan_White:

	* FreeMono.sfd: a few paths that don't convert
	  well to quadratics

2008-03-12 21:45 +0000 [r560]  Stevan_White:

	* FreeMono.sfd: Fixed glyphs with mixed contours
	  and references

2008-03-12 01:05 +0000 [r559]  Stevan_White:

	* FreeSerifBoldItalic.sfd: cleanup of path
	  problems as others

2008-03-12 00:51 +0000 [r558]  Stevan_White:

	* FreeSerifItalic.sfd: cleanup of path problems
	  as others

2008-03-12 00:08 +0000 [r557]  Stevan_White:

	* FreeSerifBold.sfd: cleanup of path problems
	  open intersecting clockwise ref reversed points outside glyph
	  removed hints

2008-03-11 23:13 +0000 [r556]  Stevan_White:

	* FreeMonoBoldOblique.sfd: cleanup of path
	  problems open intersecting clockwise flipped ref points beyond
	  spline removed hints

2008-03-11 22:59 +0000 [r555]  Stevan_White:

	* FreeMonoBold.sfd,
	  FreeMonoOblique.sfd: cleanup of path problems
	  open paths intersecting clockwise flipped references points
	  beyond glyph removed hints

2008-03-11 22:31 +0000 [r554]  Stevan_White:

	* FreeSansBoldOblique.sfd: cleanup of path
	  problems open intersecting clockwise flipped references beyond
	  spline

2008-03-11 21:51 +0000 [r553]  Stevan_White:

	* FreeSansBold.sfd: clean-up of path problems
	  open intersecting clockwise flipped references control points
	  beyond spline removed hints

2008-03-11 21:46 +0000 [r552]  Stevan_White:

	* FreeSansOblique.sfd: clean-up of path and
	  reference problems open paths intersecting clockwise flipped refs
	  removed hints

2008-03-09 20:58 +0000 [r551]  Stevan_White:

	* FreeSerif.sfd: continuing general clean-up,
	  Found several ligatures that referred to a missing glyph "ZWJ".
	  Took this to mean the "zero width joiner" u+200D There are a
	  couple more cases in Tamil, but I don't know the glyph they're
	  referring to.

2008-03-09 14:27 +0000 [r550]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSans.sfd,
	  FreeSerif.sfd: further cleanup of path/ref
	  problems

2008-03-09 14:08 +0000 [r549]  Stevan_White:

	* FreeMono.sfd: clean-up of path problems

2008-03-09 14:00 +0000 [r548]  Stevan_White:

	* FreeMono.sfd: Efforts to make legible at 9pt.
	  At 8pt, glyphs are at least distinct.

2008-03-09 12:53 +0000 [r547]  Stevan_White:

	* FreeMono.sfd: math symbols: set for LaTeX 2e
	  seems to be all there. To do: improve legibility at 9pt. Some
	  double-arrows are just a blur.

2008-03-09 12:18 +0000 [r546]  Stevan_White:

	* FreeMono.sfd: more math characters corrected
	  logical 'assert' relations, etc. 22a2-22af

2008-03-09 10:41 +0000 [r544-545]  Stevan_White:

	* FreeSerif.sfd: corrected L-dot

	* FreeSerifItalic.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd: Math symbols ============
	  SerifItalic Greek lunate epsilon FreeMono many additions in math
	  range reduced size of binary union, intersection, vee, wedge
	  correcte empty set

2008-03-08 23:22 +0000 [r543]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: FreeSans added Greek lunate
	  epsilon and rho symbol both un-stacked some more stacked
	  diacriticals

2008-03-08 22:41 +0000 [r542]  Stevan_White:

	* FreeSans.sfd: Clean-up of font paths open
	  self-intersecting outermost clockwise missing extrema also
	  flipped references (unlinked)

2008-03-08 18:17 +0000 [r541]  Stevan_White:

	* FreeSerif.sfd: Added lunate epsilon Corrected
	  empty set tightened up spacing of some other technical characters
	  worked on some more math operators involving =

2008-03-08 15:11 +0000 [r540]  Stevan_White:

	* FreeSerif.sfd: Added several math operators
	  used by LaTeX 2e triangle several arrows arrowhookleft
	  arrowhookright Supplemental Arrows-A long left arrow long right
	  arrow long leftright arrow long left double arrow long right
	  double arrow long leftright double arrow long left arrow with
	  flat tail long right arrow with flat tail

2008-03-08 13:11 +0000 [r539]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Supplemental Math operators
	  ============== Added a few of the "n-ary" operators. Math
	  operators ============== Corrected n-ary union, intersection, and
	  spikes to be larger than the binary operators Made (many of) the
	  operators based on + - = to use those symbols directly (by
	  reference or copying). I think it is good practice to harmonize
	  the appearance of math operators with the Latin character set.
	  Geometrical Shapes ================== Made the large circle
	  larger still. When is it large enough? Combining marks for
	  Symbols =========================== Made the combining circle to
	  really combine. Made a circle-slash

2008-03-08 10:15 +0000 [r538]  Stevan_White:

	* FreeSerif.sfd: Clean-up of path problems
	  intersecting paths missing extrema outer path clockwise Many
	  changes in Bengali, Ethiopic ranges. These could really use
	  further clean-up. Added 'divides' and 'not divides'

2008-03-08 10:10 +0000 [r537]  Stevan_White:

	* FreeMono.sfd: Added Grid Fit "GASP" table
	  Clean-up of contour problems: open paths intersecting paths (esp.
	  those of references) outermost paths clockwise missing extrema
	  Also references problems fipped refs tt (TrueType) transformation
	  matrix problems Did same process of un-stacking some accents in
	  Latin Extended range as done for Serif. Made combining
	  diacritical 'hook' to be shorter. (Should have been multiple
	  commits...I know)

2008-03-06 18:58 +0000 [r536]  Stevan_White:

	* FreeSerif.sfd: Shortened and thickened the
	  combining hook mark, U+0309, thus shortening many combined
	  characters and making it look more like the Unicode samples. Then
	  touched on bug #22499 Latin Extended Additionals incorrectly
	  stack accents At least in this face, un-stacked
	  incorrectly-stacked marks.

2008-03-05 22:01 +0000 [r535]  Stevan_White:

	* FreeSerif.sfd: Fixed positioning U+1EC8, 9, I
	  with hook above

2008-03-05 21:45 +0000 [r534]  Stevan_White:

	* FreeSerif.sfd: added spacing modifier letters
	  02B9 02Ba prime and double-prime

2008-03-05 21:36 +0000 [r533]  Stevan_White:

	* FreeSerif.sfd: vertical lines: combining
	  diacritical marks corrected 0300 030D 0329 0348 (were rendered as
	  straight apostrophes) spacing modifier letters added 02C8 02CC

2008-03-05 20:20 +0000 [r532]  Stevan_White:

	* FreeSerif.sfd: Reverses 1.90. I regretted 1.90
	  before while and after doing it.

2008-03-04 20:47 +0000 [r531]  Stevan_White:

	* FreeSerif.sfd: Some modifications to Spacing
	  Modifier Letters 02B0 - 02FF Idea was to bring in line with
	  Unicode that suggests that these characters should all occupy the
	  width of a space in the font, here, 250 EM. Unfortunate
	  side-effects in hundreds of characters that used them. Hope I got
	  them all.

2008-03-03 20:01 +0000 [r530]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Set the OS/2 Sup/Sub
	  settings, which by default looked like random trash. I don't know
	  if it really helps but it looks better.

2008-03-03 00:13 +0000 [r529]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: TTY strings updates
	  Updated Copyright to 2008 Added Vendor URL as the Savannah
	  freefont site Added a standard pangram as the Sample Text for
	  Russian in FreeMono*. It reads: In the thickets of the South once
	  there was a citrus...--yes, but a fake specimen!

2008-03-02 22:50 +0000 [r528]  Stevan_White:

	* FreeMono.sfd: Completed fix of glyph path
	  problems open path intersecting not clockwise missing extrema Had
	  to remove one glyph, because couldn't figure out how to make it
	  not intersect itself: dkshade U+2193, a checkerboard (probably
	  should be re-done as non-intersecting squares). Also, lowered the
	  diacritics on a bunch of capital Latin letters, so they didn't
	  have to intersect when stacked. Act of desperation?

2008-03-02 21:37 +0000 [r527]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Clean sweep, fixing
	  all open glyph contours.

2008-03-02 20:13 +0000 [r526]  Stevan_White:

	* FreeSans.sfd: Parital clean-up of glyph path
	  problems open paths some of intersecting paths There are many
	  more instances of intersecting paths. The Bengali range is
	  particularly affected.

2008-03-02 19:14 +0000 [r525]  Stevan_White:

	* FreeMonoBold.sfd: Cleaned up glyph path
	  problems open paths intersecting paths not clockwise missing
	  extrema

2008-03-02 15:48 +0000 [r524]  Stevan_White:

	* FreeMono.sfd: Fixed most glyph path problems.
	  Open path path direction extrema There are still a few
	  intersecting paths...

2008-03-02 12:31 +0000 [r523]  Stevan_White:

	* FreeSerif.sfd: Clean-up of problems found by
	  FontForge "Find Problems". Fixed all problems with Open Paths
	  Intersecting Paths Check outermost paths clockwise With Check
	  missing extrema there were lots of problems, and it's very
	  fiddly. I got up through the Arabic range, but pooped out in the
	  Indian scripts. Note: Most of the problems were plainly mistakes,
	  or problems that cropped up after some transformation of the
	  glyph. Others seem to be the result of freehand drawing or
	  scanning. More info at: bug #22454: Path bugs in FreeSerif

2008-03-01 21:14 +0000 [r522]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoBoldOblique.sfd: Removed all back
	  layers from glyphs that had them. In most cases, this layer had
	  been used as a guide, slightly modified to make the front layer.
	  Sometimes it was just a mistake. This remedies bug #22381: Some
	  characters have "ghost" glyphs in back layer
	  https://savannah.gnu.org/bugs/index.php?22381 However, this is
	  bound to happen again. It would be good to have a script that
	  checks, or maybe a facility in FontForge's "Find Problems".

2008-03-01 10:20 +0000 [r521]  Stevan_White:

	* FreeSerif.sfd: Arabic now seems to be working,
	  in a limited way, for display of modern text. Remedy for: bug
	  #22329: Arabic not really working
	  https://savannah.gnu.org/bugs/index.php?22329 * Drew several
	  glyphs for special forms of yeh * Drew sukun combining
	  diacritical * Made some diacritical combining marks to be
	  properly zero width. * Added 'init' 'medi' 'fina' tables to
	  convert letters to their initial, medial, and final forms within
	  words. * Added 'rlig' table for required ligatures of lam and
	  alif * Constructed the lam-alif ligatures. Not done: * may be
	  more required ligatures of lam and alif * other common ligatures
	  * 'mark' table for better positioning of diacriticals * 'mark'
	  table for positioning vowels * 'locl' table for localized forms
	  of certain letters * 'ccmp' table for decomposing ligatures Also
	  (nothing to do with Arabic): moved some glyphs 02d4-02D7 up.

2008-02-27 22:45 +0000 [r520]  Stevan_White:

	* FreeSerif.sfd: Shifted entire Arabic alphabet
	  down by 200EM, so it shares the common baseline. I hope. Arabic
	  Arabic Presetation Forms

2008-02-27 22:26 +0000 [r519]  Stevan_White:

	* FreeSans.sfd: Filled out Combining Diacritical
	  Forms in Sans

2008-02-27 22:02 +0000 [r518]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: more tweeks to Combining
	  Diacritical Forms

2008-02-27 08:38 +0000 [r517]  Stevan_White:

	* FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd: Combinint diacriticals range
	  Mostly filled Sans. The rest are tweeks.

2008-02-26 00:12 +0000 [r516]  Stevan_White:

	* FreeSerif.sfd: More tweeks to combining
	  diacriticals. added DPOS 'mark' tables for Latin below topright
	  ogonek and put corresponding marks in many Latin letters

2008-02-24 11:57 +0000 [r515]  Stevan_White:

	* FreeSerif.sfd: Some tweeks to previous commit.

2008-02-24 11:34 +0000 [r514]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Much fiddling with the
	  "combining diacriticals" range 0300-036F. More info: see bug
	  #22331: combining diacriticals are misplaced in FreeSerif Filled
	  in for Serif. Also tidied diacriticals in some of the other
	  faces. Made to align to medium-sized preceding character, when
	  not using anchor marks. Added "above" marks where needed. To do:
	  add "below" table and marks.

2008-02-23 18:30 +0000 [r513]  Stevan_White:

	* FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Continued programme
	  of filling HTML Character Entities in Mono. I think there are
	  still a few missing, but I don't have an easy way to tell...
	  Anyway, it's looking good.

2008-02-23 13:43 +0000 [r512]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Unified and corrected
	  combining diacritical marks U+0360-1 where they existed. Also
	  tidied some of the "tie" marks. These glyphs have width 0. I'm
	  not sure that is the best way to approach the problem of
	  combining marks, but I see other fonts that do the same.

2008-02-23 12:58 +0000 [r511]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Checked U+203F, U+2040 (tie
	  and undertie). Mostly the tie was too low.

2008-02-23 11:55 +0000 [r510]  Stevan_White:

	* FreeSans.sfd, FreeMono.sfd,
	  FreeSerif.sfd: Made "HTML 4 Character
	  Entities" complete for normal-style faces. Partial remedy for bug
	  #21784: missing glyphs for HTML 4 character entities
	  https://savannah.gnu.org/bugs/?21784 Main things remaining: do
	  similar process for bold and oblique forms. ----- notes
	  ----------- Filled out several typographic spaces in Sans and
	  Mono U+2000, etc. Mono made Rfraktur Mono made 'angle' Mono
	  copied cards suites from FreeSerif and scaled to fit Sans drew
	  upsilon-hook. Sans constructed omegapi from omega and a minus
	  Sans made overline Sans flat copied cards suites from FreeSerif
	  (maybe not "sans" enough?) Sans 'similar' didn't look like other
	  such symbols and was much skinnier Sans made several symbols to
	  be references to 'similar' Sans made 'congruent' Sans made
	  reference to aleph from alephmath Sans angleleft and angleright
	  brackets just copied from Serif (in which they look too angular)
	  Sans "subset" is too high and looks like a letter C. Made to be a
	  tad higher than small letter Sans notin doesn't look like isin:
	  'element' 2208 is the height of a capital. Fixed this, and used
	  references to better effect. Sans weierstrass p: copied from Mono
	  (where its style doesn't fit anyway), and widened the stroke
	  -------- other notes ----------------- better check Sans 0361
	  also doubt 223E is right What is to be done about that? Serif
	  2040 tie is low...didn't I fix this? Sans Rfraktur and Ifraktur
	  are not sans-serif. Overall, many Sans math symbols are much
	  thinner than seems to fit. Sans surprises me not all the
	  "equals"-like characters are made from "minus" Sans "period" is a
	  square. (Don't like the dot operator being a square, but that
	  would be consistent.) Serif ceil and floor glyphs are not high
	  enough

2008-02-21 21:28 +0000 [r509]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: In previous commit,
	  changed Font Info -> OS/2 -> Vendor ID to GNU Repeated that for
	  the remaining fonts.

2008-02-21 21:01 +0000 [r508]  Stevan_White:

	* FreeSans.sfd, FreeMono.sfd,
	  FreeSerif.sfd: Worked toward extended integral
	  signs. U+2320 TOP HALF INTEGRAL U+23AE INTEGRAL EXTENSION U+2321
	  BOTTOM HALF INTEGRAL Regarding bug #13370: INTEGRAL EXTENSION
	  does not align with TOP/BOTTOM HALF INTEGRAL
	  https://savannah.gnu.org/bugs/?13370 Hope was to make the three
	  pieces precisely match up. But the horizontal space eludes me.
	  There is still a tiny gap, althogh I have made the parts slightly
	  longer than they ought to be. Also, FreeMono is still not
	  advertizing that it supports "Miscellaneous Technical Symbols",
	  although FontForge shows it selected in Font Info->OS/2.

2008-02-21 00:46 +0000 [r507]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Moved capital Greeek
	  letters with tonos so tonos doesn't cover preceding letter
	  Partial remedy for bug #12798: some combined Greek characters
	  have a bug https://savannah.gnu.org/bugs/index.php?12798 Only
	  deals with range 0380 - 03df (Greek). More needs to be done in
	  Greek Extended range.

2008-02-20 21:57 +0000 [r506]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Made Greek 'tonos' to
	  be the same as combining accent acute Remedy for bug #12821:
	  FreeSerif has Greek tonos different than Greek acute
	  https://savannah.gnu.org/bugs/index.php?12821 In some cases, just
	  made a reference to U+0301 (combining accent acute) of U+0384
	  (Greek tonos). The characters that used tonos were linked to
	  that. In many cases, this cause spacing problems, which were also
	  fixed

2008-02-20 19:50 +0000 [r505]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoOblique.sfd: Tweeks to curly quotes of
	  previous commit

2008-02-20 08:52 +0000 [r504]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Made proper curly
	  quotes of U2018 - U201F in monospace fonts (removed incorrect
	  reference in some straight quotes U2033... Remedies bug #18300:
	  FreeMono has the wrong quote marks â (U+2018) and â (U+201C)
	  https://savannah.gnu.org/bugs/index.php?18300

2008-02-19 23:17 +0000 [r503]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: This touches on two
	  bug reports with interrelated glyphs bug #20278: Vertical offset
	  of superscripted numerical digits Some vertical offset problems,
	  lots more horizontal ones. Changes also impacted vulgar
	  fractions, so had to fix those too. bug #17756: possible printing
	  problem with vulgar fractions. . . .
	  https://savannah.gnu.org/bugs/index.php?17756 I think this was a
	  case of front and back layers inadvertently both having glyphs in
	  them. Removed the ones I found, and tidied up all vulgar
	  fractions

2008-02-18 23:21 +0000 [r502]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Added to FreeSerif* uni2E17
	  double hyphen In pursuit of bug #22267: Request Double-Hyphen
	  Sign https://savannah.gnu.org/bugs/?22267

2008-02-18 22:37 +0000 [r501]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: unified 'liga' for Latin:
	  now only ff ffi ffl fi fl Other ligatures should be put in dlig,
	  hlig, etc. Removed such as Lj, DZ. I don't thing these are
	  ligatures. (Am I wrong? the glyphs aren't modified...)

2008-02-18 08:33 +0000 [r500]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerif.sfd: Slightly increased the height
	  of Cyrillic Ðž (U+0438) to be even with other similar glyphs
	  (e.g. U+043D) Remedies bug #22289: The Cyrillic Ðž letter in
	  FreeSerif isn't high enough
	  https://savannah.gnu.org/bugs/index.php?22289

2008-02-18 08:18 +0000 [r499]  Stevan_White:

	* FreeSans.sfd,
	  FreeSerif.sfd: Made U+30FB, KATAKANA MIDDLE
	  DOT to be same width as other Katakana. Remedies bug #18326: ã»
	  (U+30FB, KATAKANA MIDDLE DOT) should be fullwidth
	  https://savannah.gnu.org/bugs/index.php?18326

2008-02-18 08:02 +0000 [r498]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Moved U+203F UNDERTIE to be
	  below the baseline, as it should be. See
	  http://www.unicode.org/charts/PDF/U2000.pdf Remedy for bug
	  #18413: U+203F UNDERTIE glyph too high in FreeSerif
	  https://savannah.gnu.org/bugs/?18413

2008-02-18 01:19 +0000 [r497]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Re-promoted ff ffi ffl fi
	  fl as standard ligatures in Latin. Made st ligature historical,
	  if anything. Cleaned up some silly ligatures (Roman numerals as
	  ligatures, etc) There is more to be done here, just for Latin
	  ligatures.

2008-02-17 23:41 +0000 [r496]  Stevan_White:

	* FreeSans.sfd: Offset Hiragana and Katakana
	  ranges of FreeSans as in
	  https://savannah.gnu.org/bugs/index.php?22326

2008-02-17 23:34 +0000 [r495]  Stevan_White:

	* FreeSerif.sfd,
	  FreeSerifBold.sfd: Corrected vertical offset
	  of Hiragana and Katakana, cf
	  https://savannah.gnu.org/bugs/index.php?22326 (with advice from
	  Kenshi Muto)

2008-02-17 23:26 +0000 [r494]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Auto-Hinted
	  everything. The auto-hinting in the new FontForge is said to be
	  (and seems to me to be) much better than in previous versions.

2008-02-17 23:16 +0000 [r493]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: TT Info ->
	  Manufacturer = GNU

2008-02-17 22:58 +0000 [r492]  Stevan_White:

	* FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Updated FontForge SFD
	  file version to SplineFontDB: 2.0 using FontFOrge 20080214 This
	  is a very big change, with which we can carry on with the new
	  FontForge.

2008-02-17 22:20 +0000 [r491]  Stevan_White:

	* FreeSerif.sfd: This removes the georgian-d
	  patch of revision 1.58. I can't imagine what went on here. Maybe
	  I somehow got the patch inverted. Anyway, after the patch, 10e6
	  and 10d3 are indistinguishable, which is wrong, according to
	  http://www.unicode.org/charts/PDF/U10A0.pdf Without the patch,
	  they seem to be correct.

2008-02-14 23:36 +0000 [r490]  Stevan_White:

	* INSTALL: Instructions for KDE local installation
	  Instructions for Windows Vista

2008-02-11 22:55 +0000 [r489]  Stevan_White:

	* FreeSerif.sfd: This is from an anonymous patch
	  patch #5924: correct postscript name for cyrillic yat' Wednesday
	  05/09/2007 at 18:25 (now bug #22265: correct postscript name for
	  cyrillic yat') The postscript name of the two cyrillic letters
	  yat' and YAT' (0x0462) is not correct, so this fixes it: I found
	  confirmation of the PostScript names here
	  http://www.adobe.com/devnet/font/pdfs/5013.Cyrillic_Font_Spec.pdf

2008-02-11 20:06 +0000 [r488]  Stevan_White:

	* README: Made status of Microsoft core web fonts a
	  little clearer.

2008-02-10 19:50 +0000 [r486]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Apply patch
	  011_liga_to_dlig by Christian Perrier from Debian sources. I am
	  not quite sure which bug this is meant to fix, but it is most
	  like bug #15792: Freefont Alef and Lamed combine although this
	  patch is not the patch included in that bug report. (The report
	  was closed...no indication if any action was taken.) This is the
	  last outstanding patch from the Debian sources for ttf-freefont
	  found in ttf-freefont-20060501cvs-12.diff That is, the SVD files
	  of this project should now match the ones used to build the
	  Debian ttf-freefont fonts.

2008-02-10 19:38 +0000 [r483-485]  Stevan_White:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Apply patch
	  009_freemono_really_mono by Christian Perrier from Debian sources
	  meant to remedy bug #17062: Erroneous Georgian D in FreeSerif
	  reported by Christian Perrier

	* FreeSerif.sfd: Apply patch 008_georgian_d by
	  Christian Perrier from Debian sources, meant to remedy bug
	  #17062: Erroneous Georgian D in FreeSerif

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Apply patch
	  007_horizontal_spacing by Christian Perrier from Debian sources,
	  which is meant to remedy bug #17038: horizontal spacing bug in
	  freemono reported by Christian Perrier

2008-02-10 19:28 +0000 [r482]  Stevan_White:

	* FreeMono.sfd: Apply the patch
	  006_remove-russian-sample by Christian Perrier from the Debian
	  sources that remedies bug #15791 Weird strings in freefont fonts
	  reported by Christian Perrier Note this change had previously
	  been made by Primoz Peterlin. The problem was that the Russian
	  string had somehow become corrupted. I may replace it with a
	  correctly encoded version in the future.

2008-02-10 19:23 +0000 [r481]  Stevan_White:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Apply patch
	  004_workaround-spacing-bug by Christian Perrier, from the Debian
	  sources which is meant as a (partial) remedy for bug #15956: Line
	  height is too big in sans and serif reported by Eeli Kaikkonen
	  Thanks also to Jan Willem Stumpel

2008-02-10 19:12 +0000 [r479-480]  Stevan_White:

	* FreeMono.sfd: In preparation for merge with
	  Debian sources, which date from 2006-05-01, remove this commit,
	  which was RCS file: /sources/freefont/freefont/FreeMono.sfd,v
	  revision 1.28 date: 2006-05-04 15:54:45 +0200; author: peterlin;
	  state: Exp; lines: +3 -3; Deleted messy Russian sample text from
	  FreeMono. The Debian diffs already contain this patch, so it will
	  not get re-applied. Note: I also found the Russian string that
	  somehow got corrupted, and may well re-insert it later, because I
	  like it.

	* FreeSans.sfd: In preparation for merge with
	  Debian sources which date from 2006-05-01, un-do this commit,
	  which was revision 1.80 date: 2006-05-01 14:42:53 +0200; author:
	  moyogo; state: Exp; lines: +72 -36; replaced U+2134 Plan to apply
	  the Debian patches, then re-apply this one.

2008-02-03 09:50 +0000 [r478]  Stevan_White:

	* README: updated URLs corrected spelling errors
	  added reference to DejaVu (Bitstream Vera) fonts removed
	  reference to ttfedit, which seems no longer to exist updated
	  freefont URL added Steve White

2008-01-22 18:35 +0000 [r476-477]  Stevan_White:

	* test (removed): remove the test file

	* test (added): test to see if I can really commit
	  with the new account

2006-09-20 11:37 +0000 [r475]  peterlin:

	* INSTALL, ChangeLog: * INSTALL:
	  added installation procedure for MacOS X, courtesy Philipp
	  Kempgen.

2006-05-04 13:54 +0000 [r474]  peterlin:

	* ChangeLog, FreeMono.sfd:
	  Deleted messy Russian sample text from FreeMono.

2006-05-01 12:42 +0000 [r473]  moyogo:

	* FreeSans.sfd: replaced U+2134

2006-04-15 21:49 +0000 [r472]  peterlin:

	* ChangeLog, FreeSans.sfd,
	  FreeSerif.sfd: * FreeSerif.sfd: corrected
	  U+10D3. * FreeSans.sfd: ligature U+FB06 (LATIN SMALL LIGATURE
	  S T) changed from mandatory ("liga") to discretionary ("dlig")
	  (bug #16253).

2006-04-15 21:01 +0000 [r471]  peterlin:

	* ChangeLog, FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: * FreeMono.sfd:
	  deleted incomplete glyph U+FB06 (LATIN SMALL LIGATURE S T);
	  deleted U+FB00, U+FB01, U+FB02, U+FB05 as ligatures. *
	  FreeMonoOblique.sfd, FreeMonoBoldOblique.sfd: added
	  U+FB00; deleted U+FB01, U+FB02 as ligatures. *
	  FreeMonoBold.sfd: deleted U+FB00, U+FB01, U+FB02 as
	  ligatures.

2006-04-15 20:21 +0000 [r470]  peterlin:

	* ChangeLog, FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Added Georgian
	  letters, donated by Gia Shervashidze.

2006-02-22 22:57 +0000 [r469]  peterlin:

	* ChangeLog, FreeMono.sfd,
	  FreeMonoOblique.sfd: * FreeMono.sfd,
	  FreeMonoOblique.sfd: ligature U+FB4F changed from mandatory
	  ("liga") to discretionary ("dlig"). This is respons to
	  Bug#349657: [bug #15792] Freefont Alef and Lamed combine

2006-02-21 00:23 +0000 [r468]  peterlin:

	* ChangeLog, FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeSerifBold.sfd: * FreeSerifBold.sfd,
	  FreeSans.sfd, FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd, FreeSansBold.sfd: ligature
	  U+FB4F changed from mandatory ("liga") to discretionary ("dlig").
	  This is respons to Bug#349657: [bug #15792] Freefont Alef and
	  Lamed combine

2006-02-20 23:13 +0000 [r467]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd: corrected bug#275759: [bug #15790] FreeSerif
	  glyphs for U+2198/U+2199 were reversed.

2006-02-14 23:09 +0000 [r466]  moyogo:

	* ChangeLog, FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: * removed ij and IJ
	  ligature substitutions

2006-02-10 22:13 +0000 [r465]  peterlin:

	* AUTHORS, ChangeLog,
	  CREDITS, FreeSerif.sfd: *
	  FreeSerif.sfd: added small Georgian letters (mkhedruli),
	  donated by Gia Shervashidze * AUTHORS: Added Gia Shervashidze *
	  CREDITS: Added Gia Shervashidze

2006-01-30 23:07 +0000 [r464]  peterlin:

	* ChangeLog, notes/maintenance.txt:
	  notes/maintenance.txt - login as "anonymous"

2006-01-26 22:32 +0000 [r462]  peterlin:

	* ChangeLog, notes/maintenance.txt: *
	  notes/maintenance.txt: Added information on the Makefile now
	  used.

2006-01-26 22:12 +0000 [r461]  peterlin:

	* ChangeLog, FreeSansBold.sfd: *
	  FreeSansBold.sfd: added U+0569, U+0571, U+0579, U+057B,
	  U+0586. Armenian small letters completed.

2006-01-26 15:44 +0000 [r460]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd: added U+0297, U+02AD-02AF. IPA Extensions
	  section is now complete. Copied a dozen of glyphs from Omega IPA
	  to Phonetic Extension section.

2006-01-25 23:01 +0000 [r459]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd: added U+01A, U+01A3, U+01A6, U+01B2, U+01BA,
	  U+01BB, U+01BE, U+01BF.

2006-01-25 12:44 +0000 [r458]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd: aligned small Armenian letters to x-height in
	  response to bug #15480. Armenian in Free Sans needs a major
	  cleanup.

2006-01-24 23:39 +0000 [r457]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd: changed U+0452, U+045B. Cleanup: U+0460,
	  U+0461, U+04Bc, U+04BD, U+0508.

2006-01-24 12:18 +0000 [r456]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd: changed U+0285.

2006-01-24 12:01 +0000 [r455]  peterlin:

	* ChangeLog, FreeSansOblique.sfd:
	  * FreeSansOblique.sfd: replaced accented chars in Latin-1 and
	  Latin Extended-B sections with references, where possible.

2006-01-23 22:13 +0000 [r454]  peterlin:

	* ChangeLog, FreeSans.sfd,
	  FreeSansOblique.sfd: * FreeSans.sfd: added
	  U+0195, U+01AA, U+0297, U+03D7, U+03F0. Several flipped
	  references replaced by outlines. * FreeSansOblique.sfd: Latin
	  Extended-B section more or less brought in sync with FreeSans.

2006-01-23 15:33 +0000 [r453]  peterlin:

	* ChangeLog,
	  FreeMonoBoldOblique.sfd: *
	  FreeMonoBoldOblique.sfd: added glyphs from FreeMonoBold in
	  the Latin Extended-B and IPA Extensions sections.

2006-01-23 14:44 +0000 [r452]  peterlin:

	* ChangeLog, FreeSerifBold.sfd: *
	  FreeSerifBold.sfd: Added U+0224, U+0225. Changed U+01B7,
	  U+01B8, U+04E0, U+0452, U+045B. Replaced accented characters in
	  the Cyrillic region with references.

2006-01-20 23:37 +0000 [r451]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd: Deleted spurious glyphs in the control code
	  area.

2006-01-20 23:27 +0000 [r450]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd: added U+0255, U+0264, U+0277, U+0286, U+029D.
	  Changed U+0261.

2006-01-19 22:00 +0000 [r449]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd: replaced Hardip Pannu Singh's Gurmukhi with
	  AnmolUni by Kulbir Singh Thind.

2006-01-17 22:10 +0000 [r448]  peterlin:

	* ChangeLog, FreeSansBold.sfd: *
	  FreeSansBold.sfd: Added U+018D, U+0194, U+01B5, U+01B6,
	  U+01BE, U+0262, U+02A2.

2006-01-17 14:07 +0000 [r447]  peterlin:

	* ChangeLog, FreeSansBold.sfd: *
	  FreeSansBold.sfd: Changed U+0261 in order to distinguish it
	  from U+0067. Changed U+0251, U+0252.

2006-01-17 13:26 +0000 [r446]  peterlin:

	* ChangeLog,
	  FreeSerifBoldItalic.sfd: *
	  FreeSerifBold.sfd: Small changes in the Cyrillic section.
	  Added U+0183, U+018C.

2006-01-17 10:18 +0000 [r445]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd: Added U+2045, U+2046.

2006-01-17 10:02 +0000 [r444]  peterlin:

	* AUTHORS, ChangeLog,
	  CREDITS, FreeSansBold.sfd: *
	  FreeSansBold.sfd: Filled in the Gurkmukhi part with the
	  AnmolUni-Bold by Kulbir Singh Thind. Also some minor corrections
	  in the Cyrillic part. * CREDITS: Added Kulbir Singh Thind. *
	  AUTHORS: Added Kulbir Singh Thind.

2006-01-14 18:09 +0000 [r443]  peterlin:

	* AUTHORS, ChangeLog,
	  CREDITS, FreeSerif.sfd: *
	  FreeSerif.sfd: Thomas Ridgeway's Tamil characters replaced by
	  the ones released by the Samyak font project. * CREDITS: Added
	  Pravin Satpute, Bageshri Salvi, Rahul Bhalerao and Sandeep
	  Shedmake * AUTHORS: Added Pravin Satpute, Bageshri Salvi, Rahul
	  Bhalerao and Sandeep Shedmake

2006-01-08 14:12 +0000 [r442]  peterlin:

	* ChangeLog, FreeSansBold.sfd,
	  FreeMonoBoldOblique.sfd: FreeSansBold.sfd,
	  FreeMonoBoldOblique.sfd: minor changes

2006-01-05 22:18 +0000 [r441]  peterlin:

	* ChangeLog: Noted moyogo's modifications in
	  ChangeLog as well.

2006-01-05 16:12 +0000 [r440]  moyogo:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: added cedi sign
	  U+20B5, Ghanaian currency

2006-01-05 15:55 +0000 [r439]  moyogo:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: added cedi sign U+20B5,
	  Ghanaian currency

2006-01-05 15:44 +0000 [r438]  moyogo:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: added cedi sign
	  U+20B5, Ghanaian currency

2005-12-29 14:39 +0000 [r437]  peterlin:

	* ChangeLog, FreeSans.sfd:
	  FreeSans.sfd: minor cleanup in the Gujarati part.

2005-12-22 12:46 +0000 [r436]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd: Devanagari and Gujarati parts cleared; once
	  again merged with Gargi 1.9 and Padmaa 0.6, this time correctly
	  so that the anchor points survived the merger.

2005-12-16 15:48 +0000 [r435]  peterlin:

	* ChangeLog, FreeSansBold.sfd:
	  FreeSansBold.sfd: added U+0577.

2005-12-15 09:26 +0000 [r434]  peterlin:

	* ChangeLog, FreeSans.sfd,
	  FreeSansBold.sfd: * FreeSans.sfd: added
	  U+0559, U+055F, U+2024. * FreeSansBold.sfd: added U+056E,
	  U+0573.

2005-12-14 10:07 +0000 [r433]  peterlin:

	* AUTHORS, ChangeLog,
	  CREDITS, FreeSans.sfd: *
	  FreeSans.sfd: Merged with Gargi 1.9 and Padmaa 0.6, courtesy
	  Monika Shah and Sonali Sonania from C-DAC, Mumbai. * CREDITS:
	  Added Monika Shah and Sonali Sonania. * AUTHORS: Added Monika
	  Shah and Sonali Sonania.

2005-12-13 13:34 +0000 [r432]  peterlin:

	* ChangeLog, FreeSans.sfd,
	  FreeSerif.sfd: * FreeSans.sfd - Removed
	  Sinhala glyphs. * FreeSerif.sfd - Added Sinhala glyphs,
	  formerly in FreeSans.

2005-12-09 10:04 +0000 [r431]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd: added U+20AF, U+211E. Changed U+20AC (EURO
	  SIGN)

2005-12-09 09:34 +0000 [r430]  peterlin:

	* ChangeLog,
	  FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: * FreeSerifBold.sfd:
	  added more glyphs from Txfonts to the Arrows and Mathematical
	  Symbols ranges. * FreeSerifBoldItalic.sfd: added U+03F5 from
	  Txfonts.

2005-12-09 08:54 +0000 [r429]  peterlin:

	* tools/freefont-ttf.spec (added): Specification
	  file for building a RPM package, courtesy Rok Papez.

2005-12-08 14:58 +0000 [r428]  peterlin:

	* ChangeLog, FreeSansBold.sfd: *
	  FreeSans.sfd: added U+0567, U+056A, U+056C, U+0582.

2005-12-08 14:19 +0000 [r427]  peterlin:

	* ChangeLog, FreeSerifBold.sfd: *
	  FreeSerifBold.sfd: copied Box Drawing range from FreeSans.

2005-12-08 13:13 +0000 [r426]  peterlin:

	* ChangeLog, FreeSerifBold.sfd: *
	  FreeSerifBold.sfd: added glyphs from Txfonts to the Arrows
	  and Mathematical Symbols ranges.

2005-12-08 09:59 +0000 [r425]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd: added U+2259-225A, U+22BA, U+2308-230B,
	  U+2322-2323. Cyrillic composite characters replaced with
	  references.

2005-12-07 23:30 +0000 [r424]  peterlin:

	* ChangeLog, FreeSerifBold.sfd: *
	  FreeSerifBold.sfd: added U+025A, U+025D, U+026B, U+029B,
	  U+02AE, U+02AF, U+02DE.

2005-12-07 22:20 +0000 [r423]  peterlin:

	* ChangeLog, FreeSerifBold.sfd: *
	  FreeSerifBold.sfd: updated Hebrew part with Drugulin font
	  from the Culmus project.

2005-12-07 20:54 +0000 [r422]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd: added U+207A-207C, U+208A-208C, U+2215-2216.

2005-12-07 15:12 +0000 [r421]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd: added U+ U+2320 TOP HALF INTEGRAL, U+23AE
	  INTEGRAL EXTENSION, U+2321 BOTTOM HALF INTEGRAL (bug #13370).

2005-12-06 23:17 +0000 [r420]  peterlin:

	* ChangeLog, FreeSerifBold.sfd: *
	  FreeSerifBold.sfd: added U+0294-0296, U+02A1-02A2. Started
	  adding "below" anchors. Performed hinting on characters that were
	  not hinted "en masse".

2005-12-06 16:02 +0000 [r419]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd: fixed some more metrics problems in the
	  Extended Greek area; performed hinting on characters that were
	  not hinted "en masse".

2005-12-06 10:52 +0000 [r418]  peterlin:

	* ChangeLog, Makefile: * Makefile:
	  clean also signature files.

2005-12-06 09:46 +0000 [r416]  peterlin:

	* ChangeLog, FreeMonoBold.sfd,
	  FreeMonoBoldOblique.sfd: *
	  FreeMonoBoldOblique.sfd, FreeMonoBold.sfd: cosmetic
	  changes; cleaning background of referenced composed characters.

2005-12-05 17:52 +0000 [r415]  teras:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Some changes to the
	  greek glyphs, mostly having to do with "tonos" (accent)

2005-12-05 16:24 +0000 [r414]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd: minor cosmetic changes.

2005-12-05 15:18 +0000 [r413]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd: adjusted widths of characters in the Extended
	  Greek range; accents are not any more considerably overhanging on
	  the left side. Added U+1EDA-1EE3, U+1EE8-1EF1.

2005-12-05 09:03 +0000 [r412]  peterlin:

	* ChangeLog, FreeSans.sfd:
	  FreeSans.sfd - continued working on Extended Greek range; not
	  finished yet.

2005-12-03 11:15 +0000 [r411]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd: fixed combined Greek accents (bug #12800).
	  Width of characters still need to be adjusted as in FreeSerif.

2005-12-03 10:56 +0000 [r410]  peterlin:

	* AUTHORS, ChangeLog,
	  CREDITS, FreeSerif.sfd: *
	  FreeSerif.sfd: fixed positions of Greek accents (bug #12798).
	  * CREDITS: Added Panayotis Katsaloulis. * AUTHORS: Added
	  Panayotis Katsaloulis.i

2005-12-03 09:25 +0000 [r409]  peterlin:

	* ChangeLog, Makefile: * Makefile:
	  minor changes; now creating also a tarfile with sfds.

2005-12-01 15:31 +0000 [r408]  peterlin:

	* ChangeLog, FreeSerifItalic.sfd:
	  * FreeSerifItalic.sfd: added U+0183, U+018C, U+01C0, U+01C1,
	  U+01C3, U+01E0, U+01E1, U+01F8, U+01F9.

2005-12-01 15:03 +0000 [r407]  peterlin:

	* ChangeLog, Makefile (added): *
	  Makefile: created a Makefile to assist building.

2005-12-01 15:00 +0000 [r406]  peterlin:

	* ChangeLog, README: README: an
	  update.

2005-12-01 14:41 +0000 [r405]  peterlin:

	* ChangeLog, COPYING (added): *
	  COPYING: added GNU General Public License, version 2.

2005-12-01 12:40 +0000 [r404]  peterlin:

	* ChangeLog, tools/GenerateTrueType
	  (added): * tools/GenerateTrueType: wrote a FontForge script for
	  conversion to TrueType.

2005-12-01 11:07 +0000 [r403]  peterlin:

	* ChangeLog, FreeSerifItalic.sfd,
	  FreeSerif.sfd: * FreeSerif.sfd: merged
	  with SolaimanLipi Bangla OpenType font from www.ekushey.org,
	  courtesy Solaiman Karim. * FreeSerifItalic.sfd: merged with
	  SolaimanLipi Bangla OpenType font from www.ekushey.org, slanted
	  by 15.5 degrees.

2005-12-01 10:15 +0000 [r402]  peterlin:

	* AUTHORS, ChangeLog,
	  CREDITS, FreeSans.sfd,
	  FreeSansOblique.sfd: * FreeSans.sfd:
	  merged with Rupali Bangla OpenType font from www.ekushey.org *
	  FreeSansOblique.sfd: merged with Rupali Bangla OpenType font
	  from www.ekushey.org, slanted by 12 degrees. * CREDITS: added
	  Solaiman Karim * AUTHORS: added Solaiman Karim

2005-11-30 14:42 +0000 [r401]  peterlin:

	* AUTHORS, ChangeLog,
	  CREDITS, FreeSerif.sfd: *
	  FreeSerif.sfd: merged with the Rachana Normal. * AUTHORS:
	  added K.H. Hussain and R. Chitrajan * CREDITS: added K.H. Hussain
	  and R. Chitrajan

2005-11-23 22:37 +0000 [r400]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd - added U+01A0-01A1, U+01AF-01B0, U+026E,
	  U+028F, U+0291, U+02A3-02A5, U+031B. Modified U+0198.

2005-11-23 09:08 +0000 [r399]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd - cleaned some background images.

2005-11-22 22:20 +0000 [r398]  peterlin:

	* ChangeLog, FreeSans.sfd,
	  FreeSansBold.sfd: * FreeSans.sfd,
	  FreeSansBold.sfd - added U+0263.

2005-11-22 15:51 +0000 [r397]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd - added U+2591-25A1, U+25A3-25A5, U+25AA,
	  U+25AC.

2005-11-22 09:34 +0000 [r396]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd - added U+2504-250B.

2005-11-21 23:12 +0000 [r395]  peterlin:

	* ChangeLog, FreeSansBold.sfd: *
	  FreeSansBold.sfd - added U+0574, U+0576. Removed overlaps.

2005-11-21 22:47 +0000 [r394]  peterlin:

	* ChangeLog,
	  FreeSerifBoldItalic.sfd: *
	  FreeSerifBoldItalic.sfd - applied the sequence suggested by
	  Werner Lemberg for reducing redundant points. Added a couple of
	  glyphs in the IPA Extensions region.

2005-11-21 15:49 +0000 [r393]  peterlin:

	* ChangeLog, FreeMono.sfd,
	  FreeMonoOblique.sfd: * FreeMono.sfd -
	  corrected positions of some Greek diacritics on page 0x1F. *
	  FreeMonoOblique.sfd - working on bringing it in sync with
	  FreeMono.sfd.

2005-11-20 22:56 +0000 [r392]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd - added U+02AA-02AC, U+02B0-02B2.

2005-11-19 22:59 +0000 [r391]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd - added U+01B7-01B9, U+0196, U+019A, U+01C3,
	  U+0224-0225, U+025E, U+029A, U+2422. Changed U+0184-0185, U+0192,
	  U+01B4, U+0282, U+0284.

2005-11-18 22:44 +0000 [r390]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd - started Box Drawing area.

2005-11-18 09:30 +0000 [r389]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd - added U+02EE, U+207F.

2005-11-17 22:06 +0000 [r388]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd - removed overlaps in Latin Extended-B and IPA
	  Extensions ranges.

2005-11-17 12:59 +0000 [r387]  peterlin:

	* ChangeLog, FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: *
	  FreeMonoOblique.sfd, FreeMonoBold.sfd,
	  FreeMonoBoldOblique.sfd, FreeSansOblique.sfd,
	  FreeSansBold.sfd, FreeSansBoldOblique.sfd,
	  FreeSerifItalic.sfd, FreeSerifBold.sfd,
	  FreeSerifBoldItalic.sfd - added U+FFFD.

2005-11-17 12:33 +0000 [r386]  peterlin:

	* ChangeLog,
	  FreeSerifBoldItalic.sfd: *
	  FreeSerifBoldItalic.sfd - added U+1EDA-1EE3, U+1EE8-1EF1,
	  U+2190-219B, U+219E-21A8, U+21B9-21BA, U+21C4-21CA, U+21E4-21E5,
	  U+2669-266F. MES-1 compliant.

2005-11-17 10:43 +0000 [r385]  peterlin:

	* ChangeLog, FreeSerifItalic.sfd:
	  * FreeSerifItalic.sfd - added U+018B, U+025C, U+0265, U+026F,
	  U+0279, U+0287, U+028C-028E, U+029E.

2005-11-17 10:23 +0000 [r384]  peterlin:

	* ChangeLog, FreeSerifItalic.sfd:
	  * FreeSerifItalic.sfd - added glyphs from the Omega project
	  to Latin Extended-B, IPA Extensions and Greek ranges.

2005-11-17 09:31 +0000 [r383]  peterlin:

	* ChangeLog,
	  FreeSerifBoldItalic.sfd: *
	  FreeSerifBoldItalic.sfd - added glyphs from the Omega project
	  to Latin Extended-B, IPA Extensions and Greek ranges.

2005-11-17 09:09 +0000 [r382]  peterlin:

	* ChangeLog, FreeSerifBold.sfd: *
	  FreeSerifBold.sfd - added glyphs from the Omega project to
	  Latin Extended-B, IPA Extensions and Greek ranges.

2005-11-16 23:17 +0000 [r381]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd - added a handful of letters in the Latin
	  Extended-B and IPA Extension ranges from the Omega font
	  collection.

2005-11-16 22:37 +0000 [r380]  peterlin:

	* ChangeLog, FreeSerifItalic.sfd:
	  * FreeSerifItalic.sfd - started added accent anchors. Added a
	  handful of Greek letters from Omega font collection.

2005-11-16 15:41 +0000 [r379]  peterlin:

	* ChangeLog, FreeSerifItalic.sfd:
	  * FreeSerifItalic.sfd - applied the sequence suggested by
	  Werner Lemberg for reducing redundant points

2005-11-16 15:02 +0000 [r378]  peterlin:

	* papers/eurotex2003/freefont.bib,
	  papers/eurotex2003/freefont.tex: Changed files
	  sent back by Karl Berry. The text should now exactly match the
	  paper published in TUGboat 24(2003)545-549.

2005-11-16 14:49 +0000 [r377]  peterlin:

	* ChangeLog, FreeSans.sfd,
	  FreeSansOblique.sfd: * FreeSansOblique.sfd
	  - applied the sequence suggested by Werner Lemberg for reducing
	  redundant points. * FreeSans.sfd - changed U+01A5.

2005-11-16 13:23 +0000 [r376]  moyogo:

	* ChangeLog, FreeSerifItalic.sfd,
	  FreeSerif.sfd: fixed latin gamma and baby
	  gamma in Serif; added Ezhes variations to SerifItalic

2005-11-16 12:50 +0000 [r375]  peterlin:

	* ChangeLog, FreeMonoOblique.sfd:
	  * FreeMonoOblique.sfd - applied the sequence suggested by
	  Werner Lemberg for reducing redundant points. Added U+F6BE.

2005-11-16 12:28 +0000 [r374]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd - added U+0255, U+025A, U+025D, U+025F,
	  U+0262-0263, U+026B-026C, U+0274, U+0276-0277, U+028F, U+0291,
	  U+029D.

2005-11-16 10:36 +0000 [r373]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd - replaced Greek letters with references, where
	  applicable. Added U+03D7, U+03F0-03F2.

2005-11-16 09:39 +0000 [r372]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd - Made small Greek letters the same height as
	  Latin and Cyrillic ones and replaced them with references, where
	  applicable.

2005-11-15 23:09 +0000 [r371]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd - applied the sequence suggested by Werner
	  Lemberg for reducing redundant points. Replaced accented glyphs
	  in the Latin-1 and Latin Extended-A areas with references. Made
	  capital Greek letters the same height as Latin and Cyrillic ones
	  and replaced them with references, where applicable.

2005-11-15 19:21 +0000 [r370]  moyogo:

	* ChangeLog, FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: fixed U+026A, to
	  prevent confusion with U+0069 when accented

2005-11-15 12:18 +0000 [r369]  peterlin:

	* ChangeLog, FreeMonoBold.sfd,
	  FreeMonoBoldOblique.sfd: *
	  FreeMonoBold.sfd - corrected Greek tonos (slanted instead of
	  a vertical line). * FreeMonoBoldOblique.sfd - applied the
	  sequence suggested by Werner Lemberg for reducing redundant
	  points. Replaced accented glyphs in the Latin-1 and Latin
	  Extended-A areas with references.

2005-11-14 23:20 +0000 [r368]  peterlin:

	* ChangeLog, FreeMonoBold.sfd: *
	  FreeMonoBold.sfd - applied the sequence suggested by Werner
	  Lemberg for reducing redundant points. Replaced accented glyphs
	  in the Latin-1 and Latin Extended-A areas with references.

2005-11-14 22:44 +0000 [r367]  peterlin:

	* ChangeLog, FreeMono.sfd: *
	  FreeMono.sfd - applied the sequence suggested by Werner
	  Lemberg for reducing redundant points. Replaced accented glyphs
	  in the Greek and Cyrillic areas with references.

2005-11-14 13:43 +0000 [r366]  peterlin:

	* ChangeLog,
	  FreeSansBoldOblique.sfd: *
	  FreeSansBoldOblique.sfd - added U+0180, U+0184, U+0185,
	  U+0195, U+01A0-01A2, U+01AF-01B0, U+025E, U+026E, U+0292,
	  U+0294-0296, U+029A, U+02A1, U+2126-2127, U+2190-219B,
	  U+219E-21A8, U+21C4-21CA, U+2669-266F. MES-1 compliant.

2005-11-14 12:55 +0000 [r365]  peterlin:

	* ChangeLog,
	  FreeSansBoldOblique.sfd: *
	  FreeSansBoldOblique.sfd - applied the sequence suggested by
	  Werner Lemberg for reducing redundant points. Replaced accented
	  glyphs in the Latin-1 area with references.

2005-11-14 09:07 +0000 [r364]  peterlin:

	* ChangeLog, FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Added 2005 in
	  copyright info.

2005-11-13 23:19 +0000 [r363]  peterlin:

	* ChangeLog, FreeSansBold.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: * FreeSerif.sfd -
	  applied the sequence suggested by Werner Lemberg for reducing
	  redundant points. * FreeSansBold.sfd - added U+219A, U+219B,
	  U+2669-266F. * FreeSerifBold.sfd - added U+2669-266F.

2005-11-12 22:31 +0000 [r362]  peterlin:

	* ChangeLog, FreeSerifBold.sfd: *
	  FreeSansBold.sfd - added U+0180, U+0181, U+0183, U+0187,
	  U+0188, U+018A, U+018C, U+018D, U+0193, U+019C, U+01A0, U+01A1,
	  U+01AC, U+01AF, U+01B0, U+025C, U+0260, U+026E, U+0277, U+0281,
	  U+0284.

2005-11-11 22:50 +0000 [r361]  peterlin:

	* ChangeLog, FreeSerifBold.sfd: *
	  FreeSerifBold.sfd - applied the sequence for reducing
	  redundant points, suggested by Werner Lemberg. Added U+01A5,
	  U+02A0, U+2190-219B, U+219E-21A8, U+21B8, U+21B9, U+21C4-21CA,
	  U+21E4, U+21E5.

2005-11-11 15:52 +0000 [r360]  peterlin:

	* ChangeLog, FreeMono.sfd: *
	  FreeMono.sfd - corrected Greek letters (using tonos instead
	  of a vertical line). Added U+026E, U+F6BE. Accented characters in
	  Latin 1, Latin Extended A and partly Latin Extended B replaced by
	  references.

2005-11-11 15:03 +0000 [r359]  peterlin:

	* ChangeLog, FreeMono.sfd: *
	  FreeMono.sfd - applied the sequence for reducing redundant
	  points, suggested by Werner Lemberg.

2005-11-11 10:40 +0000 [r358]  peterlin:

	* ChangeLog, FreeSansBold.sfd: *
	  FreeSansBold.sfd - added U+195, U+1A6, U+025E, U+026E,
	  U+029A, U+0313, U+0314, U+0342, U+0344, U+0345. Started adding
	  accent anchors.

2005-11-10 22:56 +0000 [r357]  peterlin:

	* ChangeLog, FreeSansBold.sfd: *
	  FreeSansBold.sfd - replaced glyphs with references in the
	  Cyrillic area. Removed U+04A8, U+04A9. Added U+04C5, U+04C6,
	  U+04C9, U+04CA, U+04CD, U+04CE, U+0535, U+053F, U+0546, U+0565,
	  U+0584, U+0587, U+0589.

2005-11-10 22:11 +0000 [r356]  peterlin:

	* ChangeLog, FreeSansOblique.sfd:
	  * FreeSansOblique - changed U+0192, U+01A5; added
	  U+01C0-01C3.

2005-11-10 17:01 +0000 [r355]  moyogo:

	* ChangeLog, FreeSans.sfd,
	  FreeSansOblique.sfd: added a couple of IPA
	  characters to FreeSans and FreeSansOblique

2005-11-10 15:53 +0000 [r354]  peterlin:

	* ChangeLog, FreeSansBold.sfd: *
	  FreeSansBold.sfd - applied the sequence for reducing
	  redundant points, suggested by Werner Lemberg. Added
	  automatically constructed accented characters in page 0x1E.

2005-11-10 13:28 +0000 [r353]  peterlin:

	* ChangeLog, FreeSansBold.sfd: *
	  FreeSansBold.sfd - added U+00A0, U+00AD, U+0531, U+2126,
	  U+2190-2199, U+219E-21A8, U+21C4-21CA.

2005-11-10 10:07 +0000 [r352]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd - added U+01A6. Simplified outlines in the
	  ASCII range.

2005-11-09 22:42 +0000 [r351]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd - added U+0184, U+0185, U+018D, U+0195, U+0197,
	  U+019A, U+019B, U+01A0, U+01A1, U+01AC, U+01B5, U+01B6, U+01C0,
	  U+01C1, U+01C3, U+01F6, U+0294-0296, U+1E9A, U+1EDA-1EE3,
	  U+1EE8-1EF1.

2005-11-09 15:41 +0000 [r350]  peterlin:

	* ChangeLog, FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd: *
	  FreeSerifItalic.sfd - changed U+03D5. *
	  FreeSerifBoldItalic.sfd - changed U+03C6; added U+2070,
	  U+2075-2079, U+207F, U+2080, U+2085-2089, U+2155-217F.

2005-11-09 14:32 +0000 [r349]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd - added U+1EA2, U+1EA3, U+1EA8, U+1EA9, U+1EB2,
	  U+1EB3, U+1EBA, U+1EBB, U+1EC2, U+1EC3, U+1EC8, U+1EC9, U+1ECE,
	  U+1ECF, U+1ED4, U+1ED5, U+1EE6, U+1EE7, U+1EF6, U+1EF7, U+220A,
	  U+220B, U+220D, U+2272, U+2273, U+2282, U+2283.

2005-11-09 12:57 +0000 [r348]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd - added U+0183, U+018C.

2005-11-07 21:00 +0000 [r347]  peterlin:

	* FreeSansBold.sfd: FreeSansBold.sfd - added
	  U+0562, U+056D. U+0575.

2005-11-07 11:39 +0000 [r346]  peterlin:

	* ChangeLog, FreeMono.sfd: *
	  FreeSansBold.sfd - added U+0562, U+056D. U+0575. *
	  FreeMono.sfd - added U+0589.

2005-11-06 22:20 +0000 [r345]  peterlin:

	* ChangeLog, FreeMono.sfd,
	  FreeMonoBold.sfd: * FreeMono.sfd - added
	  U+2227, U+2228, U+2262. Corrected U+2299-229D in response to bug
	  report http://bugs.debian.org/cgi-bin/bugreport.cgi?bug=276121 *
	  FreeMonoBold.sfd - added U+2010, U+2012 in response to bug
	  report http://bugs.debian.org/cgi-bin/bugreport.cgi?bug=289032
	  Swapped U+03C6 (Greek small letter phi) and U+03D5 (Greek phi
	  symbol) in order to conform to Unicode standard. Simplified glyph
	  shapes in ASCII range. Started adding "above" and "below"
	  anchors.

2005-11-06 20:47 +0000 [r344]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd - added U+0278, U+03D5, U+2248. Corrected
	  U+2071, U+222E, U+2242, U+2243 in response to bug reports
	  http://bugs.debian.org/cgi-bin/bugreport.cgi?bug=276118
	  http://bugs.debian.org/cgi-bin/bugreport.cgi?bug=276120

2005-11-05 23:18 +0000 [r343]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd - accented letters in Latin Extended-A replaced
	  by references wherever possible. * FreeSerif.sfd - added
	  U+0180, U+0181, U+0187, U+0188, U+018A, U+0193, U+019C, U+01A4,
	  U+01A5, U+01A7, U+01A8, U+01AF, U+01B0, U+026E, U+0270, U+0278,
	  U+0280, U+0281, U+028B, U+0299, U+029C, U+029F.

2005-11-03 09:32 +0000 [r342]  peterlin:

	* ChangeLog, FreeSansBold.sfd:
	  FreeSansBold.sfd - cleaner Arabic outlines. Added U+01E4,
	  U+01E5.

2005-11-02 23:25 +0000 [r341]  peterlin:

	* ChangeLog, FreeSans.sfd,
	  FreeSansBold.sfd: * FreeSansBold.sfd -
	  added U+0180, U+0184, U+0185, U+0192, U+019B, U+01A0-01A2,
	  U+01AF, U+01B0, U+01EE, U+01EF, U+0292, U+0294-0296, U+02A1,
	  U+0532, U+054C, U+057C, U+222B. Changed U+014B, U+01A5, U+01B4,
	  U+03BB. * FreeSans.sfd - added U+04C5, U+04C6, U+04C9,
	  U+04CA, U+04D, U+04CE.

2005-11-02 09:52 +0000 [r340]  peterlin:

	* ChangeLog, FreeMono.sfd: *
	  FreeMono.sfd - swapped U+03C6 (Greek small letter phi) and
	  U+03D5 (Greek phi symbol) in order to conform to Unicode
	  standard. Added U+04C5, U+04C6, U+04C9, U+04CA, U+04D, U+04CE.

2005-11-02 09:11 +0000 [r339]  peterlin:

	* ChangeLog, FreeSansBold.sfd: *
	  FreeSansBold.sfd - started Armenian; added U+0538, U+0542,
	  U+0544, U+0548, U+054D, U+054F, U+0550, U+0553, U+0555, U+0561,
	  U+0563, U+0564, U+0566, U+0568 U+056B, U+056F, U+0570, U+0572,
	  U+0578, U+057A, U+057D-057F, U+0580, U+0581, U+0583, U+0585.

2005-11-01 22:12 +0000 [r338]  peterlin:

	* ChangeLog, FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd: *
	  FreeSansBold.sfd - modified U+019C. *
	  FreeSansBoldOblique.sfd - added U+00A0, U+00AD, U+019C,
	  U+01B7, U+01B8, U+0275, U+0278, U+0298, U+2012, U+2015,
	  U+2070-207F, U+2080-208E, U+2153-217F, U+2213, U+2215.

2005-10-31 11:37 +0000 [r337]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd - added U+0199, U+01AB, U+0265, U+0282, U+0288,
	  U+028C-028E, U+0290, U+029E, U+02A0.

2005-10-28 23:13 +0000 [r336]  peterlin:

	* ChangeLog, FreeSerifBold.sfd:
	  FreeSerifBold.sfd - added U+0199, U+019B, U+01B8, U+01B9,
	  U+01BE, U+01C0, U+0262, U+0274, U+0278, U+0280, U+028F, U+0298,
	  U+0299, U+029C, U+029E, U+029F, U+2012, U+2015, U+2016, U+2129,
	  U+2217.

2005-10-28 12:20 +0000 [r335]  peterlin:

	* ChangeLog, FreeSerifBold.sfd:
	  FreeSerifBold.sfd - added U+2070, U+2075-2079, U+2080,
	  U+2085-2089, U+2153-215E, U+2113-2115, U+2119.

2005-10-28 09:03 +0000 [r334]  peterlin:

	* ChangeLog, FreeSerifBold.sfd:
	  FreeSerifBold.sfd - added U+019E, U+01AB, U+01AD, U+01B1,
	  U+0256, U+025F, U+0265, U+0269, U+026F, U+0270, U+0279-027F,
	  U+0282, U+0287, U+0288, U+028C-028E, U+0290.

2005-10-27 22:26 +0000 [r333]  peterlin:

	* ChangeLog, FreeSerifBold.sfd:
	  FreeSerifBold.sfd - added U+018B, U+018E, U+018F, U+0191,
	  U+019D, U+01A7, U+01A8, U+01AE, U+0253, U+0266, U+0267, U+026A,
	  U+0271-0273, U+0283, U+0285.

2005-10-27 14:28 +0000 [r332]  peterlin:

	* ChangeLog, FreeSansBold.sfd:
	  FreeSansBold.sfd - added subscripts and superscripts
	  (U+2070-208F), completed fractions (U+2152-215F) and Roman
	  numerals (U+2160-217F).

2005-10-27 13:44 +0000 [r331]  peterlin:

	* ChangeLog, FreeSerifItalic.sfd:
	  FreeSerifItalic.sfd - minor cleanup in the superscript range
	  (U+2070-2079).

2005-10-27 09:42 +0000 [r330]  peterlin:

	* ChangeLog, FreeSans.sfd:
	  FreeSans.sfd - added U+018D, U+0194, U+019B, U+019C, U+01B5,
	  U+01B6, U+0295, U+0296, U+029B, U+02A2, U+0472, U+0473, U+2114,
	  U+2119.

2005-10-26 21:31 +0000 [r329]  peterlin:

	* ChangeLog, FreeSans.sfd:
	  FreeSans.sfd - Added U+01C0-01C2, U+0276, U+0292, U+0298.
	  Changed U+0251, U+0294, U+02A1.

2005-10-26 14:45 +0000 [r328]  peterlin:

	* ChangeLog, FreeSansBold.sfd: *
	  FreeSansBold.sfd - Added U+2010-2012, U+2015, U+2032, U+203C,
	  U+2047-2049.

2005-10-26 13:56 +0000 [r327]  peterlin:

	* ChangeLog, FreeSansBold.sfd: *
	  FreeSansBold.sfd - added U+0278, U+0298. Cleaned up outlines
	  of most Greek letters.

2005-10-26 12:52 +0000 [r326]  peterlin:

	* ChangeLog, FreeMono.sfd: *
	  FreeMono.sfd - a try to imitate Denis' work on adding anchors
	  by adding "above" anchor to a couple of basic Latin characters.

2005-10-26 11:57 +0000 [r325]  peterlin:

	* ChangeLog, FreeMono.sfd:
	  FreeMono.sfd - added U+2011, U+2012, U+203B, U+204A, U+2071,
	  U+2129, U+2232, U+2233. Changed and/or corrected U+2106, U+211E,
	  U+2126, U+2127, U+2153-215F, U+2202.

2005-10-26 08:18 +0000 [r324]  peterlin:

	* ChangeLog, FreeSans.sfd: *
	  FreeSans.sfd - added "above" anchors to selected Cyrillic
	  characters. Added U+0294, U+02A1.

2005-10-25 14:29 +0000 [r323]  peterlin:

	* ChangeLog, FreeSansOblique.sfd:
	  FreeSansOblique.sfd - added U+00A0, U+2011-2012, U+2015,
	  U+2070, U+2071, U+2074-2079, U+2080-2089, U+2126, U+2153-215F,
	  U+2190-2195, U+2215, U+266A. FreeSansOblique is now MES-1
	  compliant.

2005-10-25 14:00 +0000 [r322]  peterlin:

	* ChangeLog, FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMonoBold.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd:
	  FreeSansOblique.sfd, FreeSans.sfd, FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd, FreeMonoBoldOblique.sfd,
	  FreeMonoBold.sfd, FreeSerifItalic.sfd,
	  FreeSerifBold.sfd FreeSerifBoldItalic.sfd - brought in
	  sync with Valek Filipov's urw-fonts-1.0.7pre41

2005-10-25 12:13 +0000 [r321]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd - added U+019E, U+01AD, U+01B8, U+01B9, U+0253,
	  U+0256, U+0257, U+025C, U+0260, U+0266, U+0267, U+0269, U+026D,
	  U+0271-0273, U+0279-027D.

2005-10-25 09:53 +0000 [r320]  peterlin:

	* ChangeLog,
	  FreeSerifBoldItalic.sfd:
	  FreeSerifBoldItalic.sfd - added U+00A0, U+00AD, U+2010-2012,
	  U+2015, U+2032-2034, U+203C, U+2047-204A, U+2074, U+2081-2084,
	  U+2126, U+2153, U+2154, U+215F, U+2215. Corrected positions of
	  diacritics on U+0200-0217.

2005-10-25 09:06 +0000 [r319]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd - added U+0191, U+019D, U+01AE, U+027E, U+027F,
	  U+0283, U+0285.

2005-10-25 08:12 +0000 [r318]  peterlin:

	* ChangeLog, FreeSerifItalic.sfd:
	  FreeSerifItalic.sfd - added U+00A0, U+00AD, U+2010-2012,
	  U+2015, U+2126, U+2127, U+2153-215E, U+2160-217F, U+2190-2193,
	  U+2669-266F. FreeSerifItalic is now MES-1 compliant.

2005-10-24 19:34 +0000 [r317]  moyogo:

	* ChangeLog, FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: added ccmp feature
	  for i and j to be substituted with dotless i or j when followed
	  by above diacritic

2005-10-24 15:01 +0000 [r316]  peterlin:

	* ChangeLog, FreeSans.sfd:
	  FreeSans.sfd - added U+2011, U+2012, U+2015. FreeSans is now
	  MES-1 conformant.

2005-10-23 12:29 +0000 [r314-315]  moyogo:

	* FreeSans.sfd: added above, below, abovemk and
	  belowmk anchors for diacritics placement; cleared U+0084

	* ChangeLog, FreeSans.sfd: added
	  above, below, abovemk and belowmk anchors for diacritics
	  placement

2005-10-21 12:18 +0000 [r313]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd - added U+02B9, U+02BA, U+02CD, U+2017, U+2036,
	  U+2037, U+203C, U+203E, U+2047-204A.

2005-10-20 12:04 +0000 [r312]  peterlin:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd - added U+00A0, U+00AD, U+0182, U+0189, U+018B,
	  U+018E, U+018F, U+0192, U+019F, U+01A9, U+01B1, U+01B7, U+01DD,
	  U+2010-2013, U+2015. FreeSerif is now MES-1 conformant.

2005-10-20 11:13 +0000 [r311]  peterlin:

	* ChangeLog, FreeSerifBold.sfd: *
	  FreeSerifBold.sfd - added U+0182, U+0189, U+0192, U+019F,
	  U+01A9, U+01B7, U+01C4-01CC, U+01E0-1E2, U+01F0-01F3, U+F6BE.
	  Corrected position of diacritics on U+0200-0217.

2005-10-19 13:03 +0000 [r310]  moyogo:

	* ChangeLog, FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: * FreeSerif.sfd - added
	  U+0268, U+026A, U+0289, U+0292; and anchor "above" to more base
	  glyphs. * FreeSerifBold.sfd, FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd - added U+0250-0252, U+0258-0259,
	  U+0261, U+0268, U+026A, U+0279, U+0289 * FreeSerifBold.sfd -
	  added anchor "above" to marks U+0300-0314, and to base glyphs
	  (vowels).

2005-10-18 20:36 +0000 [r309]  moyogo:

	* ChangeLog, FreeSerif.sfd: *
	  FreeSerif.sfd - added anchor "above" to marks U+0300-0314,
	  and bases vowel of the U+0041-007A range, U+00E6, U+0186, U+0190,
	  U+0254 and U+025B; fixed Latin-1 Supplement block accented glyphs
	  to use references.

2005-10-17 09:25 +0000 [r308]  peterlin:

	* ChangeLog, FreeSansBold.sfd:
	  FreeSansBold.sfd - added U+01B7, U+01B8, U+0275.

2005-10-16 18:06 +0000 [r307]  moyogo:

	* ChangeLog, FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: * FreeSans.sfd,
	  FreeSansOblique.sfd - added some Latin Extended-B African
	  letters: U+0181, U+018A, U+0197-0198, U+01A4, U+01AC, U+01B1,
	  U+01B3-01B4; * FreeSansBold.sfd, FreeSansBoldOblique.sfd
	  - added Latin Extended-B U+0187, 018E-018F, U+0191, U+0193,
	  U+0197-0199, U+019D-019F, U+01AB-01AE; correcting width of
	  non-space Combining Diacrtical Marks; added more glyphs to IPA
	  Extensions to match non Bold * FreeSansBoldOblique.sfd -
	  added many accented glyphs

2005-10-15 20:59 +0000 [r306]  moyogo:

	* ChangeLog, FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: * FreeSans.sfd,
	  FreeSansOblique.sfd - added IPA Extensions
	  U+0262,U+0274,U+0280-0281, U+0299, U+029F, and Spacing Modifier
	  Letters U+02C9-02CB; fixed U+0287,029E height to baseline; added
	  stroke to U+0268 * FreeSansOblique.sfd - fixed skew on U+027F
	  * FreeSansBold.sfd, FreeSansBoldOblique.sfd - added to
	  Latin Extended-B U+01A7-01A8, IPA Extensions U+0251-0253,
	  U+0256-0257, U+0261, U+0265-026A, U+026F-0273, U+0289,
	  U+028C-028E * FreeSansBoldOblique.sfd - added to Latin
	  extended-B U+0189, U+01A8, U+01B1, U+0283, U+02C9 and Spacing
	  Modifiers U+02C9-02CB

2005-10-14 13:47 +0000 [r304-305]  peterlin:

	* ChangeLog: FreeSansBold.sfd - Added a couple
	  of composite glyphs, mostly in the IPA and Latin Extended B
	  ranges.

	* FreeSansBold.sfd: Added a couple of composite
	  glyphs, mostly in the IPA and Latin Extended B ranges.

2005-10-13 17:58 +0000 [r303]  moyogo:

	* ChangeLog, FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: * FreeSans.sfd -
	  removed overlap and simplified U+0187, 0191, 0193, 01A5, 01AE,
	  0260, 0271, 0272, 0273, 027B; fixed diacritics placement on
	  U+0200-0217; fixed glyph for U+0283 to correct esh without
	  stroke; added U+025F and fixed U+025F from it; fixed height of
	  glyph at U+0285; arranged U+027E,027F to make more
	  distinguishable from U+0072. * FreeSansOblique.sfd - added the
	  corrected or new glyphs from FreeSans; diacritics on U+200-0217
	  will need height readjustements. * FreeSansBold.sfd,
	  FreeSansBoldOblique - added U+0186, 0190, 0250, 0254, 0258, 0259,
	  025B, 025C

2005-10-13 15:00 +0000 [r302]  moyogo:

	* FreeSerifBoldItalic.sfd: fixed open o, U+0254,
	  that was left unturned

2005-10-13 12:51 +0000 [r299-301]  peterlin:

	* FreeSerifItalic.sfd: Added glyphs
	  U+222B-U+222F, U+2320, U+2321. Fixed diacritics on U+0200-U+0217.

	* AUTHORS, ChangeLog,
	  CREDITS: Added Denis Jacquerye.

	* FreeSerif.sfd: Minor changes: U+22A2, U+22A3,
	  U+22A6, U+23AE. Added U+0250, U+0251, U+0258, U+0259, U+0275.

2005-10-13 05:58 +0000 [r298]  moyogo:

	* ChangeLog, FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: 2005-10-12 Denis Jacquerye
	  <<EMAIL>> * FreeSerif.sfd - Corrected diacritics
	  position on U+01D5-01D9,01DB,01EA-01ED,0200-0217 and U+022A. *
	  FreeSerif.sfd, FreeSerifBold.sfd,
	  FreeSerifItalic.sfd, FreeSerifBoldItalic.sfd - added
	  U+0186,0190,0254 and U+025B.

2005-10-11 15:11 +0000 [r297]  peterlin:

	* FreeSerif.sfd: Attempt to correct bug #13370:
	  INTEGRAL EXTENSION does not align with TOP/BOTTOM HALF INTEGRAL;
	  added glyph U+23AE.

2005-10-11 14:33 +0000 [r296]  peterlin:

	* FreeSerif.sfd: Fixed bug #13399 (glyphs for
	  U+0360 and U+0361 swapped).

2005-05-16 14:33 +0000 [r295]  peterlin:

	* ChangeLog, FreeMono.sfd:
	  Corrected shapes for Cross of Lorraine and Cross of Jerusalem.

2005-04-07 13:45 +0000 [r294]  peterlin:

	* notes (added),
	  notes/maintenance.txt (added): Some basic notes
	  how things work with Savannah.

2005-04-07 13:25 +0000 [r292-293]  peterlin:

	* FreeSansBold.sfd: Added some combining
	  accents, just to test a new version of FontForge.

	* ChangeLog: Minor change to FreeSansBold.

2003-11-18 16:26 +0000 [r291]  peterlin:

	* FreeSans.sfd: Started to apply Josef Segur's
	  patch.

2003-10-08 12:22 +0000 [r290]  peterlin:

	* AUTHORS, ChangeLog: Added Abbas
	  Izad.

2003-10-08 12:18 +0000 [r289]  peterlin:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeMonoOblique.sfd: Applied Josef Segur's
	  corrections from Oct. 5.

2003-10-04 17:51 +0000 [r288]  peterlin:

	* FreeSerif.sfd: A rather naive composition of
	  Arabic "accented" charachers, based on the work of Abbas Izad.

2003-10-02 20:54 +0000 [r286-287]  peterlin:

	* ChangeLog: Update.

	* FreeSerif.sfd: Abbas Izad's contributed
	  Arabic/Farsi characters added.

2003-10-02 20:09 +0000 [r284-285]  peterlin:

	* ChangeLog: Update.

	* FreeSansBoldOblique.sfd: Combining chars
	  corrected (this one escaped the batch).

2003-10-02 19:55 +0000 [r283]  peterlin:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Combining characters
	  (U+0300 - U+036F) moved left, so that they have negative
	  horizontal values and zero advance width.

2003-09-15 09:23 +0000 [r282]  peterlin:

	* FreeSerifItalic.sfd: Super- and subscripts
	  added. Those copied from Free Serif regular (0,5-9) seem a bit
	  heavier to me than 1-4 designed specially for italic. Might
	  require a further adjustment. :
	  ----------------------------------------------------------------------

2003-09-15 09:08 +0000 [r281]  peterlin:

	* FreeSerifItalic.sfd: Started working on super-
	  and subscripts.

2003-09-15 08:36 +0000 [r280]  peterlin:

	* FreeSerifBold.sfd: Started working on super-
	  and subscripts.

2003-09-12 14:12 +0000 [r279]  peterlin:

	* FreeSerif.sfd: Added three Hiragana characters
	  (U+3094, U+3099 and U+309B) and 6 Katakana characters
	  (U+30F4-U+30FC).

2003-09-12 13:53 +0000 [r278]  peterlin:

	* FreeSansBold.sfd: Cleared background
	  characters in Latin Extended-A. Added some automatically
	  constructed characters in Latin Extended-B. Started with
	  superscripts and subscripts.

2003-09-12 12:53 +0000 [r277]  peterlin:

	* FreeSans.sfd: Subscript numerals
	  (U+2080-U+2089) completed.

2003-09-12 12:40 +0000 [r276]  peterlin:

	* FreeSans.sfd: Upgraded Pfaedit to 030827. Two
	  Hiragana characters (U+3099, U+309B) and 8 Katakana characters
	  (U+30F7-U+30FE) added.

2003-06-02 05:34 +0000 [r275]  teras:

	* FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Basic ancient greek
	  support

2003-05-22 19:35 +0000 [r274]  teras:

	* FreeSansBold.sfd: Basic ancient greek support

2003-05-22 18:05 +0000 [r273]  teras:

	* FreeSans.sfd: Basic ancient greek support

2003-05-19 08:18 +0000 [r272]  peterlin:

	* ChangeLog: Upload.

2003-05-19 08:15 +0000 [r271]  peterlin:

	* FreeSerifItalic.sfd: Improved control point
	  positioning on Thai character fongman. Actually, Thai needs some
	  work here...

2003-05-19 08:09 +0000 [r270]  peterlin:

	* FreeSerif.sfd: Thai letters bo baimai and po
	  pla swapped; Thai character fongman corrected, all courtesy
	  Theppitak Karoonboonyanan.

2003-05-19 07:45 +0000 [r269]  peterlin:

	* ChangeLog: Update.

2003-05-17 00:55 +0000 [r268]  teras:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Full support of all ancient
	  greek glyphs

2003-05-16 09:41 +0000 [r267]  peterlin:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSansBold.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Saved in a new SFD
	  format with a new version of PfaEdit.

2003-05-15 12:15 +0000 [r266]  peterlin:

	* ChangeLog: Mentioned KerningNumerals.pl.

2003-05-15 12:07 +0000 [r265]  peterlin:

	* ChangeLog: Update.

2003-05-15 12:04 +0000 [r263-264]  peterlin:

	* tools/KerningNumerals.pl: Now doing the new-style
	  kerning information (KernsSLIF) as well.

	* FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Oops! It's KernsSLIF,
	  not Kerns. Corrected.

2003-05-15 11:58 +0000 [r262]  peterlin:

	* FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Completed moving
	  kerning information from ASCII numerals to kerned numerals in the
	  Adobe corporate use area (U+F6xx).

2003-05-15 10:32 +0000 [r261]  peterlin:

	* FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Automatically moved
	  kerning information from ASCII numerals with the
	  KerningNumerals.pl script.

2003-05-15 10:27 +0000 [r259-260]  peterlin:

	* FreeSansBold.sfd: Automatically moved kerning
	  information with the KerningNumerals.pl script.

	* tools/KerningNumerals.pl (added): A Perl script
	  for moving kerning information from ASCII numerals (U+0030...) to
	  characters in the Adobe corporate use area (U+F6xx).

2003-05-14 12:10 +0000 [r258]  peterlin:

	* ChangeLog: Update

2003-05-14 12:00 +0000 [r257]  peterlin:

	* FreeSans.sfd: First approximation of super-
	  and subscript numerals and vulgar fractions.

2003-05-14 10:00 +0000 [r256]  peterlin:

	* ChangeLog: Update.

2003-05-14 09:57 +0000 [r255]  peterlin:

	* FreeSerif.sfd: Super- and subscript numerals
	  complete, vulgar fractions completed and redone as references
	  rather than outlines.

2003-05-12 16:58 +0000 [r253-254]  peterlin:

	* ChangeLog: Update

	* FreeSerif.sfd: Super- and subscripts, vulgar
	  fractions.

2003-05-12 16:12 +0000 [r251-252]  peterlin:

	* ChangeLog: Update.

	* FreeSerif.sfd: Rounded to integer.

2003-05-12 15:45 +0000 [r250]  peterlin:

	* FreeSerif.sfd: Cleanup of the Cyrillic letters
	  added on March 27.

2003-05-09 09:56 +0000 [r249]  peterlin:

	* ChangeLog: Update.

2003-05-09 09:53 +0000 [r248]  peterlin:

	* FreeMonoBold.sfd: Additions in the Latin
	  Extended-B and IPA Extensions range.

2003-05-08 09:07 +0000 [r246-247]  peterlin:

	* ChangeLog: Update.

	* FreeSerifBoldItalic.sfd: Added a couple of
	  characters to the Latin Extended-B area.

2003-05-08 08:37 +0000 [r245]  peterlin:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: ASCII numerals now
	  monospaced; kerned numerals moved to Adobe corporate use area
	  (U+F6xx).

2003-05-08 07:47 +0000 [r244]  peterlin:

	* ChangeLog: Update.

2003-05-07 15:56 +0000 [r243]  peterlin:

	* FreeSans.sfd,
	  FreeSerif.sfd: Roman numerals.

2003-05-07 15:13 +0000 [r242]  peterlin:

	* FreeSans.sfd: Added characters in the Latin
	  Extended-B range.

2003-05-07 13:56 +0000 [r241]  peterlin:

	* FreeSans.sfd: Added Greek accents.

2003-05-07 13:19 +0000 [r240]  peterlin:

	* FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Automated addition of
	  accented characters.

2003-05-07 12:57 +0000 [r239]  peterlin:

	* FreeMonoBoldOblique.sfd: Additions in Latin
	  Extended-B and Basic Greek.

2003-05-07 12:13 +0000 [r237-238]  peterlin:

	* FreeMonoBoldOblique.sfd: Points rounded to
	  integer values; glyphs simplified.

	* FreeMonoBoldOblique.sfd: Cleanup (fixed
	  widths, clockwise/counter-clockwise corrections etc.)

2003-05-07 11:53 +0000 [r236]  peterlin:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd: Points rounded to integer
	  values; outlines simplified.

2003-05-07 11:48 +0000 [r234-235]  peterlin:

	* FreeSans.sfd: Rounded to integer and
	  simplified.

	* FreeSans.sfd: Cleanup (open paths fixed).

2003-05-07 11:40 +0000 [r232-233]  peterlin:

	* FreeMonoOblique.sfd: Kerning pairs removed.

	* FreeMonoOblique.sfd: Cleanup (path direction
	  etc.)

2003-05-07 10:16 +0000 [r231]  peterlin:

	* FreeMonoBold.sfd: Cleanup
	  (clockwise/counter-clockwise direction corrected).

2003-05-07 10:01 +0000 [r230]  peterlin:

	* FreeMono.sfd: Major cleanup (fixed widths,
	  open paths, clockwise/counter-clockwise directions etc.).

2003-05-06 15:54 +0000 [r229]  peterlin:

	* FreeSans.sfd: Proportional numerals don't
	  really have to be outlines - references are enough. Caught two
	  remaining numeral kerns, ",1" and ".1". Free Sans is now clean.

2003-05-06 15:39 +0000 [r228]  peterlin:

	* FreeSansBold.sfd: ASCII numerals now
	  monospaced; kerned numerals moved to Adobe corporate use area
	  (U+F6xx). Not finished yet - left kerns are done, right only up
	  to letter C!

2003-05-06 14:39 +0000 [r227]  peterlin:

	* FreeSans.sfd: ASCII numerals now monospaced;
	  kerned numerals moved to Adobe corporate use area (uniF6xx).

2003-05-06 13:09 +0000 [r226]  peterlin:

	* tools/OS2UnicodeRange (added): A simple script to
	  display OS/2 Unicode range table in TrueType fonts.

2003-05-06 11:53 +0000 [r225]  peterlin:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Updated years of FSF
	  copyleft.

2003-05-06 11:36 +0000 [r224]  peterlin:

	* FreeMonoBold.sfd: Minor changes in Latin
	  Extended-B and IPA Extensions.

2003-04-02 15:54 +0000 [r223]  peterlin:

	* papers (added), papers/eurotex2003
	  (added), papers/eurotex2003/eurotex2003.cls
	  (added), papers/eurotex2003/freefont.bib (added),
	  papers/eurotex2003/freefont.tex (added): Paper for
	  the EuroTeX 2003 conference, ENST Bretagne, Brest, June 24-27.

2003-03-27 08:40 +0000 [r222]  peterlin:

	* AUTHORS, ChangeLog,
	  CREDITS: Update.

2003-03-27 08:13 +0000 [r221]  peterlin:

	* FreeSerif.sfd: Cyrillic and Cyrillic
	  Supplement blocks brought to conformance with Unicode 3.2,
	  courtesy Daniel Shurovich Chirkov.

2003-03-19 17:31 +0000 [r220]  peterlin:

	* FreeSansOblique.sfd: Synchronized germandbls
	  (U+00DF) with the one in FreeSans.

2003-03-19 17:17 +0000 [r219]  peterlin:

	* FreeSans.sfd: Made somewhat wider germandbls
	  (U+00DF). Walter Schmidt brought up this problem with URW fonts
	  in the tex-fonts list.

2003-03-18 16:38 +0000 [r218]  peterlin:

	* FreeSans.sfd: Added Sinhala glyphs from the
	  Tipitaka project <http://www.metta.lk>, recoded to Unicode by
	  Noah Levitt.

2003-02-27 13:56 +0000 [r217]  peterlin:

	* FreeSansBold.sfd: Somewhat "cleaner" glyphs in
	  the Greek part.

2003-02-24 12:49 +0000 [r216]  peterlin:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: All numerals set to
	  equal width (556 points).

2003-02-21 16:58 +0000 [r215]  peterlin:

	* FreeSans.sfd: A nicer capital schwa.

2003-02-19 09:35 +0000 [r213-214]  peterlin:

	* ChangeLog: Update.

	* FreeSans.sfd: Minor changes on mathematics
	  operators.

2003-02-18 16:57 +0000 [r212]  peterlin:

	* FreeSans.sfd: Added two Tamil signs (virama
	  and au length mark).

2003-02-18 11:35 +0000 [r211]  peterlin:

	* ChangeLog: Update.

2003-02-18 11:28 +0000 [r210]  peterlin:

	* FreeMono.sfd: Integral signs more in the
	  spirit of Courier design.

2003-02-18 11:12 +0000 [r209]  peterlin:

	* FreeMono.sfd: Cleaned out some backgrounds.

2003-02-05 15:46 +0000 [r207-208]  peterlin:

	* ChangeLog: Update.

	* FreeSans.sfd: Added a couple of glyphs in the
	  IPA and African Latin ranges.

2003-01-30 15:55 +0000 [r206]  peterlin:

	* ChangeLog: Update.

2003-01-30 15:35 +0000 [r205]  peterlin:

	* FreeSansBold.sfd,
	  FreeSansBoldOblique.sfd: Corrected Maltese
	  hbar and Hbar.

2003-01-30 14:54 +0000 [r204]  peterlin:

	* FreeSans.sfd,
	  FreeSansOblique.sfd,
	  FreeMonoBoldOblique.sfd: Corrected Maltese
	  hbar and Hbar.

2003-01-30 14:43 +0000 [r203]  peterlin:

	* FreeMonoBold.sfd: Corrected Maltese Hbar.

2003-01-28 16:31 +0000 [r202]  peterlin:

	* FreeSerifItalic.sfd: Corrected Maltese hbar.

2002-12-19 18:31 +0000 [r201]  peterlin:

	* FreeSans.sfd: Cleaner Tamil outlines.

2002-12-18 17:38 +0000 [r199-200]  peterlin:

	* ChangeLog: Update.

	* tools/ConvertFont (added): Convert fonts from
	  PfaEdit's native format to TrueType format.

2002-12-18 17:17 +0000 [r198]  peterlin:

	* ChangeLog: Update.

2002-12-18 10:43 +0000 [r197]  peterlin:

	* FreeSans.sfd: Added (almost complete) Tamil
	  Unicode range.

2002-12-17 16:44 +0000 [r196]  peterlin:

	* FreeSans.sfd: Started adding Kannada glyphs
	  from Akruti fonts.

2002-12-17 15:56 +0000 [r195]  peterlin:

	* AUTHORS, ChangeLog,
	  CREDITS: Added M.S. Sridhar and Akruti fonts.

2002-12-17 15:42 +0000 [r194]  peterlin:

	* FreeSans.sfd: Added some Devanagari and
	  Gujarati glyphs from the Akruti fonts.

2002-12-17 10:08 +0000 [r193]  peterlin:

	* ChangeLog, CREDITS: Correct
	  spelling of Culmus project.

2002-12-12 15:09 +0000 [r192]  peterlin:

	* INSTALL (added): Installation notes for the Free
	  UCS outline fonts.

2002-12-12 08:57 +0000 [r191]  peterlin:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Checked in as v1.1.

2002-12-12 08:48 +0000 [r190]  peterlin:

	* FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Checked in as v1.1.

2002-12-06 15:13 +0000 [r189]  peterlin:

	* FreeMono.sfd: Minor correction on U+230F.

2002-12-06 15:00 +0000 [r188]  peterlin:

	* FreeMono.sfd: Various additions in the
	  U+2000-U+27FF area, courtesy Vyacheslav Dikonov.

2002-12-06 14:20 +0000 [r187]  peterlin:

	* CREDITS: Added Vyacheslav Dikonov.

2002-12-06 14:08 +0000 [r186]  peterlin:

	* AUTHORS: Added Vyacheslav Dikonov.

2002-12-06 13:59 +0000 [r185]  peterlin:

	* FreeMono.sfd: Added Runes (U+16A0...),
	  courtesy Vyacheslav Dikonov.

2002-12-06 13:43 +0000 [r184]  peterlin:

	* ChangeLog: Update.

2002-12-06 13:40 +0000 [r182-183]  peterlin:

	* FreeSans.sfd: Added Unicode Syriac glyphs,
	  courtesy Vyacheslav Dikonov.

	* FreeMono.sfd: Added Braille glyphs, courtesy
	  Vyacheslav Dikonov.

2002-11-28 10:10 +0000 [r181]  peterlin:

	* README: Resubmitted using -r1.1.

2002-11-28 10:02 +0000 [r180]  peterlin:

	* AUTHORS: Resubmitted using -r 1.1

2002-11-28 09:57 +0000 [r179]  peterlin:

	* AUTHORS: Re-submitted using CVS 1.11.2.

2002-11-28 09:41 +0000 [r178]  peterlin:

	* README: Updated text reflecting that Microsoft Web
	  fonts are no longer freely available.

2002-10-16 11:02 +0000 [r177]  peterlin:

	* FreeSerif.sfd: Corrected glyph 0x0446, thanks
	  to Reiji SUZUKI, who spotted it.

2002-10-11 16:40 +0000 [r176]  peterlin:

	* ChangeLog: Update.

2002-10-11 16:29 +0000 [r175]  peterlin:

	* ChangeLog: Update.

2002-10-11 15:46 +0000 [r174]  peterlin:

	* FreeSans.sfd: Added a handful of kern pairs
	  beyond Latin-1.

2002-10-11 14:14 +0000 [r173]  peterlin:

	* FreeSerif.sfd: Adding kern pairs manually also
	  cleans the empty glyph slots (and makes the file unreadable by
	  PfaEdit). Reintroduced them.

2002-10-11 14:01 +0000 [r172]  peterlin:

	* FreeSerif.sfd: Added about one dozen kern
	  pairs as a test.

2002-10-11 13:44 +0000 [r171]  peterlin:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Patch from Sept 23
	  introduced core dumps, as PfaEdit dutifully cleared all the empty
	  glyph slots along the way, which messed up its internal workings.
	  Re-introduced all the empty glyph slots.

2002-09-23 16:00 +0000 [r170]  peterlin:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: At some point I lost the
	  kerning information. First attempt to reconstruct it from AFM
	  files.

2002-09-11 14:39 +0000 [r169]  peterlin:

	* FreeMono.sfd: Swapped incorrect "dialytika and
	  oxia" and "dialytika and varia". Made "tonos" equal to "oxia".
	  Started doing base Greek with new tonos.

2002-09-11 11:31 +0000 [r168]  peterlin:

	* FreeMonoBold.sfd: Hebrew parts in sync with
	  Culmus 0.6.

2002-09-11 11:26 +0000 [r167]  peterlin:

	* ChangeLog: Update.

2002-09-11 09:53 +0000 [r166]  peterlin:

	* FreeMono.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Hebrew parts in sync
	  with Culmus 0.6.

2002-09-11 09:16 +0000 [r165]  peterlin:

	* ChangeLog: Update.

2002-09-11 09:13 +0000 [r164]  peterlin:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: * Added Danilo
	  Segan's Serbian Cyrillic glyphs. * Updated Hebrew part to comply
	  with Culmus 0.6.

2002-09-10 08:21 +0000 [r163]  peterlin:

	* FreeSans.sfd: Started working on IPA range.

2002-09-09 16:01 +0000 [r161-162]  peterlin:

	* ChangeLog: Update.

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Updated Cyrillic part
	  to match Filippov's 1.0.7pre14.

2002-09-09 15:39 +0000 [r160]  peterlin:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Updated Cyrillic part
	  to match Filippov's 1.0.7pre14.

2002-09-09 14:33 +0000 [r158-159]  peterlin:

	* ChangeLog: Update.

	* FreeSansOblique.sfd: Added Stepanyan's
	  Armenian glyphs from FreeSans, skewed for 12 degrees.

2002-09-06 14:16 +0000 [r157]  peterlin:

	* AUTHORS, CREDITS: Changed @ in
	  email addresses to " AT ".

2002-09-06 13:43 +0000 [r155-156]  peterlin:

	* ChangeLog: A minor correction.

	* ChangeLog: Update.

2002-09-06 13:18 +0000 [r154]  peterlin:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Added Maxim Iorsh's
	  Hebrew glyphs (still considered experimental by their author, but
	  much better than previous ones).

2002-08-28 15:31 +0000 [r152-153]  peterlin:

	* AUTHORS, CREDITS: Added Maxim
	  Iorsh.

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Added Maxim Iorsh's
	  Hebrew glyphs.

2002-07-26 10:21 +0000 [r151]  peterlin:

	* ChangeLog: Update.

2002-07-26 10:17 +0000 [r150]  peterlin:

	* FreeMono.sfd: Some minor changes.

2002-06-11 19:28 +0000 [r148-149]  peterlin:

	* ChangeLog: Update.

	* FreeMono.sfd: Applied Michalis Kabrianis's
	  patch concerning perispomeni in Greek politoniko.

2002-05-23 11:25 +0000 [r147]  peterlin:

	* ChangeLog: Update.

2002-05-23 11:20 +0000 [r146]  peterlin:

	* FreeMono.sfd: Applied Michalis Kabrianis's
	  patch which corrects psili. Also some working variants of
	  characters in the IPA range.

2002-05-16 14:44 +0000 [r145]  peterlin:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoBoldOblique.sfd: Added LangName
	  information.

2002-05-15 13:23 +0000 [r144]  peterlin:

	* ChangeLog: Update.

2002-05-15 13:14 +0000 [r143]  peterlin:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Deleted explicit ".notdef"
	  characters.

2002-05-14 08:59 +0000 [r142]  peterlin:

	* FreeSansBoldOblique.sfd: Filled-in the missing
	  TTFWidth and TTFWeight values.

2002-05-14 08:51 +0000 [r141]  peterlin:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Correctly formed
	  Panose and LineGap lines.

2002-05-09 18:13 +0000 [r140]  peterlin:

	* ChangeLog: Update.

2002-05-09 17:42 +0000 [r139]  peterlin:

	* FreeSans.sfd: Added some more composite
	  glyphs. Croatian ligatures better kerned.

2002-05-09 17:10 +0000 [r138]  peterlin:

	* FreeSans.sfd: Added diacritics. Added glyphs
	  to the Latin Extenede-B area.

2002-05-07 16:13 +0000 [r137]  peterlin:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Updated Panose and LangName
	  information in the TTF header.

2002-05-07 15:40 +0000 [r136]  peterlin:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Updated Panose and
	  LangName information in the TTF header.

2002-05-07 15:28 +0000 [r135]  peterlin:

	* FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd: Updating Panose and
	  LangName information in the font header.

2002-04-30 12:27 +0000 [r134]  peterlin:

	* ChangeLog: Update

2002-04-30 11:21 +0000 [r133]  peterlin:

	* FreeMonoBold.sfd: Several minor changes (lower
	  carons etc.)

2002-04-30 10:14 +0000 [r132]  peterlin:

	* FreeMonoBold.sfd: Working on Greek small
	  letters.

2002-04-29 19:44 +0000 [r131]  peterlin:

	* ChangeLog: Update.

2002-04-29 19:38 +0000 [r129-130]  peterlin:

	* FreeMonoBoldOblique.sfd: Started adding Greek.

	* FreeMono.sfd,
	  FreeMonoBold.sfd: Harmonizing Greek with
	  Latin. Done with capitals.

2002-04-29 18:08 +0000 [r128]  peterlin:

	* FreeMonoBold.sfd: Added glyphs in the
	  Geometrical Shapes and Miscellaneous Symbols area.

2002-04-29 17:35 +0000 [r127]  peterlin:

	* FreeMono.sfd: Added one glyph to the
	  Geometrical Shapes area, which is now completed; added three
	  glyphs to the Miscellaneous Symbols area.

2002-04-29 10:34 +0000 [r126]  peterlin:

	* FreeMono.sfd: Erased explicit ".notdef"
	  character (U+0000). Hopefully this will now stop getting PfaEdit
	  confused about the "post" table.

2002-04-26 15:41 +0000 [r124-125]  peterlin:

	* ChangeLog: Update.

	* FreeSans.sfd: Adjusted accent positions on
	  several glyphs in the Latin Extended-A area.

2002-04-25 16:20 +0000 [r121-123]  peterlin:

	* ChangeLog: Update.

	* FreeMono.sfd: Minor corrections in the Box
	  Drawing area.

	* FreeMonoBold.sfd: Box Drawing area completed.
	  Added some glyphs in the Geometrical Shapes area.

2002-04-24 16:09 +0000 [r120]  peterlin:

	* ChangeLog: Update.

2002-04-24 16:06 +0000 [r119]  peterlin:

	* FreeMono.sfd: BoxDrawing area completed.

2002-04-23 15:22 +0000 [r118]  peterlin:

	* FreeMonoBold.sfd: Added glyphs in the General
	  Punctuation area.

2002-04-23 15:04 +0000 [r117]  peterlin:

	* FreeMono.sfd,
	  FreeMonoBold.sfd: Added glyphs to the Box
	  Drawing area.

2002-04-23 13:52 +0000 [r115-116]  peterlin:

	* ChangeLog: Update.

	* tools/WGL4.lst: Correction. Added missing
	  characters 02DC:SMALL TILDE, F001:LATIN SMALL LIGATURE FI and
	  F002:LATIN SMALL LIGATURE FL (the latter two belong to private
	  use area). Left 00A0:NO-BREAK SPACE in place, although some specs
	  don't list it. Total 653 characters on the list (WGL4 is supposed
	  to have 652 coded characters).

2002-04-22 21:27 +0000 [r114]  peterlin:

	* FreeMonoBold.sfd: Adding new glyphs to the
	  Latin Extended-B and IPA Extensions ranges.

2002-04-22 15:05 +0000 [r112-113]  peterlin:

	* ChangeLog: *** empty log message ***

	* FreeMono.sfd,
	  FreeMonoBold.sfd: Working on Latin Extended-B
	  and Greek.

2002-04-19 15:58 +0000 [r110-111]  peterlin:

	* FreeSerif.sfd: Somewhat cleaner chess figures.

	* ChangeLog: *** empty log message ***

2002-04-19 14:57 +0000 [r109]  peterlin:

	* ChangeLog: Update.

2002-04-19 14:23 +0000 [r108]  peterlin:

	* tools/MES-2.txt, tools/MES-2.lst:
	  Corrected list (it is not 203C-203E, it is 203C and 203E).

2002-04-19 09:24 +0000 [r107]  peterlin:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSans.sfd,
	  FreeSansBold.sfd,
	  FreeSansOblique.sfd,
	  FreeSansBoldOblique.sfd,
	  FreeMono.sfd,
	  FreeSerif.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeSerifBold.sfd,
	  FreeMonoBoldOblique.sfd: Changed "Family Name"
	  from Free to FreeSerif, FreeSans and FreeMono, as appropriate.
	  Changed Font Modifiers from MonoBold etc. to Bold, Italic,
	  Oblique, BoldOblique and BoldItalic.

2002-04-18 09:38 +0000 [r106]  peterlin:

	* FreeMono.sfd,
	  FreeMonoBold.sfd,
	  FreeMonoOblique.sfd,
	  FreeMonoBoldOblique.sfd: Corrected metrics;
	  now all character widths are set to 600.

2002-04-17 18:49 +0000 [r104-105]  peterlin:

	* ChangeLog: Added forgotten info on box drawing
	  characters in FreeSerif.

	* ChangeLog: *** empty log message ***

2002-04-17 17:37 +0000 [r103]  peterlin:

	* FreeSerif.sfd: Box drawing characters
	  corrected. Now they extend both ascender and descender height.

2002-04-17 15:45 +0000 [r102]  peterlin:

	* FreeMonoBold.sfd: Continued working on Greek.

2002-04-17 14:53 +0000 [r101]  peterlin:

	* FreeMonoBold.sfd: Added some more Box Drawing
	  characters.

2002-04-17 11:56 +0000 [r100]  peterlin:

	* FreeMonoBold.sfd: Added some Box Drawing
	  characters.

2002-04-16 15:36 +0000 [r99]  peterlin:

	* ChangeLog: *** empty log message ***

2002-04-16 15:30 +0000 [r98]  peterlin:

	* FreeMono.sfd: Added a couple of characters in
	  the Miscellaneous Symbols area.

2002-04-16 14:28 +0000 [r97]  peterlin:

	* FreeMonoBoldOblique.sfd: Added superscripts,
	  subscripts, vulgar fractions and a couple other characters needed
	  to make font MES-1 compliant.

2002-04-16 13:58 +0000 [r96]  peterlin:

	* FreeMonoBold.sfd: Added some characters,
	  mostly in the Miscellaneous Symbols area. FreeMonoBold now
	  complies with MES-1.

2002-04-16 10:16 +0000 [r95]  peterlin:

	* ChangeLog: Update.

2002-04-16 10:13 +0000 [r94]  peterlin:

	* FreeMonoBold.sfd: Started harmonizing Greek
	  letters with Latin and Cyrillic.

2002-04-16 09:28 +0000 [r93]  peterlin:

	* FreeMonoBold.sfd: Added subscripts,
	  superscripts and vulgar fractions.

2002-04-15 13:42 +0000 [r92]  peterlin:

	* ChangeLog: Update

2002-04-12 15:59 +0000 [r91]  peterlin:

	* AUTHORS, CREDITS: Added Beyene,
	  Kudlek, Kummer and Metzinger from the Theoretical Foundations of
	  Computer Science, University of Hamburg, the authors of the set
	  of Ethiopic metafonts.

2002-04-12 15:42 +0000 [r90]  peterlin:

	* ChangeLog: Update.

2002-04-12 15:38 +0000 [r89]  peterlin:

	* FreeSerif.sfd: Added Ethiopian glyphs,
	  converted from the Metafont sources from TGI, Universität Hamburg
	  (authors Berhanu Beyene, Prof. Dr. Manfred Kudlek, Olaf Kummer,
	  and Jochen Metzinger) using Szabo's TeXtrace and retouched using
	  PfaEdit. Ethiopian metafonts are released under GNU GPL,
	  http://www.informatik.uni-hamburg.de/TGI/mitarbeiter/wimis/kummer/ethiop_eng.html

2002-04-12 15:30 +0000 [r88]  peterlin:

	* FreeMonoBold.sfd: Added 40 characters, mostly
	  in the Latin Extended-B and IPA Extensions areas.

2002-04-11 19:56 +0000 [r86-87]  peterlin:

	* ChangeLog: *** empty log message ***

	* FreeMono.sfd: Added a handful of characters in
	  the Latin Extended-B, Currency Symbols and Miscellaneous Symbols
	  areas.

2002-04-11 09:57 +0000 [r85]  peterlin:

	* FreeMono.sfd: Added three glyphs to the IPA
	  Extensions area.

2002-04-09 07:12 +0000 [r83-84]  peterlin:

	* ChangeLog: *** empty log message ***

	* FreeMono.sfd: Corrected accent positioning in
	  the Extended Greek area; adding a couple of glyphs here and
	  there. 20 characters short of MES-2 conformance.

2002-04-08 16:57 +0000 [r82]  peterlin:

	* AUTHORS, CREDITS: Added Jeroen
	  Hellingman and Thomas Ridgeway, the authors of Malayalam, Oriya
	  and Tamil glyphs sets.

2002-04-08 16:39 +0000 [r81]  peterlin:

	* ChangeLog: *** empty log message ***

2002-04-08 16:33 +0000 [r80]  peterlin:

	* FreeMono.sfd: Added some arrows and Greek
	  politoniko.

2002-04-05 15:04 +0000 [r79]  peterlin:

	* FreeMono.sfd,
	  FreeMonoOblique.sfd: Modern Non-Russian
	  Cyrillic mostly completed.

2002-04-05 10:37 +0000 [r78]  peterlin:

	* FreeSerif.sfd: Added Thomas Ridgeway's Tamil
	  fonts, partly edited.

2002-04-04 16:15 +0000 [r77]  peterlin:

	* ChangeLog: Update.

2002-04-04 16:12 +0000 [r76]  peterlin:

	* FreeMonoBold.sfd: Started working on the
	  non-Russian Cyrillic glyphs. Corrected Serbian letters dje, lje,
	  nje and tshe.

2002-04-04 10:44 +0000 [r75]  peterlin:

	* FreeMono.sfd: Minor modifications in the
	  non-Russian Cyrillic area.

2002-04-04 10:35 +0000 [r74]  peterlin:

	* FreeMono.sfd,
	  FreeMonoOblique.sfd: Armenian capital
	  characters matching in case Latin, Cyrillic and Greek.

2002-04-04 09:57 +0000 [r73]  peterlin:

	* FreeMonoOblique.sfd: Serbian Cyrillic glyphs
	  synchronized with FreeMono.

2002-04-04 09:36 +0000 [r71-72]  peterlin:

	* ChangeLog: Update.

	* FreeMono.sfd: Serbian cyrillic characters dje
	  and tshe corrected.

2002-04-03 16:34 +0000 [r70]  peterlin:

	* ChangeLog: *** empty log message ***

2002-04-03 16:29 +0000 [r69]  peterlin:

	* FreeMono.sfd: Armenian characters added.

2002-03-28 15:32 +0000 [r67-68]  peterlin:

	* ChangeLog: Update.

	* FreeMono.sfd: Added some mathematical symbols.

2002-03-26 21:00 +0000 [r66]  peterlin:

	* ChangeLog: Update.

2002-03-26 20:56 +0000 [r65]  peterlin:

	* FreeSans.sfd: Added H.S. Pannu's Gurmukhi. It
	  actually fits in here much better than it fits FreeSerif. I'll
	  have to look for a Gurmukhi font with modulated stroke for
	  FreeSerif.

2002-03-26 20:45 +0000 [r64]  peterlin:

	* FreeSerif.sfd: A more stylish barred H.

2002-03-26 20:35 +0000 [r63]  peterlin:

	* ChangeLog: Update.

2002-03-26 20:29 +0000 [r62]  peterlin:

	* FreeMono.sfd: Minor changes in Greek; added
	  some mathematical symbols.

2002-03-26 16:20 +0000 [r61]  peterlin:

	* FreeMono.sfd: Completed vulgar fractions.

2002-03-26 16:00 +0000 [r60]  peterlin:

	* FreeSerif.sfd: Added dotted Hebrew letters.

2002-03-26 14:40 +0000 [r58-59]  peterlin:

	* FreeSerifItalic.sfd: Took Hebrew characters
	  from FreeSerif and slanted them for 15.5 degrees.

	* FreeSerif.sfd: Telugu letters optimized
	  somehow. Still too small, though.

2002-03-26 14:15 +0000 [r57]  peterlin:

	* AUTHORS, CREDITS: Corrected Unicode
	  ranges for Gurmukhi and Gujarati.

2002-03-26 09:58 +0000 [r55-56]  peterlin:

	* ChangeLog: Update.

	* FreeMonoBold.sfd: Added characters to the
	  Latin Extended-B and IPA Extensions area.

2002-03-25 20:00 +0000 [r53-54]  peterlin:

	* ChangeLog: Update.

	* FreeMonoBold.sfd: Started adding Latin
	  Extended-B and IPA Extensions.

2002-03-25 19:28 +0000 [r51-52]  peterlin:

	* ChangeLog: Update.

	* FreeMono.sfd: Minor cosmetic changes; cleaning
	  up Greek (removing redundant control points), added some
	  non-European Cyrillic glyphs as a test.

2002-03-22 11:31 +0000 [r50]  peterlin:

	* FreeMono.sfd: Some minor modifications;
	  letters in Latin Extended-B area "welded" together.

2002-03-20 20:47 +0000 [r49]  peterlin:

	* FreeMono.sfd,
	  FreeMonoOblique.sfd: Numerous glyphs added in
	  the Latin Extended-B, IPA Extensions and Number Forms areas.
	  MES-1 compliant!

2002-03-20 12:46 +0000 [r48]  peterlin:

	* ChangeLog: Update.

2002-03-19 14:22 +0000 [r47]  peterlin:

	* ChangeLog: Update.

2002-03-19 13:12 +0000 [r46]  peterlin:

	* FreeMonoOblique.sfd: Added characters in Latin
	  Extended-B and IPA Extensions area. Now mostly synchronized with
	  FreeMono.

2002-03-18 17:56 +0000 [r45]  peterlin:

	* FreeMono.sfd: Added some more IPA characters.

2002-03-18 14:33 +0000 [r44]  peterlin:

	* ChangeLog: Update.

2002-03-18 12:21 +0000 [r43]  peterlin:

	* FreeMono.sfd: Added some more characters, in
	  particular in the Latin Extended-B and IPA Extensions areas, to a
	  total of 1424 chars.

2002-03-15 19:08 +0000 [r41-42]  peterlin:

	* FreeMono.sfd: Added a dozen or two new
	  characters, in particular in the IPA Extensions area.

	* ChangeLog: Update.

2002-03-15 13:57 +0000 [r40]  peterlin:

	* ChangeLog: Update.

2002-03-14 09:15 +0000 [r38-39]  peterlin:

	* ChangeLog: Update.

	* FreeMono.sfd: Added three smiley characters.

2002-03-10 18:16 +0000 [r37]  peterlin:

	* AUTHORS, ChangeLog,
	  CREDITS: Due credit to the original author of
	  Gurmukhi font, Hardip Singh Pannu.

2002-03-10 18:07 +0000 [r36]  peterlin:

	* FreeSerif.sfd: Anshuman Pandey has only
	  converted Gurmukhi from TrueType to Metafont; the original author
	  of Gurkmukhi font is Hardip Singh Pannu
	  <http://members.aol.com/hspannu/punjabi.html>. Got the permission
	  from him to include the Gurmukhi glyph set.

2002-03-08 12:17 +0000 [r34-35]  peterlin:

	* ChangeLog: Update

	* FreeSerif.sfd: Added some more glyphs in the
	  Mathematical Symbols area.

2002-03-06 20:48 +0000 [r32-33]  peterlin:

	* ChangeLog: Update.

	* FreeSerif.sfd: Basic Gurmukhi (Unicode range
	  without ligatures). I believe the author is Anshuman Pandey
	  <<EMAIL>>, found in CTAN
	  <http://www.tex.ac.uk/tex-archive/language/gurmukhi/pandey/>.

2002-03-06 20:13 +0000 [r31]  peterlin:

	* ChangeLog: Update.

2002-03-06 18:07 +0000 [r30]  peterlin:

	* FreeMono.sfd: Silly me. Glyphs in Box Drawing
	  and Block Elements areas extend through ascender *and descender*
	  width. Corrected.

2002-03-06 15:10 +0000 [r29]  peterlin:

	* FreeMono.sfd,
	  FreeMonoOblique.sfd: Added musical glyphs in
	  the Miscellaneous Symbols area, linked "non-breaking" space to
	  space, "soft hyphen" to hyphen-minus. Now we are 4 glyphs short
	  of MES-1 conformance.

2002-03-05 19:50 +0000 [r28]  peterlin:

	* ChangeLog: Update.

2002-03-05 19:47 +0000 [r27]  peterlin:

	* tools/WGL4.lst (added): Microsoft Windows Glyph
	  List 4.0 (WGL4). I seem to have lost 2 characters - there ought
	  to be 652, and I have only 650.

2002-03-05 19:20 +0000 [r25-26]  peterlin:

	* ChangeLog: Update.

	* tools/LigatureList.pl (added): A Perl script,
	  which lists the GSUB list (ligature list) of a OpenType font.
	  Requires George Williams' showttf utility, available on
	  <http://bibliofile.mc.duke.edu/gww/FreeWare/MyToys.html>.

2002-03-05 19:12 +0000 [r24]  peterlin:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerifBold.sfd: An auxilliary substitute
	  for Hebrew glyphs added.

2002-03-04 17:38 +0000 [r23]  peterlin:

	* ChangeLog: Update

2002-03-04 17:35 +0000 [r22]  peterlin:

	* FreeSerif.sfd: Added some more glyphs in the
	  Mathematical Operators area.

2002-03-04 15:24 +0000 [r21]  peterlin:

	* AUTHORS, CREDITS: Added Frans
	  Velthuis <<EMAIL>> and Anshuman Pandey
	  <<EMAIL>>.

2002-03-04 14:54 +0000 [r20]  peterlin:

	* ChangeLog: Update

2002-03-04 14:44 +0000 [r19]  peterlin:

	* FreeSerif.sfd: Fragments of Harsh Kumar's
	  Devanagari font substituted by Frans Velthuis' and Anshuman
	  Pandey's devanagari metafont, converted to PFB by Peter Szabo's
	  TeXtrace. Right now only Unicode part without ligatures is made
	  (though they are present in original font).

2002-02-28 19:14 +0000 [r18]  peterlin:

	* ChangeLog: Update ChangeLog.

2002-02-28 19:05 +0000 [r17]  peterlin:

	* FreeSerif.sfd: Added some more glyphs to the
	  Miscellaneous Symbols page (0x26).

2002-02-28 18:33 +0000 [r16]  peterlin:

	* AUTHORS, CREDITS: Added Prasad A.
	  Chodavarapu, the author of Tikkana, a Telugu font available under
	  the GNU General Public License.

2002-02-28 18:00 +0000 [r15]  peterlin:

	* FreeSerif.sfd: Added some Telugu glyphs to
	  page 0x0C, courtesy Prasad A. Chodavarapu
	  <http://chaitanya.bhaavana.net/fonts/>

2002-02-28 16:49 +0000 [r14]  peterlin:

	* AUTHORS, CREDITS: Prof. Venkatesh
	  (Venky) Hariharan supplied me with an email address for Harsh
	  Kumar.

2002-02-28 14:51 +0000 [r13]  peterlin:

	* ChangeLog (added): A log of changes to project
	  files.

2002-02-26 18:38 +0000 [r12]  peterlin:

	* AUTHORS, CREDITS: Mohamed Ishan's
	  email address corrected to his preferred one.

2002-02-25 19:26 +0000 [r11]  peterlin:

	* FreeMono.sfd: Added some more glyphs, in
	  particular in the Mathematical Operators section. Changed
	  FamilyName to Free, FontName to FreeMono, and Full name to "Free
	  Monospaced".

2002-02-25 12:43 +0000 [r10]  peterlin:

	* FreeSerif.sfd: Added a couple of glyphs in
	  Mathematics Operators.

2002-02-20 21:08 +0000 [r9]  peterlin:

	* AUTHORS, CREDITS: New coauthors
	  added: Mohamed Ishan, Harsh Kumar and Sushant Kumar Dash.

2002-02-20 20:21 +0000 [r8]  peterlin:

	* FreeSerifItalic.sfd,
	  FreeSerifBoldItalic.sfd,
	  FreeSerif.sfd,
	  FreeSerifBold.sfd: Glyphs originating from the
	  old Omega system removed.

2002-02-20 20:06 +0000 [r7]  peterlin:

	* FreeSerifItalic.sfd (added),
	  FreeSerifBoldItalic.sfd (added),
	  FreeSerif.sfd (added),
	  FreeSerifBold.sfd (added): Proportional ISO
	  10646 fonts with modulated stroke.

2002-02-20 18:46 +0000 [r6]  peterlin:

	* FreeSans.sfd: Added an incomplete set of Oriya
	  characters, courtesy Sushant Dash <<EMAIL>>.

2002-02-20 16:37 +0000 [r5]  peterlin:

	* tools/MES-3B.lst (added), tools
	  (added), tools/MES-1.txt (added),
	  tools/MES-2.txt (added),
	  tools/CheckConformance.pl (added),
	  tools/MES-1.lst (added),
	  tools/MES-2.lst (added),
	  tools/MES-3B.txt (added),
	  tools/mes-list-expand.pl (added): Tools for
	  checking conformance with coded character sets.

2002-02-20 13:52 +0000 [r3-4]  peterlin:

	* AUTHORS (added), CREDITS (added),
	  README (added): Free UCS Scalable Fonts

	* FreeSans.sfd (added),
	  FreeSansBold.sfd (added),
	  FreeSansOblique.sfd (added),
	  FreeSansBoldOblique.sfd (added): Proportional
	  ISO 10646 fonts with uniform stroke.

2002-02-20 13:46 +0000 [r2]  peterlin:

	* sfd (added), trunk/freefont (added),
	  FreeMono.sfd (added),
	  FreeMonoBold.sfd (added),
	  FreeMonoOblique.sfd (added),
	  FreeMonoBoldOblique.sfd (added): Monospaced
	  ISO 10646 fonts.

