<?php
/**
 * Facebook Service
 * 
 * This class provides a wrapper around the Facebook SDK for PHP.
 */
class FacebookService {
    private $fb;
    private $appId;
    private $appSecret;
    private $redirectUri;
    private $graphVersion = 'v18.0';
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->appId = FB_APP_ID;
        $this->appSecret = FB_APP_SECRET;
        $this->redirectUri = FB_REDIRECT_URI;
        
        // Check if Facebook SDK is available
        if (file_exists(APPROOT . '/vendor/autoload.php')) {
            require_once APPROOT . '/vendor/autoload.php';
            
            try {
                // Initialize Facebook SDK
                $this->fb = new \Facebook\Facebook([
                    'app_id' => $this->appId,
                    'app_secret' => $this->appSecret,
                    'default_graph_version' => $this->graphVersion,
                ]);
                
                error_log('FacebookService: Successfully initialized Facebook SDK');
            } catch (Exception $e) {
                error_log('FacebookService: Error initializing Facebook SDK: ' . $e->getMessage());
                $this->fb = null;
            }
        } else {
            error_log('FacebookService: Facebook SDK not found at ' . APPROOT . '/vendor/autoload.php');
        }
        
        // Log Facebook configuration
        if (empty($this->appId) || empty($this->appSecret)) {
            error_log('FacebookService: Facebook App ID or App Secret is not configured');
        }
    }
    
    /**
     * Check if Facebook SDK is available
     * 
     * @return bool
     */
    public function isSdkAvailable() {
        return isset($this->fb);
    }
    
    /**
     * Get login URL
     * 
     * @param string $state CSRF state token
     * @return string
     */
    public function getLoginUrl($state) {
        if (!$this->isSdkAvailable()) {
            // Fallback to manual URL construction
            return "https://www.facebook.com/{$this->graphVersion}/dialog/oauth?client_id={$this->appId}&redirect_uri={$this->redirectUri}&state={$state}&scope=email,public_profile";
        }
        
        $helper = $this->fb->getRedirectLoginHelper();
        return $helper->getLoginUrl($this->redirectUri, ['email', 'public_profile'], $state);
    }
    
    /**
     * Get access token from code
     * 
     * @param string $code Authorization code
     * @return string|null
     */
    public function getAccessToken($code) {
        if (!$this->isSdkAvailable()) {
            // Fallback to manual token retrieval
            $tokenUrl = "https://graph.facebook.com/{$this->graphVersion}/oauth/access_token";
            $tokenParams = [
                'client_id' => $this->appId,
                'client_secret' => $this->appSecret,
                'redirect_uri' => $this->redirectUri,
                'code' => $code
            ];
            
            $ch = curl_init($tokenUrl . '?' . http_build_query($tokenParams));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
            $tokenResponse = curl_exec($ch);
            
            if (curl_errno($ch)) {
                error_log('cURL error: ' . curl_error($ch));
                return null;
            }
            
            curl_close($ch);
            
            $tokenData = json_decode($tokenResponse, true);
            
            if (!isset($tokenData['access_token'])) {
                error_log('Failed to get access token: ' . ($tokenData['error']['message'] ?? 'Unknown error'));
                return null;
            }
            
            return $tokenData['access_token'];
        }
        
        try {
            $helper = $this->fb->getRedirectLoginHelper();
            return $helper->getAccessToken();
        } catch (\Facebook\Exceptions\FacebookResponseException $e) {
            error_log('Graph returned an error: ' . $e->getMessage());
            return null;
        } catch (\Facebook\Exceptions\FacebookSDKException $e) {
            error_log('Facebook SDK returned an error: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get user data from access token
     * 
     * @param string $accessToken Access token
     * @return array|null
     */
    public function getUserData($accessToken) {
        error_log('FacebookService::getUserData - Getting user data with token: ' . substr($accessToken, 0, 10) . '...');
        
        if (!$this->isSdkAvailable()) {
            error_log('FacebookService::getUserData - SDK not available, using manual method');
            // Fallback to manual user data retrieval
            $graphUrl = "https://graph.facebook.com/{$this->graphVersion}/me";
            $graphParams = [
                'fields' => 'id,name,email,picture.width(500).height(500).type(large)',
                'access_token' => $accessToken
            ];
            
            $fullUrl = $graphUrl . '?' . http_build_query($graphParams);
            error_log('FacebookService::getUserData - Requesting: ' . str_replace($accessToken, '[TOKEN]', $fullUrl));
            
            $ch = curl_init($fullUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            $graphResponse = curl_exec($ch);
            
            if (curl_errno($ch)) {
                error_log('FacebookService::getUserData - cURL error: ' . curl_error($ch));
                curl_close($ch);
                return null;
            }
            
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode != 200) {
                error_log('FacebookService::getUserData - HTTP error: ' . $httpCode . ', Response: ' . $graphResponse);
                return null;
            }
            
            $userData = json_decode($graphResponse, true);
            
            if (!isset($userData['id'])) {
                error_log('FacebookService::getUserData - Failed to get user data: ' . ($userData['error']['message'] ?? 'Unknown error') . ', Response: ' . $graphResponse);
                return null;
            }
            
            error_log('FacebookService::getUserData - Successfully retrieved user data: ' . json_encode(array_keys($userData)));
            return $userData;
        }
        
        try {
            error_log('FacebookService::getUserData - Using Facebook SDK');
            $response = $this->fb->get('/me?fields=id,name,email,picture.width(500).height(500).type(large)', $accessToken);
            $userData = $response->getGraphUser()->asArray();
            error_log('FacebookService::getUserData - Successfully retrieved user data: ' . json_encode(array_keys($userData)));
            return $userData;
        } catch (\Facebook\Exceptions\FacebookResponseException $e) {
            error_log('FacebookService::getUserData - Graph returned an error: ' . $e->getMessage());
            return null;
        } catch (\Facebook\Exceptions\FacebookSDKException $e) {
            error_log('FacebookService::getUserData - Facebook SDK returned an error: ' . $e->getMessage());
            return null;
        } catch (Exception $e) {
            error_log('FacebookService::getUserData - Unexpected error: ' . $e->getMessage());
            return null;
        }
    }
}