<?php
/**
 * Facebook Session Manager
 * 
 * This class handles Facebook session management to prevent conflicts
 * between multiple Facebook apps or features.
 */
class FacebookSessionManager {
    /**
     * Clear all Facebook-related sessions
     * 
     * @return void
     */
    public static function clearFacebookSessions() {
        // Make sure session is started
        if (session_status() !== PHP_SESSION_ACTIVE) {
            session_start();
        }
        
        // Clear our custom FB sessions
        unset($_SESSION['fb_access_token']);
        unset($_SESSION['fb_user_id']);
        unset($_SESSION['fb_state']);
        unset($_SESSION['fb_token_expires']);
        
        // Clear SocialController sessions
        unset($_SESSION['facebook_share_state']);
        unset($_SESSION['facebook_share_csrf']);
        
        // Clear any SDK-created sessions
        foreach ($_SESSION as $key => $value) {
            if (strpos($key, 'fb_') === 0 || strpos($key, 'FBRLH_') === 0 || strpos($key, 'facebook_') === 0) {
                unset($_SESSION[$key]);
            }
        }
        
        // Force session write
        session_write_close();
        session_start();
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('FacebookSessionManager: Cleared all Facebook sessions');
            error_log('FacebookSessionManager: Session ID after clearing: ' . session_id());
        }
    }
    
    /**
     * Check if there's an error in the Facebook response
     * 
     * @return bool
     */
    public static function hasError() {
        return isset($_GET['error']) || isset($_GET['error_code']);
    }
    
    /**
     * Get the error message from the Facebook response
     * 
     * @return string|null
     */
    public static function getErrorMessage() {
        if (isset($_GET['error_description'])) {
            return $_GET['error_description'];
        } elseif (isset($_GET['error_message'])) {
            return $_GET['error_message'];
        } elseif (isset($_GET['error'])) {
            return $_GET['error'];
        }
        
        return null;
    }
    
    /**
     * Check if the user denied the request
     * 
     * @return bool
     */
    public static function userDeniedRequest() {
        return isset($_GET['error']) && $_GET['error'] === 'access_denied';
    }
    
    /**
     * Store Facebook access token in session
     * 
     * @param string $token Access token
     * @param int $expires Expiration time
     * @return void
     */
    public static function storeAccessToken($token, $expires = null) {
        $_SESSION['fb_access_token'] = $token;
        
        if ($expires) {
            $_SESSION['fb_token_expires'] = time() + $expires;
        }
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('FacebookSessionManager: Stored access token in session');
        }
    }
    
    /**
     * Get Facebook access token from session
     * 
     * @return string|null
     */
    public static function getAccessToken() {
        if (isset($_SESSION['fb_access_token'])) {
            // Check if token is expired
            if (isset($_SESSION['fb_token_expires']) && $_SESSION['fb_token_expires'] < time()) {
                self::clearFacebookSessions();
                return null;
            }
            
            return $_SESSION['fb_access_token'];
        }
        
        return null;
    }
    
    /**
     * Store Facebook state in session
     * 
     * @param string $state State parameter
     * @return void
     */
    public static function storeState($state) {
        // Make sure session is started
        if (session_status() !== PHP_SESSION_ACTIVE) {
            session_start();
        }
        
        // Store state in both session variables
        $_SESSION['fb_state'] = $state;
        $_SESSION['facebook_share_state'] = $state;
        
        // Force session write
        session_write_close();
        session_start();
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('FacebookSessionManager: Stored state in session: ' . $state);
            error_log('FacebookSessionManager: Session ID: ' . session_id());
            error_log('FacebookSessionManager: Verification - fb_state: ' . ($_SESSION['fb_state'] ?? 'not set'));
            error_log('FacebookSessionManager: Verification - facebook_share_state: ' . ($_SESSION['facebook_share_state'] ?? 'not set'));
        }
    }
    
    /**
     * Get Facebook state from session
     * 
     * @return string|null
     */
    public static function getState() {
        return $_SESSION['fb_state'] ?? null;
    }
    
    /**
     * Verify Facebook state
     * 
     * @param string $state State parameter from callback
     * @return bool
     */
    public static function verifyState($state) {
        $storedState = self::getState();
        
        if (!$storedState) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
            }
            return false;
        }
        
        $result = $state === $storedState;
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('FacebookSessionManager: State verification ' . ($result ? 'succeeded' : 'failed'));
            error_log('FacebookSessionManager: Stored state: ' . $storedState);
            error_log('FacebookSessionManager: Received state: ' . $state);
        }
        
        return $result;
    }
}