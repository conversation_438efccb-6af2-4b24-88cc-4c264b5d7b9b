# 🚀 Portable Beta PDF Generator - Setup Instructions

## 📦 What You Get

This is a **completely portable** beta PDF generator that can be placed anywhere:
- ✅ Any subdomain (`tools.yoursite.com`)
- ✅ Any folder (`yoursite.com/secret-folder/`)
- ✅ Any server (completely different domain)
- ✅ Local development environment

## 🔐 Security Features

### Built-in Security Options:
1. **Password Protection** - Always enabled
2. **IP Restrictions** - Optional (restrict to your IP only)
3. **Secret URL Access** - Optional (require secret parameter)
4. **Stealth Mode** - Hides from search engines and bots
5. **Fake 404 Errors** - Unauthorized access shows "Page Not Found"

## 🚀 Quick Setup (3 Steps)

### Step 1: Upload Files
Upload these files to your chosen location:
```
your-secret-folder/
├── beta_pdf_generator.php    (main file)
└── libraries/               (optional - for better PDFs)
    └── tcpdf/
        └── tcpdf.php
```

### Step 2: Configure Security
Edit the top of `beta_pdf_generator.php`:

```php
// Change this password!
define('ADMIN_PASSWORD', 'your_new_password_here');

// Optional: Restrict to your IP only
define('ALLOWED_IPS', ['YOUR.IP.ADDRESS.HERE']);

// Optional: Require secret URL parameter
define('SECRET_ACCESS_KEY', 'mysecretkey2025');
```

### Step 3: Access Your Generator
- **Basic:** `https://yoursite.com/secret-folder/beta_pdf_generator.php`
- **With Secret:** `https://yoursite.com/secret-folder/beta_pdf_generator.php?access=mysecretkey2025`

## 🛡️ Security Configuration Examples

### Maximum Security (Recommended)
```php
define('ADMIN_PASSWORD', 'MyStr0ngP@ssw0rd2025');
define('ALLOWED_IPS', ['************']); // Your IP only
define('SECRET_ACCESS_KEY', 'beta2025secret');
define('STEALTH_MODE', true);
```
**Access:** `https://tools.yoursite.com/beta_pdf_generator.php?access=beta2025secret`

### Medium Security
```php
define('ADMIN_PASSWORD', 'MyStr0ngP@ssw0rd2025');
define('ALLOWED_IPS', []); // Allow any IP
define('SECRET_ACCESS_KEY', 'beta2025secret');
define('STEALTH_MODE', true);
```

### Basic Security
```php
define('ADMIN_PASSWORD', 'MyStr0ngP@ssw0rd2025');
define('ALLOWED_IPS', []); // Allow any IP
define('SECRET_ACCESS_KEY', ''); // No secret required
define('STEALTH_MODE', true);
```

## 📍 Deployment Options

### Option 1: Subdomain (Recommended)
1. Create subdomain: `tools.yoursite.com`
2. Point to folder containing the files
3. Access: `https://tools.yoursite.com/beta_pdf_generator.php`

### Option 2: Hidden Folder
1. Create folder: `/secret-beta-tools/`
2. Upload files to this folder
3. Access: `https://yoursite.com/secret-beta-tools/beta_pdf_generator.php`

### Option 3: Different Domain
1. Use completely different domain/server
2. Upload files anywhere
3. Access: `https://anotherdomain.com/beta_pdf_generator.php`

## 🔧 TCPDF Installation (Optional)

For professional PDF quality, install TCPDF in one of these locations:

### Same Directory:
```
your-folder/
├── beta_pdf_generator.php
└── libraries/tcpdf/tcpdf.php
```

### Or tcpdf folder:
```
your-folder/
├── beta_pdf_generator.php
└── tcpdf/tcpdf.php
```

**Download TCPDF:** https://tcpdf.org/

## 🎯 Usage

1. **Access** your secure URL
2. **Login** with your password
3. **Generate** professional PDFs for beta testers
4. **Download** and send to prospects

## 🗑️ Easy Removal

When beta testing is complete:
1. **Delete** the folder/files
2. **Remove** subdomain (if used)
3. **Done!** No database cleanup needed

## 🔍 Finding Your IP Address

To restrict access to your IP only:
1. Visit: https://whatismyipaddress.com/
2. Copy your IP address
3. Add to `ALLOWED_IPS` array

## 🚨 Security Best Practices

1. **Use strong passwords** (12+ characters, mixed case, numbers, symbols)
2. **Enable IP restrictions** if you have a static IP
3. **Use secret access keys** for additional security
4. **Choose obscure folder names** (`x7k9m` instead of `beta-tools`)
5. **Delete immediately** when beta testing is complete

## 📞 Support

If you need help with setup, the system includes:
- ✅ Automatic TCPDF detection
- ✅ Clear error messages
- ✅ Setup validation
- ✅ Security status display

The system will tell you exactly what's working and what needs attention!
