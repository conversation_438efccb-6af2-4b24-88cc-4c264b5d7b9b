<?php
/**
 * Quick Setup Script for Portable Beta PDF Generator
 * 
 * This script helps you quickly configure security settings
 * Run this ONCE, then delete it for security.
 */

// Check if beta_pdf_generator.php exists
if (!file_exists('beta_pdf_generator.php')) {
    die('Error: beta_pdf_generator.php not found in this directory.');
}

// Handle form submission
if ($_POST) {
    $password = $_POST['password'] ?? '';
    $allowedIps = $_POST['allowed_ips'] ?? '';
    $secretKey = $_POST['secret_key'] ?? '';
    $stealthMode = isset($_POST['stealth_mode']) ? 'true' : 'false';
    
    if (empty($password)) {
        $error = 'Password is required!';
    } else {
        // Read the current file
        $content = file_get_contents('beta_pdf_generator.php');
        
        // Replace configuration values
        $content = preg_replace(
            "/define\('ADMIN_PASSWORD', '[^']*'\);/",
            "define('ADMIN_PASSWORD', '" . addslashes($password) . "');",
            $content
        );
        
        // Handle IP restrictions
        if (!empty($allowedIps)) {
            $ipsArray = array_map('trim', explode(',', $allowedIps));
            $ipsString = "['" . implode("', '", array_map('addslashes', $ipsArray)) . "']";
        } else {
            $ipsString = "[]";
        }
        
        $content = preg_replace(
            "/define\('ALLOWED_IPS', \[[^\]]*\]\);/",
            "define('ALLOWED_IPS', $ipsString);",
            $content
        );
        
        $content = preg_replace(
            "/define\('SECRET_ACCESS_KEY', '[^']*'\);/",
            "define('SECRET_ACCESS_KEY', '" . addslashes($secretKey) . "');",
            $content
        );
        
        $content = preg_replace(
            "/define\('STEALTH_MODE', (true|false)\);/",
            "define('STEALTH_MODE', $stealthMode);",
            $content
        );
        
        // Write the updated file
        if (file_put_contents('beta_pdf_generator.php', $content)) {
            $success = true;
            $accessUrl = 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/beta_pdf_generator.php';
            if (!empty($secretKey)) {
                $accessUrl .= '?access=' . urlencode($secretKey);
            }
        } else {
            $error = 'Failed to update configuration file. Check file permissions.';
        }
    }
}

// Get current IP for convenience
$currentIp = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Setup - Portable Beta PDF Generator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <?php if (isset($success) && $success): ?>
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h3><i class="fas fa-check-circle"></i> Setup Complete!</h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-success">
                                <h5>Configuration Updated Successfully!</h5>
                                <p><strong>Your Access URL:</strong></p>
                                <div class="input-group mb-3">
                                    <input type="text" class="form-control" value="<?= htmlspecialchars($accessUrl) ?>" readonly>
                                    <button class="btn btn-outline-secondary" onclick="copyToClipboard(this.previousElementSibling.value)">
                                        <i class="fas fa-copy"></i> Copy
                                    </button>
                                </div>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> Important Security Steps:</h6>
                                <ol>
                                    <li><strong>Delete this setup file:</strong> <code>QUICK_SETUP.php</code></li>
                                    <li><strong>Test your access URL</strong> above</li>
                                    <li><strong>Bookmark the URL</strong> for future use</li>
                                </ol>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <a href="<?= htmlspecialchars($accessUrl) ?>" class="btn btn-primary" target="_blank">
                                    <i class="fas fa-external-link-alt"></i> Test Access
                                </a>
                                <button class="btn btn-danger" onclick="deleteSetupFile()">
                                    <i class="fas fa-trash"></i> Delete This Setup File
                                </button>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h3><i class="fas fa-cog"></i> Quick Setup - Portable Beta PDF Generator</h3>
                        </div>
                        <div class="card-body">
                            <?php if (isset($error)): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
                                </div>
                            <?php endif; ?>
                            
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Security Configuration</h6>
                                <p>Configure your security settings below. You can always change these later by editing the PHP file directly.</p>
                            </div>
                            
                            <form method="POST">
                                <div class="mb-3">
                                    <label for="password" class="form-label">Admin Password <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="password" name="password" required 
                                           placeholder="Enter a strong password">
                                    <div class="form-text">Use a strong password with 12+ characters, mixed case, numbers, and symbols.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="allowed_ips" class="form-label">Allowed IP Addresses (Optional)</label>
                                    <input type="text" class="form-control" id="allowed_ips" name="allowed_ips" 
                                           placeholder="<?= htmlspecialchars($currentIp) ?>" value="<?= htmlspecialchars($currentIp) ?>">
                                    <div class="form-text">
                                        Leave blank to allow any IP, or enter comma-separated IPs to restrict access. 
                                        Your current IP is: <strong><?= htmlspecialchars($currentIp) ?></strong>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="secret_key" class="form-label">Secret Access Key (Optional)</label>
                                    <input type="text" class="form-control" id="secret_key" name="secret_key" 
                                           placeholder="e.g., beta2025secret">
                                    <div class="form-text">If set, you'll need to add ?access=yourkey to the URL for access.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="stealth_mode" name="stealth_mode" checked>
                                        <label class="form-check-label" for="stealth_mode">
                                            Enable Stealth Mode (Recommended)
                                        </label>
                                    </div>
                                    <div class="form-text">Hides the system from search engines and bots.</div>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Configure & Setup
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="generateSecretKey()">
                                        <i class="fas fa-random"></i> Generate Secret Key
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script>
    function generateSecretKey() {
        const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let result = 'beta2025';
        for (let i = 0; i < 8; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        document.getElementById('secret_key').value = result;
    }
    
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            alert('URL copied to clipboard!');
        });
    }
    
    function deleteSetupFile() {
        if (confirm('Are you sure you want to delete this setup file? This action cannot be undone.')) {
            fetch('QUICK_SETUP.php', {
                method: 'DELETE'
            }).then(() => {
                alert('Setup file deleted successfully!');
                window.location.reload();
            }).catch(() => {
                alert('Could not delete setup file automatically. Please delete QUICK_SETUP.php manually.');
            });
        }
    }
    </script>
</body>
</html>
